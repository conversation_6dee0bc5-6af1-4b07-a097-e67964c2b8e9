<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modificar el enum para agregar el valor 'cancelled'
        DB::statement("ALTER TABLE tasks MODIFY COLUMN status ENUM('started', 'pending', 'ended', 'stopped', 'cancelled') DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revertir el enum a su estado original
        DB::statement("ALTER TABLE tasks MODIFY COLUMN status ENUM('started', 'pending', 'ended', 'stopped') DEFAULT 'pending'");
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_transactions', function (Blueprint $table) {
            $table->foreignId('order_id')->nullable()->change();
            $table->foreignId('task_id')->nullable()->constrained('tasks')->nullOnDelete()->after('order_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_transactions', function (Blueprint $table) {
            $table->foreignId('order_id')->nullable(false)->change();
            $table->dropForeign(['task_id']);
            $table->dropColumn('task_id');
        });
    }
};

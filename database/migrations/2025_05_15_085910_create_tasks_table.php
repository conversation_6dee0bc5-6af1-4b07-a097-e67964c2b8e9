<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
            $table->foreignId('provider_id')->constrained('users')->cascadeOnDelete();
            $table->foreignId('job_application_id')->constrained('job_applications')->cascadeOnDelete();
            $table->foreignId('service_id')->nullable()->constrained('services')->onDelete('set null');
            $table->foreignId('location_id')->nullable()->constrained('user_locations')->onDelete('set null');
            $table->integer('d_id');
            $table->date('execution_date')->default(now());
            $table->time('start_time')->default(now());
            $table->time('end_time')->nullable();
            $table->integer('duration_minutes')->default(0);
            $table->enum('calculation_type', ['fixed', 'hourly'])->default('fixed');
            $table->decimal('fixed_amount', 10, 2)->nullable();
            $table->enum('provider_payment_method', ['fixed', 'hourly'])->nullable();
            $table->decimal('provider_hourly_cost', 10, 2)->nullable();
            $table->decimal('provider_fixed_cost', 10, 2)->nullable();
            $table->decimal('system_hourly_cost', 10, 2)->nullable();
            $table->boolean('include_transportation')->default(false);
            $table->decimal('system_transportation_cost', 10, 2)->nullable();
            $table->decimal('provider_transportation_cost', 10, 2)->nullable();
            $table->enum('status', ['started', 'pending',  'ended', 'stopped'])->default('pending');
            $table->text('notes')->nullable();
            $table->boolean('is_financially_closed_by_user')->default(true);
            $table->boolean('is_financially_closed_by_provider')->default(false);
            $table->boolean('financially_closed_by_user')->default(true);
            $table->boolean('financially_closed_by_provider')->default(false);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};

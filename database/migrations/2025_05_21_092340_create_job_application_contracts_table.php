<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_application_contracts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_application_id')->constrained('job_applications')->cascadeOnDelete();
            // $table->enum('party_type', ['user,provider']);
            $table->decimal('fixed_amount', 10, 2)->nullable();
            $table->enum('provider_calculation_type', ['fixed', 'hourly'])->default('fixed');
            $table->decimal('provider_fixed_cost', 10, 2)->nullable();
            $table->decimal('provider_hourly_cost', 10, 2)->nullable();
            $table->date('month')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_application_contracts');
    }
};

/**
 * Financial Reports - Select2 Initialization
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Financial Reports script loaded');

    // Initialize Select2 for searchable selects
    if ($.fn.select2) {
        try {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%',
                allowClear: true,
                placeholder: function() {
                    return $(this).data('placeholder') || $(this).find('option:first').text();
                },
                language: {
                    noResults: function() {
                        return "لا توجد نتائج";
                    },
                    searching: function() {
                        return "جاري البحث...";
                    }
                }
            });

            console.log('Select2 initialized successfully in financial reports');
        } catch (e) {
            console.error('Error initializing Select2 in financial reports:', e);
        }
    } else {
        console.error('Select2 not found');
    }
});

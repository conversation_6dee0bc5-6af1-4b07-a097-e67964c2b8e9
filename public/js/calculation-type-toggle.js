/**
 * Calculation Type Toggle Script
 * This script handles the toggling of calculation type sections and provider payment method
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize calculation type toggle
    initCalculationTypeToggle();

    // Initialize provider payment method toggle
    initProviderPaymentMethodToggle();
});

/**
 * Initialize calculation type toggle
 */
function initCalculationTypeToggle() {
    const fixedAmountSection = document.getElementById('fixed_amount_section');
    const hourlyCostSection = document.getElementById('hourly_cost_section');
    const calculationTypeFixed = document.getElementById('calculation_type_fixed');
    const calculationTypeHourly = document.getElementById('calculation_type_hourly');

    if (calculationTypeFixed && calculationTypeHourly && fixedAmountSection && hourlyCostSection) {
        console.log('Calculation type toggle initialized');
        
        // Set initial state
        fixedAmountSection.style.display = calculationTypeFixed.checked ? 'block' : 'none';
        hourlyCostSection.style.display = calculationTypeHourly.checked ? 'block' : 'none';

        // Add event listeners
        calculationTypeFixed.addEventListener('change', function() {
            if (this.checked) {
                fixedAmountSection.style.display = 'block';
                hourlyCostSection.style.display = 'none';
                document.getElementById('provider_payment_method').value =
                    document.getElementById('provider_payment_method_fixed').value;
                console.log('Switched to fixed calculation type');
            }
        });

        calculationTypeHourly.addEventListener('change', function() {
            if (this.checked) {
                fixedAmountSection.style.display = 'none';
                hourlyCostSection.style.display = 'block';
                document.getElementById('provider_payment_method').value = 'hourly';
                console.log('Switched to hourly calculation type');
            }
        });
    } else {
        console.warn('Calculation type toggle: One or more elements not found');
        if (!calculationTypeFixed) console.warn('- Fixed calculation type radio button not found');
        if (!calculationTypeHourly) console.warn('- Hourly calculation type radio button not found');
        if (!fixedAmountSection) console.warn('- Fixed amount section not found');
        if (!hourlyCostSection) console.warn('- Hourly cost section not found');
    }
}

/**
 * Initialize provider payment method toggle
 */
function initProviderPaymentMethodToggle() {
    const providerPaymentMethodFixed = document.getElementById('provider_payment_method_fixed');
    const providerFixedCostContainer = document.getElementById('provider_fixed_cost_container');
    const providerHourlyCostContainer = document.getElementById('provider_hourly_cost_container');

    if (providerPaymentMethodFixed && providerHourlyCostContainer) {
        console.log('Provider payment method toggle initialized');
        
        // Set initial state
        const isFixed = providerPaymentMethodFixed.value === 'fixed';
        if (providerFixedCostContainer) {
            providerFixedCostContainer.style.display = isFixed ? 'block' : 'none';
        }
        providerHourlyCostContainer.style.display = isFixed ? 'none' : 'block';

        // Add event listener
        providerPaymentMethodFixed.addEventListener('change', function() {
            const isFixed = this.value === 'fixed';
            if (providerFixedCostContainer) {
                providerFixedCostContainer.style.display = isFixed ? 'block' : 'none';
            }
            providerHourlyCostContainer.style.display = isFixed ? 'none' : 'block';
            document.getElementById('provider_payment_method').value = this.value;
            console.log('Provider payment method changed to:', this.value);
        });
    } else {
        console.warn('Provider payment method toggle: One or more elements not found');
        if (!providerPaymentMethodFixed) console.warn('- Provider payment method select not found');
        if (!providerFixedCostContainer) console.warn('- Provider fixed cost container not found (this might be OK)');
        if (!providerHourlyCostContainer) console.warn('- Provider hourly cost container not found');
    }
}

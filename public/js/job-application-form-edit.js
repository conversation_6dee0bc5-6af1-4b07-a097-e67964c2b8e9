document.addEventListener('DOMContentLoaded', function() {
    // تهيئة المتغيرات
    window.scheduleIndex = 0; // استخدام متغير عالمي لتتبع مؤشر الجدول

    // طباعة محتوى الجدول عند تحميل الصفحة
    console.log('%c=== INITIAL PAGE LOAD ===', 'background: #9C27B0; color: white; font-weight: bold; padding: 3px 5px;');
    console.log('Initial schedule container content:', document.getElementById('schedule-container')?.innerHTML || 'Not found');
    console.log('Initial schedule table content:', document.getElementById('scheduleTableBody')?.innerHTML || 'Not found');

    // تنظيف الجدول من أي صفوف موجودة مسبقاً في HTML
    const initialTableBody = document.getElementById('scheduleTableBody');
    if (initialTableBody) {
        // التحقق من وجود صفوف فارغة
        const emptyRows = Array.from(initialTableBody.querySelectorAll('tr')).filter(row => {
            return !row.dataset.startTime || row.dataset.startTime.trim() === '';
        });

        if (emptyRows.length > 0) {
            console.warn(`Found ${emptyRows.length} empty rows in initial HTML, removing them...`);
            emptyRows.forEach(row => {
                console.log('Removing empty row from initial HTML:', row);
                row.remove();
            });
        }
    }

    // تنظيف الحقول المخفية الفارغة في HTML الأصلي
    setTimeout(() => {
        console.log('%c=== CLEANING INITIAL HTML CONTENT ===', 'background: #E91E63; color: white; font-weight: bold; padding: 3px 5px;');
        const scheduleContainer = document.getElementById('schedule-container');
        if (scheduleContainer) {
            // البحث عن الحقول المخفية الفارغة في HTML الأصلي
            const emptyInputs = Array.from(scheduleContainer.querySelectorAll('input[name^="schedule["][name$="[start_time]"]'))
                .filter(input => !input.value || input.value.trim() === '');

            if (emptyInputs.length > 0) {
                console.warn(`Found ${emptyInputs.length} empty inputs in initial HTML, cleaning...`);
                emptyInputs.forEach(input => {
                    const matches = input.name.match(/schedule\[(\d+)\]/);
                    if (matches && matches.length === 2) {
                        const index = matches[1];
                        const relatedInputs = scheduleContainer.querySelectorAll(`input[name^="schedule[${index}]"]`);
                        relatedInputs.forEach(relatedInput => {
                            console.log(`Removing initial hidden field: ${relatedInput.name}`);
                            relatedInput.remove();
                        });
                    }
                });

                // إعادة بناء الجدول بعد تنظيف الحقول المخفية
                cleanEmptyHiddenFields();
            } else {
                console.log('No empty inputs found in initial HTML');
            }
        }
    }, 100);

    const daySelect = document.getElementById('day_select');
    const providerSelect = document.getElementById('provider_id');
    const checkAvailabilityBtn = document.getElementById('check-availability-btn');
    const addTimeBtn = document.getElementById('add-time-btn');

    // تعطيل زر إضافة الوقت في البداية
    if (addTimeBtn) {
        addTimeBtn.disabled = true;
    }

    // إضافة مستمع الأحداث لتغيير اليوم
    if (daySelect) {
        daySelect.addEventListener('change', function() {
            const selectedDay = this.value;
            document.getElementById('selected_day').value = selectedDay;
            // لا نقوم باستدعاء checkAvailability تلقائيًا
        });
    }

    // إضافة مستمع الأحداث لتغيير مقدم الخدمة
    if (providerSelect) {
        providerSelect.addEventListener('change', function() {
            // لا نقوم باستدعاء checkAvailability تلقائيًا
        });
    }

    // دالة التحقق من التوافر
    async function checkAvailability() {
        const providerId = document.getElementById('provider_id')?.value;
        const selectedDay = document.getElementById('day_select')?.value;
        const duration = document.getElementById('duration')?.value || 2;
        const container = document.getElementById('time-slots-container');
        const jobApplicationId = getJobApplicationIdFromUrl();

        if (!providerId || !selectedDay) {
            container.innerHTML = '<div class="alert alert-warning">يرجى اختيار مقدم الخدمة واليوم</div>';
            return;
        }

        try {
            // إظهار مؤشر التحميل
            const loadingOverlay = document.querySelector('.loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.classList.remove('d-none');
            }

            // استخدام طريقة FormData بدلاً من JSON
            const formData = new FormData();
            formData.append('provider_id', providerId);
            formData.append('day', selectedDay);
            formData.append('duration', duration);

            // إضافة معرف طلب التوظيف الحالي إذا كان موجودًا
            if (jobApplicationId) {
                formData.append('job_application_id', jobApplicationId);
            }

            // إضافة CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
            formData.append('_token', csrfToken);

            // إضافة طابع زمني لمنع التخزين المؤقت
            formData.append('_t', new Date().getTime());

            console.log('Checking availability with params:', {
                provider_id: providerId,
                day: selectedDay,
                duration: duration,
                job_application_id: jobApplicationId
            });

            // جلب الأوقات المتاحة
            console.log('Sending request to check-availability endpoint...');
            const availabilityResponse = await fetch('/job-applications/check-availability', {
                method: 'POST',
                body: formData
            });

            console.log('Response status from check-availability:', availabilityResponse.status);
            const availabilityData = await availabilityResponse.json();
            console.log('Available slots response (full):', availabilityData);

            if (availabilityData.slots) {
                console.log('%c=== AVAILABLE SLOTS DETAILS ===', 'background: #4CAF50; color: white; font-weight: bold; padding: 3px 5px;');
                console.log('Total slots count:', availabilityData.slots.length);

                // فرز الأوقات إلى متاحة ومحجوزة في نفس الطلب
                const availableOnly = availabilityData.slots.filter(slot => slot.is_available === true);
                const currentJobOnly = availabilityData.slots.filter(slot => slot.is_current_job === true);

                console.log('%cالأوقات المتاحة:', 'color: green; font-weight: bold;',
                    availableOnly.length,
                    availableOnly.map(slot => `${slot.start_time}-${slot.end_time}`)
                );

                console.log('%cالأوقات المحجوزة في نفس الطلب:', 'color: gray; font-weight: bold;',
                    currentJobOnly.length,
                    currentJobOnly.map(slot => `${slot.start_time}-${slot.end_time}`)
                );

                // طباعة تفاصيل كل فترة زمنية
                console.groupCollapsed('تفاصيل جميع الفترات الزمنية');
                availabilityData.slots.forEach((slot, index) => {
                    console.log(
                        `Slot ${index + 1}: ${slot.start_time}-${slot.end_time}`,
                        slot.is_available ? 'متاح' : 'غير متاح',
                        slot.is_current_job ? '(محجوز في نفس الطلب)' : ''
                    );
                });
                console.groupEnd();
            } else {
                console.warn('No slots returned from check-availability endpoint');
            }

            // جلب الأوقات المحجوزة لنفس اليوم
            const bookedFormData = new FormData();
            bookedFormData.append('provider_id', providerId);
            bookedFormData.append('day', selectedDay);
            bookedFormData.append('duration', duration);
            bookedFormData.append('current_job_application_id', jobApplicationId || '');
            bookedFormData.append('_token', csrfToken);

            console.log('Fetching booked slots with params:', {
                provider_id: providerId,
                day: selectedDay,
                duration: duration,
                current_job_application_id: jobApplicationId || ''
            });

            console.log('Sending request to get-booked-slots endpoint...');
            const bookedResponse = await fetch('/job-applications/get-booked-slots', {
                method: 'POST',
                body: bookedFormData
            });

            console.log('Response status from get-booked-slots:', bookedResponse.status);
            const bookedData = await bookedResponse.json();
            console.log('Booked slots response (full):', bookedData);

            if (bookedData.booked_slots) {
                console.log('%c=== BOOKED SLOTS DETAILS ===', 'background: #F44336; color: white; font-weight: bold; padding: 3px 5px;');
                console.log('Total booked slots count:', bookedData.booked_slots.length);

                // فرز الأوقات المحجوزة حسب النوع
                const orderSlots = bookedData.booked_slots.filter(slot => slot.type === 'order');
                const jobSlots = bookedData.booked_slots.filter(slot => slot.type === 'job');

                console.log('%cالأوقات المحجوزة في طلبات:', 'color: #E91E63; font-weight: bold;',
                    orderSlots.length,
                    orderSlots.map(slot => `${slot.start_time}-${slot.end_time}`)
                );

                console.log('%cالأوقات المحجوزة في وظائف:', 'color: #FF9800; font-weight: bold;',
                    jobSlots.length,
                    jobSlots.map(slot => `${slot.start_time}-${slot.end_time}`)
                );

                // طباعة تفاصيل كل فترة زمنية محجوزة
                console.groupCollapsed('تفاصيل الفترات الزمنية المحجوزة');
                bookedData.booked_slots.forEach((slot, index) => {
                    console.log(
                        `Booked Slot ${index + 1}: ${slot.start_time}-${slot.end_time}`,
                        `النوع: ${slot.type === 'order' ? 'طلب' : 'وظيفة'}`,
                        `المدة: ${slot.duration} ساعة`
                    );
                });
                console.groupEnd();
            } else {
                console.warn('No booked slots returned from get-booked-slots endpoint');
            }

            // إخفاء مؤشر التحميل
            if (loadingOverlay) {
                loadingOverlay.classList.add('d-none');
            }

            if (availabilityData.success) {
                // دمج الأوقات المتاحة مع الأوقات المحجوزة
                const allSlots = availabilityData.slots || [];
                const bookedSlots = bookedData.success ? (bookedData.booked_slots || []) : [];

                console.log('Available slots (detailed):', allSlots);
                console.log('Booked slots (detailed):', bookedSlots);

                // طباعة الأوقات المتاحة بشكل مفصل
                const availableSlotsOnly = allSlots.filter(slot => slot.is_available === true);
                console.log('AVAILABLE SLOTS ONLY:', availableSlotsOnly.map(slot => `${slot.start_time} - ${slot.end_time}`));

                // طباعة الأوقات المحجوزة في نفس الطلب
                const currentJobSlotsOnly = allSlots.filter(slot => slot.is_current_job === true);
                console.log('CURRENT JOB SLOTS ONLY:', currentJobSlotsOnly.map(slot => `${slot.start_time} - ${slot.end_time}`));

                // طباعة الأوقات المحجوزة في طلبات أخرى
                console.log('BOOKED SLOTS FROM OTHER REQUESTS (not displayed):', bookedSlots.map(slot => `${slot.start_time} - ${slot.end_time}`));

                // طباعة تفاصيل الأوقات المحجوزة في طلبات أخرى
                bookedSlots.forEach(slot => {
                    console.log('Booked slot details (not displayed):', {
                        start_time: slot.start_time,
                        end_time: slot.end_time,
                        type: slot.type || 'unknown'
                    });
                });

                // لا نضيف الأوقات المحجوزة في طلبات أخرى إلى القائمة المعروضة
                console.log('Slots to render (without booked slots from other requests):', allSlots);

                // عرض الأوقات المتاحة والمحجوزة في نفس الطلب فقط
                renderTimeSlots(allSlots);
            } else {
                container.innerHTML = `<div class="alert alert-danger">${availabilityData.message || 'حدث خطأ'}</div>`;
            }
        } catch (error) {
            console.error('Error checking availability:', error);
            container.innerHTML = '<div class="alert alert-danger">حدث خطأ في الاتصال</div>';

            // إخفاء مؤشر التحميل في حالة الخطأ
            const loadingOverlay = document.querySelector('.loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.classList.add('d-none');
            }
        }
    }

    // دالة للحصول على معرف طلب التوظيف من URL
    function getJobApplicationIdFromUrl() {
        const url = window.location.pathname;
        const matches = url.match(/\/job-applications\/(\d+)\/edit/);
        return matches ? matches[1] : null;
    }

    // دالة عرض الفترات الزمنية المتاحة والمحجوزة في نفس الطلب والأوقات المحجوزة الأخرى
    function renderTimeSlots(slots) {
        console.log('renderTimeSlots called with slots:', slots);
        const container = document.getElementById('time-slots-container');

        if (!container) {
            console.error('Time slots container not found');
            return;
        }

        // فرز الأوقات إلى متاحة ومحجوزة في نفس الطلب
        const availableSlots = [];
        const currentJobSlots = [];
        const bookedSlots = [];
        const allSlots = [];

        slots.forEach(slot => {
            console.log('Processing slot:', slot);
            if (slot.is_current_job) {
                console.log('Slot is for current job');
                currentJobSlots.push(slot);
                // إضافة إلى القائمة الكاملة للترتيب
                allSlots.push({
                    start_time: slot.start_time,
                    end_time: slot.end_time,
                    is_current_job: true,
                    is_available: false,
                    is_booked: false
                });
            } else if (slot.is_booked) {
                console.log('Slot is booked in another request - not displaying');
                bookedSlots.push(slot);
                // لا نضيف الأوقات المحجوزة في طلبات أخرى إلى القائمة المعروضة
                // لأننا لا نريد عرضها في الواجهة
            } else if (slot.is_available) {
                console.log('Slot is available');
                availableSlots.push(slot);
                // إضافة إلى القائمة الكاملة للترتيب
                allSlots.push({
                    start_time: slot.start_time,
                    end_time: slot.end_time,
                    is_current_job: false,
                    is_available: true,
                    is_booked: false
                });
            } else {
                console.warn('Slot does not match any category:', slot);
            }
        });

        // ترتيب جميع السلوتات حسب وقت البداية
        allSlots.sort((a, b) => {
            const timeA = a.start_time.split(':').map(Number);
            const timeB = b.start_time.split(':').map(Number);

            // مقارنة الساعات أولاً
            if (timeA[0] !== timeB[0]) {
                return timeA[0] - timeB[0];
            }

            // إذا كانت الساعات متساوية، قارن الدقائق
            return timeA[1] - timeB[1];
        });

        // حساب إجمالي عدد الأوقات المعروضة
        const totalSlots = availableSlots.length + currentJobSlots.length + bookedSlots.length;

        // طباعة ملخص للأوقات المتاحة والمحجوزة
        console.log('%c=== FINAL SLOTS SUMMARY ===', 'background: #2196F3; color: white; font-weight: bold; padding: 3px 5px;');
        console.log('%cالأوقات المتاحة:', 'color: green; font-weight: bold;',
            availableSlots.length,
            availableSlots.map(slot => `${slot.start_time}-${slot.end_time}`)
        );
        console.log('%cالأوقات المحجوزة في نفس الطلب:', 'color: gray; font-weight: bold;',
            currentJobSlots.length,
            currentJobSlots.map(slot => `${slot.start_time}-${slot.end_time}`)
        );
        console.log('%cالأوقات المحجوزة في طلبات أخرى (غير معروضة):', 'color: #E91E63; font-weight: bold;',
            bookedSlots.length,
            bookedSlots.map(slot => `${slot.start_time}-${slot.end_time}`)
        );
        console.log('%cإجمالي الأوقات المعروضة:', 'color: blue; font-weight: bold;', allSlots.length);

        if (totalSlots === 0) {
            container.innerHTML = '<div class="alert alert-info">لا توجد أوقات متاحة</div>';
            return;
        }

        // إضافة عنوان للفترات الزمنية
        const timeSlotsContainer = document.createElement('div');
        timeSlotsContainer.className = 'time-slots-container';

        const title = document.createElement('div');
        title.className = 'time-slots-title d-flex justify-content-between align-items-center mb-2';
        title.innerHTML = `
        <h5 class="mb-0">الأوقات المتاحة</h5>
        <span class="badge bg-success">${availableSlots.length} متاح</span>
    `;

        timeSlotsContainer.appendChild(title);

        const wrapper = document.createElement('div');
        wrapper.className = 'time-slots-wrapper';

        // تنسيق الوقت
        const formatTime = (time) => {
            if (!time) return '';
            const [hours, minutes] = time.split(':');
            const hour = parseInt(hours);
            const ampm = hour >= 12 ? 'م' : 'ص';
            const formattedHour = hour % 12 || 12;
            return `${formattedHour}:${minutes} ${ampm}`;
        };

        // عرض جميع الأوقات مرتبة
        allSlots.forEach(slot => {
            const button = document.createElement('button');
            button.type = 'button';
            button.dataset.startTime = slot.start_time;
            button.dataset.endTime = slot.end_time;
            button.textContent = `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`;

            if (slot.is_current_job) {
                // الأوقات المحجوزة في نفس الطلب
                button.className = 'time-slot-btn-new unavailable stored-slot';
                button.disabled = true;
                button.title = 'هذه الفترة الزمنية موجودة بالفعل في الجدول';
            } else if (slot.is_booked) {
                // الأوقات المحجوزة في طلبات أخرى
                button.className = 'time-slot-btn-new unavailable-order';
                button.disabled = true;
                button.title = 'هذه الفترة الزمنية محجوزة في طلب آخر';
            } else {
                // الأوقات المتاحة
                button.className = 'time-slot-btn-new';
                button.onclick = () => selectTimeSlot(slot.start_time, slot.end_time);
            }

            wrapper.appendChild(button);
        });

        timeSlotsContainer.appendChild(wrapper);

        // إضافة مفتاح الألوان
        const legend = document.createElement('div');
        legend.className = 'time-slots-legend mt-3';
        legend.innerHTML = `
            <div class="d-flex flex-wrap gap-3 mt-2 justify-content-center">
                <div class="d-flex align-items-center">
                    <span class="legend-color me-1" style="background-color: #e7f5ff; border: 1px solid #4dabf7;"></span>
                    <small>متاح</small>
                </div>
                <div class="d-flex align-items-center">
                    <span class="legend-color me-1" style="background-color: #6c757d;"></span>
                    <small>محجوز في هذا الطلب</small>
                </div>
            </div>
        `;

        timeSlotsContainer.appendChild(legend);

        container.innerHTML = '';
        container.appendChild(timeSlotsContainer);
    }

    // دالة اختيار فترة زمنية
    function selectTimeSlot(startTime, endTime) {
        console.log('Selecting time slot:', startTime, '-', endTime);

        // التحقق من أن الوقت المحدد ليس معطلاً
        const selectedButton = Array.from(document.querySelectorAll('.time-slot-btn-new'))
            .find(btn => btn.dataset.startTime === startTime && btn.dataset.endTime === endTime);

        if (!selectedButton) {
            console.error('Button for time slot not found:', startTime, '-', endTime);
            return;
        }

        if (selectedButton.disabled) {
            console.log('لا يمكن اختيار وقت معطل');
            return;
        }

        // تحديث الحقول المخفية
        document.getElementById('selected_start_time').value = startTime;
        document.getElementById('selected_end_time').value = endTime;

        // إزالة التحديد من جميع الأزرار
        document.querySelectorAll('.time-slot-btn-new').forEach(btn => {
            btn.classList.remove('selected');
        });

        // إضافة التحديد للزر المختار
        selectedButton.classList.add('selected');

        // التمرير إلى الزر المحدد للتأكد من رؤيته
        selectedButton.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

        // تفعيل زر الإضافة
        const addTimeBtn = document.getElementById('add-time-btn');
        if (addTimeBtn) {
            console.log('Enabling add time button');
            addTimeBtn.disabled = false;
        } else {
            console.error('Add time button not found');
        }
    }

    // دالة لتنظيف الصفوف الفارغة أو غير الصالحة من الجدول
    function cleanInvalidRows() {
        const tableBody = document.getElementById('scheduleTableBody');
        if (!tableBody) return;

        const rows = Array.from(tableBody.querySelectorAll('tr'));
        let hasChanges = false;

        rows.forEach(row => {
            const day = row.dataset.day;
            const startTime = row.dataset.startTime;
            const endTime = row.dataset.endTime;

            // حذف الصف إذا كان فارغًا أو غير صالح
            if (!day || !startTime || !endTime || startTime.trim() === '' || endTime.trim() === '') {
                console.log('Removing invalid row:', { day, startTime, endTime });
                row.remove();
                hasChanges = true;
            }
        });

        // إعادة ترقيم الصفوف المتبقية
        if (hasChanges) {
            const validRows = Array.from(tableBody.querySelectorAll('tr'));
            validRows.forEach((row, index) => {
                const oldIndex = row.dataset.index;
                row.dataset.index = index;

                // تحديث زر الحذف
                const deleteBtn = row.querySelector('button[onclick^="deleteScheduleRow"]');
                if (deleteBtn) {
                    deleteBtn.setAttribute('onclick', `deleteScheduleRow(${index})`);
                }

                // تحديث الحقول المخفية
                document.querySelectorAll(`input[name^="schedule[${oldIndex}]"]`).forEach(input => {
                    const newName = input.name.replace(`[${oldIndex}]`, `[${index}]`);
                    input.name = newName;
                });
            });

            // تحديث المؤشر العالمي
            window.scheduleIndex = validRows.length;
        }

        return hasChanges;
    }

    // دالة تعديل فترة زمنية في الجدول
    function addTimeSlot() {
        console.log('Adding time slot to schedule table');

        // تنظيف الصفوف الفارغة أولاً
        cleanInvalidRows();


        const startTime = document.getElementById('selected_start_time')?.value;
        const endTime = document.getElementById('selected_end_time')?.value;
        const daySelect = document.getElementById('day_select');
        const selectedDay = daySelect?.value;
        const duration = document.getElementById('duration')?.value || '1';

        console.log('Time slot details:', { selectedDay, startTime, endTime, duration });

        if (!selectedDay || !startTime || !endTime) {
            Swal.fire({
                title: 'تنبيه',
                text: 'يرجى اختيار اليوم والوقت',
                icon: 'warning',
                confirmButtonText: 'حسناً'
            });
            return;
        }

        // الحصول على الزر المحدد لتعطيله بعد الإضافة
        const selectedButton = Array.from(document.querySelectorAll('.time-slot-btn-new'))
            .find(btn => btn.dataset.startTime === startTime && btn.dataset.endTime === endTime);

        if (selectedButton) {
            console.log('Found button for selected time slot, will disable after adding to table');
        }

        // التحقق مما إذا كان هناك صف موجود بالفعل لهذا اليوم والوقت
        const existingRow = Array.from(document.querySelectorAll('#scheduleTableBody tr')).find(row =>
            row.dataset.day === selectedDay &&
            row.dataset.startTime === startTime &&
            row.dataset.startTime !== '' // التأكد من أن وقت البداية ليس فارغًا
        );

        // الحصول على أعلى مؤشر موجود في الجدول
        let maxIndex = -1;
        const existingRows = document.querySelectorAll('#scheduleTableBody tr');

        existingRows.forEach(row => {
            const rowIndex = parseInt(row.dataset.index);
            if (!isNaN(rowIndex) && rowIndex > maxIndex) {
                maxIndex = rowIndex;
            }
        });

        // استخدام المؤشر التالي لأعلى مؤشر موجود
        const nextIndex = existingRows.length > 0 ? maxIndex + 1 : 0;

        // التحقق من وجود صف مكرر بنفس اليوم والوقت
        const isDuplicate = Array.from(existingRows).some(row => {
            return row.dataset.day === selectedDay &&
                   row.dataset.startTime === startTime &&
                   row.dataset.endTime === endTime;
        });

        if (isDuplicate) {
            Swal.fire({
                title: 'تنبيه',
                text: 'هذا الوقت مضاف مسبقًا في الجدول',
                icon: 'warning',
                confirmButtonText: 'حسناً'
            });
            return;
        }

        if (existingRow) {
            // إذا كان الصف موجودًا، قم بتحديثه
            const rowIndex = existingRow.dataset.index;
            const dayText = daySelect.options[daySelect.selectedIndex].text;

            // تحديث البيانات الوصفية للصف
            existingRow.dataset.day = selectedDay;
            existingRow.dataset.startTime = startTime;
            existingRow.dataset.endTime = endTime;

            existingRow.innerHTML = `
                <td class="text-center">${dayText}</td>
                <td class="text-center">${startTime} - ${endTime}</td>
                <td class="text-center">${duration} ساعة</td>
            `;

            // تحديث الحقول المخفية
            const existingInputs = document.querySelectorAll(`input[name^="schedule[${rowIndex}]"]`);
            existingInputs.forEach(input => input.remove());

            // إضافة الحقول المخفية بشكل صحيح
            const scheduleContainer = document.getElementById('schedule-container');
            if (!scheduleContainer) {
                console.error('Schedule container not found');
                return;
            }

            // إنشاء الحقول المخفية مباشرة بدلاً من استخدام innerHTML
            const dayInput = document.createElement('input');
            dayInput.type = 'hidden';
            dayInput.name = `schedule[${rowIndex}][day]`;
            dayInput.value = selectedDay;

            const startTimeInput = document.createElement('input');
            startTimeInput.type = 'hidden';
            startTimeInput.name = `schedule[${rowIndex}][start_time]`;
            startTimeInput.value = startTime;

            const hoursInput = document.createElement('input');
            hoursInput.type = 'hidden';
            hoursInput.name = `schedule[${rowIndex}][hours]`;
            hoursInput.value = duration;

            // إضافة الحقول إلى الحاوية
            scheduleContainer.appendChild(dayInput);
            scheduleContainer.appendChild(startTimeInput);
            scheduleContainer.appendChild(hoursInput);

            console.log(`Added hidden inputs for row index ${rowIndex}:`, {
                day: selectedDay,
                start_time: startTime,
                hours: duration
            });

            // تعطيل السلوت المضاف إلى الجدول
            if (selectedButton) {
                selectedButton.classList.add('unavailable', 'stored-slot');
                selectedButton.disabled = true;
                selectedButton.title = 'هذه الفترة الزمنية موجودة بالفعل في الجدول';
                selectedButton.onclick = null; // إزالة حدث النقر
                console.log('Disabled button for time slot that was added to table');
            }

            Swal.fire({
                title: 'تم التحديث!',
                text: 'تم تحديث الموعد بنجاح',
                icon: 'success',
                confirmButtonText: 'حسناً'
            });
        } else {
            // إذا لم يكن الصف موجودًا، قم بإضافته
            const scheduleTableBody = document.getElementById('scheduleTableBody');
            const dayText = daySelect.options[daySelect.selectedIndex].text;

            const row = document.createElement('tr');
            row.dataset.index = nextIndex;
            row.dataset.day = selectedDay;
            row.dataset.startTime = startTime;
            row.dataset.endTime = endTime;

            row.innerHTML = `
                <td class="text-center">${dayText}</td>
                <td class="text-center">${startTime} - ${endTime}</td>
                <td class="text-center">${duration} ساعة</td>
            `;

            scheduleTableBody.appendChild(row);

            // إضافة الحقول المخفية بشكل صحيح
            const scheduleContainer = document.getElementById('schedule-container');
            if (!scheduleContainer) {
                console.error('Schedule container not found');
                return;
            }

            // إنشاء الحقول المخفية مباشرة بدلاً من استخدام innerHTML
            const dayInput = document.createElement('input');
            dayInput.type = 'hidden';
            dayInput.name = `schedule[${nextIndex}][day]`;
            dayInput.value = selectedDay;

            const startTimeInput = document.createElement('input');
            startTimeInput.type = 'hidden';
            startTimeInput.name = `schedule[${nextIndex}][start_time]`;
            startTimeInput.value = startTime;

            const hoursInput = document.createElement('input');
            hoursInput.type = 'hidden';
            hoursInput.name = `schedule[${nextIndex}][hours]`;
            hoursInput.value = duration;

            // إضافة الحقول إلى الحاوية
            scheduleContainer.appendChild(dayInput);
            scheduleContainer.appendChild(startTimeInput);
            scheduleContainer.appendChild(hoursInput);

            console.log(`Added hidden inputs for row index ${nextIndex}:`, {
                day: selectedDay,
                start_time: startTime,
                hours: duration
            });

            // تحديث المؤشر العالمي
            window.scheduleIndex = nextIndex + 1;

            // تعطيل السلوت المضاف إلى الجدول
            if (selectedButton) {
                selectedButton.classList.add('unavailable', 'stored-slot');
                selectedButton.disabled = true;
                selectedButton.title = 'هذه الفترة الزمنية موجودة بالفعل في الجدول';
                selectedButton.onclick = null; // إزالة حدث النقر
                console.log('Disabled button for time slot that was added to table');
            }

            // إظهار رسالة نجاح عند إضافة موعد جديد
            Swal.fire({
                title: 'تمت الإضافة!',
                text: 'تمت إضافة الموعد بنجاح',
                icon: 'success',
                confirmButtonText: 'حسناً'
            });
        }

        // إعادة تعيين الحقول المخفية
        document.getElementById('selected_start_time').value = '';
        document.getElementById('selected_end_time').value = '';

        // تعطيل زر الإضافة
        const addTimeBtn = document.getElementById('add-time-btn');
        if (addTimeBtn) {
            addTimeBtn.disabled = true;
            console.log('Disabled add time button after adding slot to table');
        }

        // إعادة بناء الجدول بالكامل من الحقول المخفية الصالحة
        console.log('Rebuilding table after adding time slot...');
        cleanEmptyHiddenFields();

        // إزالة التحديد من جميع الأزرار
        document.querySelectorAll('.time-slot-btn-new').forEach(btn => {
            btn.classList.remove('selected');
        });

        // طباعة محتوى الجدول بعد الإضافة
        console.log('Schedule table after adding time slot:', document.getElementById('scheduleTableBody').innerHTML);
    }

    // دالة لتنظيف الصفوف الفارغة من الجدول
    function cleanEmptyRows() {
        console.log('Cleaning empty rows from schedule table');
        const rows = document.querySelectorAll('#scheduleTableBody tr');

        // طباعة جميع الصفوف للتشخيص
        rows.forEach((row, i) => {
            console.log(`Row ${i+1}:`, {
                index: row.dataset.index,
                day: row.dataset.day,
                startTime: row.dataset.startTime,
                endTime: row.dataset.endTime,
                isEmpty: !row.dataset.startTime || row.dataset.startTime.trim() === ''
            });
        });

        // إزالة الصفوف الفارغة
        rows.forEach(row => {
            const startTime = row.dataset.startTime;
            if (!startTime || startTime.trim() === '') {
                console.warn('إزالة صف فارغ من الجدول:', row);

                // إزالة الحقول المخفية المرتبطة بهذا الصف
                const index = row.dataset.index;
                if (index) {
                    const inputs = document.querySelectorAll(`input[name^="schedule[${index}]"]`);
                    console.log(`Removing ${inputs.length} hidden inputs for row index ${index}`);
                    inputs.forEach(input => input.remove());
                }

                // إزالة الصف من الجدول
                row.remove();
            }
        });

        // طباعة عدد الصفوف بعد التنظيف
        const remainingRows = document.querySelectorAll('#scheduleTableBody tr');
        console.log(`Rows after cleaning: ${remainingRows.length}`);
    }

    // دالة تحديث حالة الأزرار بناءً على محتوى الجدول
    function updateButtonsState() {
        // الحصول على جميع الصفوف في الجدول
        const rows = document.querySelectorAll('#scheduleTableBody tr');
        const currentDay = document.getElementById('day_select')?.value;

        // إعادة تعيين جميع الأزرار لليوم الحالي إلى الحالة الأصلية
        document.querySelectorAll('.time-slot-btn-new').forEach(btn => {
            // إزالة فئة stored-slot من جميع الأزرار
            btn.classList.remove('unavailable', 'stored-slot');
            btn.disabled = false;
            btn.title = '';

            // إعادة تعيين دالة onclick
            const startTime = btn.dataset.startTime;
            const endTime = btn.dataset.endTime;
            btn.onclick = () => selectTimeSlot(startTime, endTime);
        });

        // تعطيل الأزرار التي تمثل أوقاتًا موجودة في الجدول
        rows.forEach(row => {
            const rowDay = row.dataset.day;
            const rowStartTime = row.dataset.startTime;
            const rowEndTime = row.dataset.endTime;

            // التحقق من وجود وقت بداية صالح
            if (!rowStartTime || rowStartTime.trim() === '') {
                console.warn('تخطي صف بدون وقت بداية في updateButtonsState:', row);
                return; // تخطي هذا الصف
            }

            // إذا كان الصف لليوم الحالي
            if (rowDay === currentDay) {
                // البحث عن الزر المطابق وتعطيله
                document.querySelectorAll('.time-slot-btn-new').forEach(btn => {
                    if (btn.dataset.startTime === rowStartTime && (btn.dataset.endTime === rowEndTime || !rowEndTime)) {
                        btn.classList.add('unavailable', 'stored-slot');
                        btn.disabled = true;
                        btn.title = 'هذه الفترة الزمنية موجودة بالفعل في الجدول';
                        btn.onclick = null; // إزالة حدث النقر
                    }
                });
            }
        });

        // التحقق من الوقت المحدد حاليًا
        const selectedStartTime = document.getElementById('selected_start_time').value;
        const selectedEndTime = document.getElementById('selected_end_time').value;

        if (selectedStartTime && selectedEndTime) {
            // البحث عن الزر المطابق للوقت المحدد
            const selectedButton = Array.from(document.querySelectorAll('.time-slot-btn-new'))
                .find(btn => btn.dataset.startTime === selectedStartTime && btn.dataset.endTime === selectedEndTime);

            // إذا كان الزر موجودًا وغير معطل، قم بتحديده
            if (selectedButton && !selectedButton.disabled) {
                selectedButton.classList.add('selected');
            } else {
                // إذا كان الزر معطلًا أو غير موجود، قم بإعادة تعيين الحقول المخفية
                document.getElementById('selected_start_time').value = '';
                document.getElementById('selected_end_time').value = '';
                if (addTimeBtn) {
                    addTimeBtn.disabled = true;
                }
            }
        }
    }

    // دالة حذف صف من الجدول
    window.deleteScheduleRow = function(index) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'هل تريد حذف هذا الموعد؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، حذف',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                // الحصول على بيانات الصف قبل حذفه
                // استخدام querySelector مع مطابقة دقيقة للمؤشر
                const row = document.querySelector(`tr[data-index="${index}"]`);
                let startTimeValue = '';
                let endTimeValue = '';

                if (row) {
                    startTimeValue = row.dataset.startTime;
                    endTimeValue = row.dataset.endTime;
                    row.remove();
                }

                // إزالة الحقول المخفية المرتبطة بهذا المؤشر
                const inputs = document.querySelectorAll(`input[name^="schedule[${index}]"]`);
                inputs.forEach(input => input.remove());

                // تخزين القيم الحالية للوقت المحدد
                const currentStartTime = document.getElementById('selected_start_time').value;
                const currentEndTime = document.getElementById('selected_end_time').value;

                // إذا كان الوقت المحدد هو نفس الوقت المحذوف، قم بإعادة تعيينه
                if (currentStartTime === startTimeValue && (currentEndTime === endTimeValue || !endTimeValue)) {
                    document.getElementById('selected_start_time').value = '';
                    document.getElementById('selected_end_time').value = '';
                    addTimeBtn.disabled = true;
                }

                // إعادة بناء الجدول بالكامل من الحقول المخفية الصالحة
                console.log('Rebuilding table after deleting row...');
                cleanEmptyHiddenFields();

                // إعادة تفعيل السلوت المحذوف من الجدول
                const currentDay = document.getElementById('day_select')?.value;
                if (currentDay === row.dataset.day) {
                    // البحث عن الزر المطابق للوقت المحذوف
                    const matchingButton = Array.from(document.querySelectorAll('.time-slot-btn-new'))
                        .find(btn => btn.dataset.startTime === startTimeValue && btn.dataset.endTime === endTimeValue);

                    if (matchingButton) {
                        console.log('Re-enabling button for deleted time slot:', startTimeValue, '-', endTimeValue);
                        matchingButton.classList.remove('unavailable', 'stored-slot');
                        matchingButton.disabled = false;
                        matchingButton.title = '';
                        matchingButton.onclick = () => selectTimeSlot(startTimeValue, endTimeValue);
                    } else {
                        console.log('Button for deleted time slot not found in current view');
                    }
                }

                // تحديث حالة الأزرار بناءً على محتوى الجدول
                updateButtonsState();

                // إظهار رسالة نجاح
                Swal.fire({
                    title: 'تم الحذف!',
                    text: 'تم حذف الموعد بنجاح.',
                    icon: 'success',
                    confirmButtonText: 'حسناً'
                });
            }
        });
    };

    // إضافة مستمع الأحداث لزر إضافة الوقت
    if (addTimeBtn) {
        addTimeBtn.addEventListener('click', addTimeSlot);
    }

    // إضافة مستمع الأحداث لزر التحقق من التوافر
    if (checkAvailabilityBtn) {
        checkAvailabilityBtn.addEventListener('click', checkAvailability);
    }

    // لا نقوم بالتحقق من التوافر مباشرة عند تحميل الصفحة
    // المستخدم يجب أن ينقر على زر فحص الإتاحة

    // دالة لتنظيف الحقول المخفية الفارغة وإعادة بناء الجدول
    function cleanEmptyHiddenFields() {
        console.log('%c=== CLEANING EMPTY HIDDEN FIELDS AND REBUILDING TABLE ===', 'background: #FF5722; color: white; font-weight: bold; padding: 3px 5px;');

        // 0. طباعة محتوى HTML لحاوية الجدول للتشخيص
        console.log('Schedule container HTML before cleaning:', document.getElementById('schedule-container')?.innerHTML || 'Not found');
        console.log('Schedule table HTML before cleaning:', document.getElementById('scheduleTableBody')?.innerHTML || 'Not found');

        // 1. تنظيف الحقول المخفية الفارغة
        const scheduleContainer = document.getElementById('schedule-container');
        if (!scheduleContainer) {
            console.error('Schedule container not found');
            return;
        }

        // الحصول على جميع الحقول المخفية
        const allHiddenInputs = scheduleContainer.querySelectorAll('input[name^="schedule["]');
        console.log('All hidden inputs before cleaning:', allHiddenInputs.length);

        // الحصول على جميع الحقول المخفية لوقت البداية
        const startTimeInputs = scheduleContainer.querySelectorAll('input[name^="schedule["][name$="[start_time]"]');

        // طباعة جميع الحقول المخفية للتشخيص
        console.log('Found start_time hidden fields:', startTimeInputs.length);
        startTimeInputs.forEach((input, i) => {
            const matches = input.name.match(/schedule\[(\d+)\]/);
            if (matches && matches.length === 2) {
                const index = matches[1];
                console.log(`Hidden field ${i+1}:`, {
                    name: input.name,
                    value: input.value,
                    index: index,
                    isEmpty: !input.value || input.value.trim() === ''
                });

                // إزالة الحقول المخفية الفارغة
                if (!input.value || input.value.trim() === '') {
                    console.warn(`Removing empty hidden fields for index ${index}`);
                    const relatedInputs = scheduleContainer.querySelectorAll(`input[name^="schedule[${index}]"]`);
                    relatedInputs.forEach(relatedInput => {
                        console.log(`Removing hidden field: ${relatedInput.name}`);
                        relatedInput.remove();
                    });
                }
            }
        });

        // التحقق من وجود حقول مخفية بدون وقت بداية
        const allIndices = new Set();
        const startTimeIndices = new Set();

        // جمع جميع المؤشرات
        allHiddenInputs.forEach(input => {
            const matches = input.name.match(/schedule\[(\d+)\]/);
            if (matches && matches.length === 2) {
                allIndices.add(matches[1]);
            }
        });

        // جمع مؤشرات حقول وقت البداية
        startTimeInputs.forEach(input => {
            const matches = input.name.match(/schedule\[(\d+)\]/);
            if (matches && matches.length === 2) {
                startTimeIndices.add(matches[1]);
            }
        });

        // البحث عن المؤشرات التي ليس لها حقل وقت بداية
        allIndices.forEach(index => {
            if (!startTimeIndices.has(index)) {
                console.warn(`Found hidden fields without start_time for index ${index}`);
                const relatedInputs = scheduleContainer.querySelectorAll(`input[name^="schedule[${index}]"]`);
                relatedInputs.forEach(relatedInput => {
                    console.log(`Removing incomplete hidden field: ${relatedInput.name}`);
                    relatedInput.remove();
                });
            }
        });

        // 2. جمع البيانات من الحقول المخفية المتبقية
        const validScheduleData = [];
        const remainingInputs = scheduleContainer.querySelectorAll('input[name^="schedule["][name$="[start_time]"]');

        console.log('Remaining hidden fields after cleaning:', remainingInputs.length);

        remainingInputs.forEach(input => {
            const matches = input.name.match(/schedule\[(\d+)\]/);
            if (matches && matches.length === 2) {
                const index = matches[1];

                // الحصول على الحقول المرتبطة
                const dayInput = scheduleContainer.querySelector(`input[name="schedule[${index}][day]"]`);
                const hoursInput = scheduleContainer.querySelector(`input[name="schedule[${index}][hours]"]`);

                if (dayInput && hoursInput && input.value && input.value.trim() !== '') {
                    validScheduleData.push({
                        index: index,
                        day: dayInput.value,
                        start_time: input.value,
                        hours: hoursInput.value
                    });
                }
            }
        });

        console.log('Valid schedule data collected:', validScheduleData);

        // 3. إعادة بناء الجدول من البيانات الصالحة
        rebuildScheduleTable(validScheduleData);

        console.log('Hidden fields after complete rebuild:', scheduleContainer.querySelectorAll('input[name^="schedule["]').length);
    }

    // دالة لإعادة بناء الجدول من البيانات الصالحة
    function rebuildScheduleTable(validData) {
        console.log('%c=== REBUILDING SCHEDULE TABLE ===', 'background: #2196F3; color: white; font-weight: bold; padding: 3px 5px;');
        console.log('Rebuilding schedule table with valid data:', validData);

        // 1. إفراغ الجدول والحقول المخفية
        const scheduleTableBody = document.getElementById('scheduleTableBody');
        const scheduleContainer = document.getElementById('schedule-container');

        if (!scheduleTableBody || !scheduleContainer) {
            console.error('Schedule table or container not found');
            return;
        }

        // طباعة محتوى الجدول قبل إفراغه
        console.log('Schedule table before clearing:', scheduleTableBody.innerHTML);
        console.log('Schedule container before clearing:', scheduleContainer.innerHTML);

        // إفراغ الجدول
        scheduleTableBody.innerHTML = '';
        console.log('Table cleared');

        // إفراغ حاوية الحقول المخفية
        scheduleContainer.innerHTML = '';
        console.log('Hidden fields container cleared');

        // 2. إعادة بناء الجدول والحقول المخفية
        validData.forEach((item, newIndex) => {
            // حساب وقت الانتهاء
            const startTimeParts = item.start_time.split(':');
            const startHour = parseInt(startTimeParts[0]);
            const startMinute = parseInt(startTimeParts[1]);
            const durationHours = parseInt(item.hours);

            const endHour = startHour + durationHours;
            const endMinute = startMinute;
            const endTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;

            // الحصول على نص اليوم
            const daySelect = document.getElementById('day_select');
            let dayText = item.day; // القيمة الافتراضية هي قيمة اليوم

            if (daySelect) {
                // البحث عن النص المقابل لقيمة اليوم
                const option = Array.from(daySelect.options).find(opt => opt.value === item.day);
                if (option) {
                    dayText = option.text;
                }
            }

            // إنشاء صف جديد
            const row = document.createElement('tr');
            row.dataset.index = newIndex;
            row.dataset.day = item.day;
            row.dataset.startTime = item.start_time;
            row.dataset.endTime = endTime;

            row.innerHTML = `
                <td class="text-center">${dayText}</td>
                <td class="text-center">${item.start_time} - ${endTime}</td>
                <td class="text-center">${item.hours} ساعة</td>
            `;

            scheduleTableBody.appendChild(row);

            // إنشاء الحقول المخفية
            const dayInput = document.createElement('input');
            dayInput.type = 'hidden';
            dayInput.name = `schedule[${newIndex}][day]`;
            dayInput.value = item.day;

            const startTimeInput = document.createElement('input');
            startTimeInput.type = 'hidden';
            startTimeInput.name = `schedule[${newIndex}][start_time]`;
            startTimeInput.value = item.start_time;

            const hoursInput = document.createElement('input');
            hoursInput.type = 'hidden';
            hoursInput.name = `schedule[${newIndex}][hours]`;
            hoursInput.value = item.hours;

            scheduleContainer.appendChild(dayInput);
            scheduleContainer.appendChild(startTimeInput);
            scheduleContainer.appendChild(hoursInput);

            console.log(`Added row and hidden fields for index ${newIndex}:`, {
                day: item.day,
                start_time: item.start_time,
                hours: item.hours,
                end_time: endTime
            });
        });

        // 3. تحديث مؤشر الجدول
        window.scheduleIndex = validData.length;
        console.log(`Updated schedule index to ${window.scheduleIndex}`);

        // 4. تحديث حالة الأزرار
        updateButtonsState();
    }

    // تنظيف الحقول المخفية الفارغة أولاً
    cleanEmptyHiddenFields();

    // لكن نقوم بعرض الأوقات المحجوزة الحالية في الجدول
    loadExistingSchedules();

    // تنظيف الصفوف الفارغة عند تحميل الصفحة
    cleanEmptyRows();

    // تحديث حالة الأزرار بعد تحميل الصفحة
    setTimeout(updateButtonsState, 500);

    // دالة لتحميل الأوقات المحجوزة الحالية في الجدول
    function loadExistingSchedules() {
        console.log('%c=== LOADING EXISTING SCHEDULES ===', 'background: #9C27B0; color: white; font-weight: bold; padding: 3px 5px;');

        const scheduleTableBody = document.getElementById('scheduleTableBody');
        if (!scheduleTableBody) {
            console.error('Schedule table body not found');
            return;
        }


        // تنظيف الجدول من أي صفوف موجودة مسبقًا
        scheduleTableBody.innerHTML = '';

        // تنظيف الحقول المخفية
        const scheduleContainer = document.getElementById('schedule-container');
        if (!scheduleContainer) {
            console.error('Schedule container not found');
            return;
        }

        // إزالة جميع حقول الإدخال المخفية الموجودة
        const hiddenInputs = scheduleContainer.querySelectorAll('input[name^="schedule["]');
        hiddenInputs.forEach(input => input.remove());

        // محاولة الحصول على بيانات الجدول الزمني من النموذج
        let scheduleData = [];

        // طريقة 1: التحقق من وجود عنصر بيانات مخفي
        const scheduleDataElement = document.getElementById('job-application-schedule-data');
        if (scheduleDataElement) {
            try {
                const jsonData = scheduleDataElement.value || scheduleDataElement.textContent;
                if (jsonData) {
                    scheduleData = JSON.parse(jsonData);
                    console.log('Found schedule data from hidden element:', scheduleData);
                }
            } catch (e) {
                console.error('Error parsing schedule data from element:', e);
            }
        }

        // طريقة 2: التحقق من وجود حقول مخفية
        if (scheduleData.length === 0) {
            const scheduleInputs = scheduleContainer.querySelectorAll('input[name^="schedule["]');
            console.log('Schedule inputs found:', scheduleInputs.length);

            if (scheduleInputs.length > 0) {
                // تجميع الحقول المخفية حسب المؤشر
                const parsedData = {};
                scheduleInputs.forEach(input => {
                    // استخراج المؤشر والحقل من اسم الحقل
                    // مثلاً: schedule[0][day] -> index=0, field=day
                    const matches = input.name.match(/schedule\[(\d+)\]\[([^\]]+)\]/);
                    if (matches && matches.length === 3) {
                        const index = matches[1];
                        const field = matches[2];
                        const value = input.value;

                        if (!parsedData[index]) {
                            parsedData[index] = {};
                        }

                        parsedData[index][field] = value;
                    }
                });

                console.log('Parsed schedule data from inputs:', parsedData);

                // تحويل البيانات المجمعة إلى مصفوفة
                scheduleData = Object.values(parsedData);
            }
        }

        // طريقة 3: التحقق من وجود بيانات الجدول الزمني في النموذج
        if (scheduleData.length === 0 && typeof jobApplicationSchedule !== 'undefined' && jobApplicationSchedule) {
            console.log('Found schedule data from global variable:', jobApplicationSchedule);

            // تحويل بيانات الجدول الزمني إلى التنسيق المطلوب
            try {
                // التحقق من نوع البيانات
                if (typeof jobApplicationSchedule === 'string') {
                    jobApplicationSchedule = JSON.parse(jobApplicationSchedule);
                }

                // تحويل البيانات إلى التنسيق المطلوب
                if (Array.isArray(jobApplicationSchedule)) {
                    // تحويل البيانات من تنسيق [{day: 'monday', hours: [{start_time: '10:00', duration: 2}, ...]}]
                    // إلى تنسيق [{day: 'monday', start_time: '10:00', hours: 2}, ...]
                    const flattenedData = [];
                    jobApplicationSchedule.forEach(daySchedule => {
                        const day = daySchedule.day;
                        if (day && Array.isArray(daySchedule.hours)) {
                            daySchedule.hours.forEach(hourData => {
                                flattenedData.push({
                                    day: day,
                                    start_time: hourData.start_time,
                                    hours: hourData.duration || 2
                                });
                            });
                        }
                    });

                    scheduleData = flattenedData;
                    console.log('Converted schedule data:', scheduleData);
                }
            } catch (e) {
                console.error('Error processing schedule data:', e);
            }
        }

        // If no data found
        if (scheduleData.length === 0) {
            console.log('No schedule data found, nothing to load');
            return;
        }

        console.log('Final schedule data to load:', scheduleData);

        // Filter only valid data
        const validScheduleData = scheduleData.filter(item =>
            item &&
            item.day &&
            item.start_time &&
            (item.hours || item.duration) &&
            item.start_time.trim() !== ''
        );

        console.log('Valid schedule data to load:', validScheduleData);

        // Add rows to the table based on valid data
        validScheduleData.forEach((item, index) => {
            // Ensure hours field exists
            const hours = item.hours || item.duration || 2;

            // Get day text
            const daySelect = document.getElementById('day_select');
            let dayText = item.day; // Default is the day value

            if (daySelect) {
                // Find the text corresponding to the day value
                const option = Array.from(daySelect.options).find(opt => opt.value === item.day);
                if (option) {
                    dayText = option.text;
                }
            }

            // Calculate end time
            const startTimeParts = item.start_time.split(':');
            const startHour = parseInt(startTimeParts[0]);
            const startMinute = parseInt(startTimeParts[1]);
            const durationHours = parseInt(hours);

            const endHour = startHour + durationHours;
            const endMinute = startMinute;
            const endTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;

            // Create new row
            const row = document.createElement('tr');
            row.dataset.index = index;
            row.dataset.day = item.day;
            row.dataset.startTime = item.start_time;
            row.dataset.endTime = endTime;

            row.innerHTML = `
                <td class="text-center">${dayText}</td>
                <td class="text-center">${item.start_time} - ${endTime}</td>
                <td class="text-center">${hours} ساعة</td>
            `;

            scheduleTableBody.appendChild(row);
            console.log('Added row to schedule table for day:', item.day, 'time:', item.start_time);

            // Add hidden fields for the form
            addScheduleHiddenFields(index, item.day, item.start_time, hours, endTime);

            // Update global index
            window.scheduleIndex = index + 1;
        });

        // Update button states after loading the table
        updateButtonsState();
    }

    // Function to add hidden form fields for schedule items
    function addScheduleHiddenFields(index, day, startTime, hours, endTime) {
        const container = document.getElementById('schedule-container');
        if (!container) {
            console.error('Schedule container not found');
            return;
        }

        // Check if hidden fields with the same index already exist
        const existingDayInput = document.querySelector(`input[name="schedule[${index}][day]"]`);
        if (existingDayInput) {
            console.log(`Hidden fields for index ${index} already exist, skipping`);
            return;
        }

        // Validate input data
        if (!day || !startTime || !hours) {
            console.error(`Invalid data for hidden fields at index ${index}:`, { day, startTime, hours, endTime });
            return;
        }

        console.log(`Adding hidden fields for schedule item ${index}:`, { day, startTime, hours, endTime });

        // Create hidden fields for day, start time, and duration
        const dayInput = document.createElement('input');
        dayInput.type = 'hidden';
        dayInput.name = `schedule[${index}][day]`;
        dayInput.value = day;

        const startTimeInput = document.createElement('input');
        startTimeInput.type = 'hidden';
        startTimeInput.name = `schedule[${index}][start_time]`;
        startTimeInput.value = startTime;

        const hoursInput = document.createElement('input');
        hoursInput.type = 'hidden';
        hoursInput.name = `schedule[${index}][hours]`;
        hoursInput.value = hours;

        // Add fields to the container
        container.appendChild(dayInput);
        container.appendChild(startTimeInput);
        container.appendChild(hoursInput);

        // Update the schedule index
        if (typeof window.scheduleIndex === 'undefined') {
            window.scheduleIndex = 0;
        }
        window.scheduleIndex = Math.max(window.scheduleIndex, parseInt(index) + 1);
        console.log(`Updated schedule index to ${window.scheduleIndex}`);
    }
});

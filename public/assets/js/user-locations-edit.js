/**
 * User Locations Edit - Handles loading and selecting locations based on selected user for edit page
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const userSelect = document.getElementById('user_id');
    const locationSelect = document.getElementById('location_id');
    const userNameSpan = document.getElementById('selected-user-name');

    if (!userSelect || !locationSelect) {
        console.error('Required elements not found');
        return;
    }

    // Store the initially selected location ID
    const selectedLocationId = locationSelect.getAttribute('data-selected-id');
    console.log('Initially selected location ID:', selectedLocationId);

    // Function to load locations for a user
    function loadUserLocations(userId) {
        if (!userId) {
            console.log('No user ID provided');
            return;
        }

        console.log('Loading locations for user ID:', userId);

        // Update user name display
        const selectedOption = userSelect.options[userSelect.selectedIndex];
        const userName = selectedOption ? selectedOption.text : userId;
        if (userNameSpan) {
            userNameSpan.textContent = userName;
        }

        // Disable location select and show loading
        locationSelect.disabled = true;
        locationSelect.innerHTML = '<option value="">جاري التحميل...</option>';

        // Fetch locations from server
        fetch(`/api/users/${userId}/locations`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Locations data:', data);

            // Clear location select
            locationSelect.innerHTML = '';

            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = locationSelect.getAttribute('data-placeholder') || 'اختر الموقع';
            locationSelect.appendChild(defaultOption);

            // Add locations to select
            // استخدام data.data بدلاً من data.locations لتتوافق مع هيكل الاستجابة من API
            const locations = data.data || (data.locations || []);
            if (locations.length > 0) {
                locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.setAttribute('data-name', location.name);
                    option.textContent = location.name;

                    // Select this option if it matches the previously selected location
                    if (selectedLocationId && location.id.toString() === selectedLocationId.toString()) {
                        option.selected = true;
                        console.log('Selected location found and set:', location.name);
                    }

                    locationSelect.appendChild(option);
                });
            } else {
                console.log('No locations found for user');
                // إضافة خيار لإظهار أنه لا توجد مواقع
                const noLocationsOption = document.createElement('option');
                noLocationsOption.value = '';
                noLocationsOption.textContent = 'لا توجد مواقع لهذا المستخدم';
                noLocationsOption.disabled = true;
                locationSelect.appendChild(noLocationsOption);
            }

            // Enable location select
            locationSelect.disabled = false;

            // Initialize Select2 if available
            if (typeof $ !== 'undefined' && $.fn && $.fn.select2) {
                // تدمير أي نسخة سابقة من Select2 لتجنب المشاكل
                if ($(locationSelect).data('select2')) {
                    $(locationSelect).select2('destroy');
                }

                // تهيئة Select2 مع الخيارات المناسبة
                $(locationSelect).select2({
                    width: '100%',
                    allowClear: true,
                    minimumInputLength: 0,
                    placeholder: locationSelect.getAttribute('data-placeholder') || 'اختر الموقع',
                    dir: document.documentElement.dir || 'rtl',
                    language: document.documentElement.lang || 'ar'
                });

                // تعيين القيمة المحددة لـ Select2
                if (selectedLocationId) {
                    console.log('Setting selected location ID in Select2:', selectedLocationId);
                    setTimeout(() => {
                        $(locationSelect).val(selectedLocationId).trigger('change');
                    }, 100);
                }
            }
        })
        .catch(error => {
            console.error('Error loading locations:', error);

            // Reset location select
            locationSelect.innerHTML = '';

            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'خطأ في تحميل المواقع';
            locationSelect.appendChild(defaultOption);

            // Enable location select
            locationSelect.disabled = false;

            // إعادة تهيئة Select2 في حالة الخطأ
            if (typeof $ !== 'undefined' && $.fn && $.fn.select2) {
                // تدمير أي نسخة سابقة من Select2
                if ($(locationSelect).data('select2')) {
                    $(locationSelect).select2('destroy');
                }

                // إعادة تهيئة Select2
                $(locationSelect).select2({
                    width: '100%',
                    allowClear: true,
                    minimumInputLength: 0,
                    placeholder: 'اختر الموقع',
                    dir: document.documentElement.dir || 'rtl',
                    language: document.documentElement.lang || 'ar'
                });
            }
        });
    }

    // Add event listener to user select
    userSelect.addEventListener('change', function() {
        const userId = this.value;
        if (userId) {
            loadUserLocations(userId);
        } else {
            // Reset location select
            locationSelect.innerHTML = '<option value="">اختر الموقع</option>';
            if (userNameSpan) {
                userNameSpan.textContent = 'اختر المستخدم أولاً';
            }
        }
    });

    // Make the function globally available
    window.loadUserLocations = loadUserLocations;

    // Load locations if user is already selected
    if (userSelect.value) {
        console.log('User already selected on page load:', userSelect.value);
        loadUserLocations(userSelect.value);
    }
});

/**
 * Load Selected Service - Ensures the selected service is properly loaded and displayed in edit mode
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const serviceSelect = document.getElementById('service_id');
    
    if (!serviceSelect) {
        console.error('Service select element not found');
        return;
    }
    
    // Get the selected service ID from the data attribute
    const selectedServiceId = serviceSelect.getAttribute('data-selected-id');
    
    if (!selectedServiceId) {
        console.log('No selected service ID found');
        return;
    }
    
    console.log('Load Selected Service: Initializing with service ID:', selectedServiceId);
    
    // Function to directly select the service
    function selectServiceDirectly() {
        // Check if the service option exists
        let serviceOption = serviceSelect.querySelector(`option[value="${selectedServiceId}"]`);
        
        // If the option doesn't exist, we need to add it
        if (!serviceOption) {
            console.log('Selected service option not found, creating it');
            
            // Try to get the service name from any existing selected option
            const existingSelectedOption = serviceSelect.querySelector('option[selected]');
            const serviceName = existingSelectedOption ? 
                existingSelectedOption.textContent : 
                'Service #' + selectedServiceId;
            
            // Create and add the option
            serviceOption = document.createElement('option');
            serviceOption.value = selectedServiceId;
            serviceOption.textContent = serviceName;
            serviceOption.selected = true;
            serviceSelect.appendChild(serviceOption);
        } else {
            // If the option exists, make sure it's selected
            serviceOption.selected = true;
        }
        
        // If Select2 is available, update it
        if (typeof $ !== 'undefined' && $.fn && $.fn.select2) {
            console.log('Updating Select2 with selected service');
            $(serviceSelect).val(selectedServiceId).trigger('change');
        }
    }
    
    // Make the function globally available
    window.selectServiceDirectly = selectServiceDirectly;
    
    // Try to select the service after a short delay to ensure the DOM is ready
    setTimeout(selectServiceDirectly, 500);
    
    // Add a listener for when services are loaded
    serviceSelect.addEventListener('change', function() {
        console.log('Service select changed, checking if selected service is set');
        
        // If the selected value is not the one we want, try to set it again
        if (serviceSelect.value !== selectedServiceId) {
            const hasOption = Array.from(serviceSelect.options).some(option => 
                option.value === selectedServiceId
            );
            
            if (hasOption) {
                console.log('Selected service option exists but not selected, selecting it');
                serviceSelect.value = selectedServiceId;
                
                // Update Select2 if available
                if (typeof $ !== 'undefined' && $.fn && $.fn.select2) {
                    $(serviceSelect).val(selectedServiceId).trigger('change');
                }
            }
        }
    });
    
    // Final attempt after a longer delay
    setTimeout(function() {
        if (serviceSelect.value !== selectedServiceId) {
            console.log('Final attempt to select service');
            selectServiceDirectly();
        }
    }, 2000);
});

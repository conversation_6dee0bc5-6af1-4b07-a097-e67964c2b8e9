/**
 * Provider Services Edit - Handles loading and selecting services based on selected provider for edit page
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const providerSelect = document.getElementById('provider_id');
    const serviceSelect = document.getElementById('service_id');
    const loadingIndicator = document.getElementById('service-loading');

    if (!providerSelect || !serviceSelect) {
        console.error('Required elements not found for provider services');
        return;
    }

    // Store the initially selected service ID
    const selectedServiceId = serviceSelect.getAttribute('data-selected-id');
    console.log('Initially selected service ID:', selectedServiceId);

    // Function to load services for a provider
    function loadProviderServices(providerId) {
        if (!providerId) {
            console.log('No provider ID provided');
            return;
        }

        console.log('Loading services for provider ID:', providerId);

        // Show loading indicator
        if (loadingIndicator) {
            loadingIndicator.style.display = 'block';
        }

        // Disable service select and show loading
        serviceSelect.disabled = true;
        serviceSelect.innerHTML = '<option value="">جاري التحميل...</option>';

        // Fetch services from server
        fetch(`/api/provider-services?provider_id=${providerId}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Services data:', data);

            // Clear service select
            serviceSelect.innerHTML = '';

            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = serviceSelect.getAttribute('data-placeholder') || 'اختر الخدمة';
            serviceSelect.appendChild(defaultOption);

            // Add services to select
            let servicesFound = false;
            let selectedServiceFound = false;

            // Check both data.services and data.data for services
            const services = data.services || data.data || [];
            
            if (services && services.length > 0) {
                servicesFound = true;
                
                services.forEach(service => {
                    // Handle service name which might be a string or an object with language keys
                    let serviceName = '';
                    
                    if (typeof service.name === 'string') {
                        serviceName = service.name;
                    } else if (typeof service.name === 'object') {
                        // Get current language
                        const currentLocale = document.documentElement.lang || 'ar';
                        
                        // Try to get name in current language, fallback to any available
                        serviceName = service.name[currentLocale] || 
                                     service.name.ar || 
                                     service.name.en || 
                                     Object.values(service.name)[0] || 
                                     'Service #' + service.id;
                    } else {
                        serviceName = 'Service #' + service.id;
                    }
                    
                    const option = document.createElement('option');
                    option.value = service.id;
                    option.textContent = serviceName;
                    
                    // Select this option if it matches the previously selected service
                    if (selectedServiceId && service.id.toString() === selectedServiceId.toString()) {
                        option.selected = true;
                        selectedServiceFound = true;
                        console.log('Selected service found and set:', serviceName);
                    }
                    
                    serviceSelect.appendChild(option);
                });
            }

            // If the selected service wasn't found in the loaded services but we have a selected service ID
            if (!selectedServiceFound && selectedServiceId && serviceSelect.querySelector('option[value="' + selectedServiceId + '"]') === null) {
                // Try to get the service name from the existing selected option
                const existingSelectedOption = document.querySelector('#service_id option[selected]');
                const serviceName = existingSelectedOption ? existingSelectedOption.textContent : 'Service #' + selectedServiceId;
                
                // Add the selected service as an option
                const selectedOption = document.createElement('option');
                selectedOption.value = selectedServiceId;
                selectedOption.textContent = serviceName;
                selectedOption.selected = true;
                serviceSelect.appendChild(selectedOption);
                console.log('Added previously selected service that was not in the loaded services:', serviceName);
            }

            if (!servicesFound) {
                console.log('No services found for provider');
                const noServicesOption = document.createElement('option');
                noServicesOption.value = '';
                noServicesOption.textContent = 'لا توجد خدمات لهذا المزود';
                noServicesOption.disabled = true;
                serviceSelect.appendChild(noServicesOption);
            }

            // Enable service select
            serviceSelect.disabled = false;

            // Initialize Select2 if available
            if (typeof $ !== 'undefined' && $.fn && $.fn.select2) {
                // Destroy any previous Select2 instance
                if ($(serviceSelect).data('select2')) {
                    $(serviceSelect).select2('destroy');
                }
                
                // Initialize Select2 with appropriate options
                $(serviceSelect).select2({
                    width: '100%',
                    allowClear: true,
                    minimumInputLength: 0,
                    placeholder: serviceSelect.getAttribute('data-placeholder') || 'اختر الخدمة',
                    dir: document.documentElement.dir || 'rtl',
                    language: document.documentElement.lang || 'ar'
                });

                // Set the selected value for Select2
                if (selectedServiceId) {
                    console.log('Setting selected service ID in Select2:', selectedServiceId);
                    setTimeout(() => {
                        $(serviceSelect).val(selectedServiceId).trigger('change');
                    }, 100);
                }
            }

            // Hide loading indicator
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading services:', error);

            // Reset service select
            serviceSelect.innerHTML = '';

            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'خطأ في تحميل الخدمات';
            serviceSelect.appendChild(defaultOption);

            // Enable service select
            serviceSelect.disabled = false;

            // Re-initialize Select2 in case of error
            if (typeof $ !== 'undefined' && $.fn && $.fn.select2) {
                // Destroy any previous Select2 instance
                if ($(serviceSelect).data('select2')) {
                    $(serviceSelect).select2('destroy');
                }
                
                // Re-initialize Select2
                $(serviceSelect).select2({
                    width: '100%',
                    allowClear: true,
                    minimumInputLength: 0,
                    placeholder: 'اختر الخدمة',
                    dir: document.documentElement.dir || 'rtl',
                    language: document.documentElement.lang || 'ar'
                });
            }

            // Hide loading indicator
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        });
    }

    // Add event listener to provider select
    providerSelect.addEventListener('change', function() {
        const providerId = this.value;
        if (providerId) {
            loadProviderServices(providerId);
        } else {
            // Reset service select
            serviceSelect.innerHTML = '<option value="">اختر الخدمة</option>';
            
            // Re-initialize Select2
            if (typeof $ !== 'undefined' && $.fn && $.fn.select2) {
                $(serviceSelect).trigger('change');
            }
        }
    });

    // Make the function globally available
    window.loadProviderServices = loadProviderServices;

    // Load services if provider is already selected
    if (providerSelect.value) {
        console.log('Provider already selected on page load:', providerSelect.value);
        loadProviderServices(providerSelect.value);
    }
});

/**
 * Location Loader - Handles loading user locations for job applications
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const userSelect = document.getElementById('user_id');
    const locationSelect = document.getElementById('location_id');
    const userNameSpan = document.getElementById('selected-user-name');
    
    if (!userSelect || !locationSelect) {
        console.error('Required elements not found');
        return;
    }
    
    // Function to load locations for a user
    function loadUserLocations(userId) {
        if (!userId) {
            console.log('No user ID provided');
            return;
        }
        
        console.log('Loading locations for user ID:', userId);
        
        // Update user name display
        const selectedOption = userSelect.options[userSelect.selectedIndex];
        const userName = selectedOption ? selectedOption.text : userId;
        if (userNameSpan) {
            userNameSpan.textContent = userName;
        }
        
        // Disable location select and show loading
        locationSelect.disabled = true;
        locationSelect.innerHTML = '<option value="">Loading...</option>';
        
        // Fetch locations from server
        fetch(`/job-applications/user-locations?user_id=${userId}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Locations data:', data);
            
            // Clear location select
            locationSelect.innerHTML = '';
            
            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = locationSelect.getAttribute('data-placeholder') || 'Select Location';
            locationSelect.appendChild(defaultOption);
            
            // Add locations to select
            if (data.success && data.locations && data.locations.length > 0) {
                data.locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.setAttribute('data-name', location.name);
                    option.textContent = location.name;
                    locationSelect.appendChild(option);
                });
            } else {
                console.log('No locations found for user');
            }
            
            // Enable location select
            locationSelect.disabled = false;
            
            // Initialize Select2 if available
            if (typeof $ !== 'undefined' && $.fn && $.fn.select2) {
                $(locationSelect).select2({
                    width: '100%',
                    allowClear: true,
                    minimumInputLength: 0,
                    placeholder: locationSelect.getAttribute('data-placeholder') || 'Select Location'
                });
            }
        })
        .catch(error => {
            console.error('Error loading locations:', error);
            
            // Reset location select
            locationSelect.innerHTML = '';
            
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Error loading locations';
            locationSelect.appendChild(defaultOption);
            
            // Enable location select
            locationSelect.disabled = false;
        });
    }
    
    // Add event listener to user select
    userSelect.addEventListener('change', function() {
        const userId = this.value;
        if (userId) {
            loadUserLocations(userId);
        } else {
            // Reset location select
            locationSelect.innerHTML = '<option value="">Select Location</option>';
            if (userNameSpan) {
                userNameSpan.textContent = 'Select User First';
            }
        }
    });
    
    // Load locations if user is already selected
    if (userSelect.value) {
        loadUserLocations(userSelect.value);
    }
    
    // Update hidden fields when location is selected
    locationSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption && selectedOption.value) {
            const locationId = selectedOption.value;
            const locationName = selectedOption.getAttribute('data-name') || selectedOption.textContent;
            
            // Update hidden fields if they exist
            const locationNameInput = document.getElementById('location_name');
            if (locationNameInput) {
                locationNameInput.value = locationName;
            }
        }
    });
});

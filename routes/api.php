<?php

use App\Http\Controllers\api\authController;
use App\Http\Controllers\api\ChatController;
use App\Http\Controllers\api\ConsultationController;
use App\Http\Controllers\api\CouponController;
use App\Http\Controllers\api\CustomerFinancialCloseController;
use App\Http\Controllers\api\DataController;
use App\Http\Controllers\api\FinancialCloseController;
use App\Http\Controllers\api\FinancialReportController;
use App\Http\Controllers\api\JobApplicationController;
use App\Http\Controllers\api\LocationController;
use App\Http\Controllers\api\MobileJobApplicationController;
use App\Http\Controllers\api\NotificationController;
use App\Http\Controllers\api\PageController;
use App\Http\Controllers\api\Provider\AlbumController;
use App\Http\Controllers\api\Provider\AlbumPhotoController;
use App\Http\Controllers\api\Provider\OfferController;
use App\Http\Controllers\api\Provider\OfferServicesController;
use App\Http\Controllers\api\Provider\ServiceController;
use App\Http\Controllers\api\Provider\WalletController;
use App\Http\Controllers\api\ProviderController;
use App\Http\Controllers\api\RequestOfferController;
use App\Http\Controllers\api\ServiceGeneralController;
use App\Http\Controllers\api\SettingController;
use App\Http\Controllers\api\SmsAuthController;
use App\Http\Controllers\api\TaskApiController;
use App\Http\Controllers\api\TaskController;
use App\Http\Controllers\api\User\BillingController;
use App\Http\Controllers\api\User\BookingController;
use App\Http\Controllers\api\User\FavoritesController;
use App\Http\Controllers\api\User\OrderController;
use App\Http\Controllers\api\User\UrgentBookingController;
use App\Http\Controllers\api\UserController;
use App\Http\Controllers\api\UserLocationController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::get('user/get-works', [TaskController::class, 'getMonthlyCalendarUser'])->middleware('auth:sanctum');

// Start Authentication
Route::post('/api-login', [authController::class, 'login'])->name('api-login');
// Route::post('/api-register', [authController::class, 'Signup'])->name('api-register');
// Route::post('/verify_account', [authController::class, 'VerfiyCode'])->name('api-VerfiyCode');
Route::post('/forget_password', [authController::class, 'forgetPassword'])->name('api-forgetPassword');
Route::post('/change_password', [authController::class, 'changePassword'])->name('api-changePassword');

// SMS Authentication
Route::post('/api-register', [SmsAuthController::class, 'register'])->name('sms-register');
Route::post('/verify_account', [SmsAuthController::class, 'verifyOtp'])->name('sms-verify-otp');
Route::post('/resend-otp', [SmsAuthController::class, 'resendOtp'])->name('sms-resend-otp');
Route::post('/verify_otp', [SmsAuthController::class, 'verifyOldOtp'])->name('old-verify-otp');
Route::post('/resend_otp', [SmsAuthController::class, 'resendOldOtp'])->name('old-resend-otp');
// End Authentication

// Start Settings
// Route::get('/languages', [SettingController::class, 'getLanguages'])->name("api-languages");
// Route::get('/districts', [SettingController::class, 'getDistricts'])->name("api-districts");
// Route::get('/cities', [SettingController::class, 'getCities'])->name("api-cities");
// End Settings

/*********************** start of provider route  *************/
Route::get('/providers', [ProviderController::class, 'getAllProviders']);
Route::get('/provider-by-id/{provider_id}', [ProviderController::class, 'getProviderByID']);
Route::get('/provider-by-user/{user_id}', [ProviderController::class, 'getProviderByUser']);
Route::post('/provider/filter', [ProviderController::class, 'filter']);

Route::middleware(['auth:sanctum', 'checkProvider'])->group(function () {
    Route::post('/provider/set-work-cities', [ProviderController::class, 'setWorkCity']);
    Route::get('/provider/get-work-cities', [ProviderController::class, 'getWorkCity']);
    Route::post('/provider/set-working-time', [ProviderController::class, 'setWorkTime']);
    Route::get('/provider/working-times', [ProviderController::class, 'getWorkTime']);
    Route::post('/provider/delete-working-day/{id}', [ProviderController::class, 'deleteWorkTime']);
    Route::get('/provider/reviews', [ProviderController::class, 'indexReview']);

    // Start Of service Route
    Route::get('/provider/get-service-pricing/{id}', [ServiceController::class, 'getServicePricing']);
    Route::get('/provider/get-service-subs/{id}', [ServiceController::class, 'getServiceSubs']);
    Route::post('/provider/new-service', [ServiceController::class, 'store']);
    Route::post('/provider/edit-service', [ServiceController::class, 'update']);
    Route::post('/provider/delete-service/{id}', [ServiceController::class, 'destroy']);
    // End of service Route

    // Start Of Request Offer Route
    Route::get('/provider/offers-request/pending', [RequestOfferController::class, 'getProviderPendingOffers']);
    Route::get('/provider/offers-request/waiting-to-visit', [RequestOfferController::class, 'getWaitingRequestedOffers']);
    Route::get('/provider/offers-request/unpaid', [RequestOfferController::class, 'getUnPaidRequestedOffers']);
    Route::post('/provider/offers-request/approve', [RequestOfferController::class, 'approveOffers']);
    Route::post('/provider/offers-request/cancel/{id}', [RequestOfferController::class, 'cancelOffers']);
    Route::post('/provider/offers-request/confirm-book-date', [RequestOfferController::class, 'saveBookingDate']);
    // End Of f Request Offer Route

    // Start Of Offer Route
    Route::get('/provider/offers', [OfferController::class, 'index']);
    Route::post('/provider/new-offer', [OfferController::class, 'store']);
    Route::post('/provider/edit-offer/{id}', [OfferController::class, 'update']);
    Route::post('/provider/delete-offer/{id}', [OfferController::class, 'delete']);

    // End Of Offer Route

    // Start of Order Route
    Route::get('/provider/orders', [OrderController::class, 'index']);
    Route::get('/provider/all-orders', [OrderController::class, 'getOrders']);
    Route::get('/provider/order/{id}', [OrderController::class, 'show']);
    Route::post('/provider/cancel-order/{id}', [OrderController::class, 'cancelOrder']);
    Route::post('/provider/approve-order/{id}', [OrderController::class, 'approveOrder']);
    Route::post('/provider/complete-order/{id}', [OrderController::class, 'completeOrder']);
    Route::post('/provider/change-order-pricing-option/{id}', [OrderController::class, 'setOrderPricing']);
    Route::post('/provider/update-tracking-status/{id}', [OrderController::class, 'updateTrackingStatus']);
    Route::get('/provider/orders/get-by-group/{group', [OrderController::class, 'getOrderGroup']);
    // Route::get('/provider/orders', [OrderController::class, 'setOrderPricing']);
    Route::post('/provider/approve_cloth_order/{id}', [OrderController::class, 'approveClothOrder']);
    Route::post('/provider/confirm_receiving_clothes/{id}', [OrderController::class, 'confirmReceivingClothes']);
    Route::post('/provider/clothes-clean-finished/{id}', [OrderController::class, 'cleanFinished']);
    Route::post('/provider/update-order/{id}', [OrderController::class, 'update']);
    Route::get('/provider/order-by-status/{status}', [OrderController::class, 'getOrderBySatus']);
    Route::get('/provider/get-by-group/{group}', [OrderController::class, 'getOrderByGroup']);
    Route::get('/provider/my-order/need-action', [OrderController::class, 'orderAction']);
    Route::get('/provider/my-order/to-do', [OrderController::class, 'toDo']);

    // End of Order Route

    // wallet
    Route::get('/provider/wallet', [WalletController::class, 'index']);
    Route::post('/provider/upload-invoice', [WalletController::class, 'store']);
    // end of wallet

    // Albums route
    Route::get('/provider/albums', [AlbumController::class, 'index']);
    Route::post('/provider/album/add-new', [AlbumController::class, 'store']);
    Route::post('/provider/album/update/{id}', [AlbumController::class, 'update']);
    Route::delete('/provider/album/delete/{id}', [AlbumController::class, 'destroy']);
    Route::get('/provider/album-photo/{album_id}', [AlbumPhotoController::class, 'index']);
    Route::post('/provider/album-photo/add-new', [AlbumPhotoController::class, 'store']);
    // Route::post('album-photo/update/{id}', [AlbumPhotoController::class, 'update']);
    Route::delete('/provider/album-photo/delete/{id}', [AlbumPhotoController::class, 'destroy']);
    // end of Albums Route

    Route::get('/provider/service-offers', [OfferServicesController::class, 'index']);
    Route::post('/provider/service-offers/add', [OfferServicesController::class, 'store']);
    // new apis for jobs
    Route::get('/provider/get-works', [TaskController::class, 'getMonthlyCalendarProvider']);
});
/*********************** End of provider route  *************/

/*********************** start of user route  *************/

Route::middleware(['auth:sanctum', 'checkUser'])->group(function () {
    Route::get('/current-user', [UserController::class, 'show']);
    Route::post('/edit_profile', [UserController::class, 'editProfile']);

    // Start Of Booking Route
    Route::post('/service-time', [BookingController::class, 'requestTime']);
    Route::post('/request-pricing', [BookingController::class, 'requestPricing']);
    Route::post('/new-booking', [BookingController::class, 'requestBooking']);
    Route::post('/user/new-urgent-booking', [UrgentBookingController::class, 'requestUrgentBooking']);
    // End Of Booking Route

    // Start Of Order Route
    Route::post('/user/cancel-booking/{order}', [OrderController::class, 'cancelOrder']);
    Route::post('/user/confirm-booking/{order}', [OrderController::class, 'confirmOrder']);
    Route::post('/user/rate-order/{order}', [OrderController::class, 'rateOrder']);
    Route::post('/user/approve-order-change/{order}', [OrderController::class, 'approveOrderChange']);
    Route::post('/user/cancel-order-change/{order}', [OrderController::class, 'cancelOrderChange']);
    Route::get('/user/my-bookings', [OrderController::class, 'index']);
    Route::get('/user/order/{order}', [OrderController::class, 'show']);
    Route::get('/user/orders/get-by-status/{status}', [OrderController::class, 'getOrderBySatus']);
    Route::get('/user/orders/get-by-group/{group}', [OrderController::class, 'getOrderGroup']);
    Route::get('/user/orders/need-action', [OrderController::class, 'orderAction']);
    Route::get('/user/orders/to-do', [OrderController::class, 'toDo']);

    Route::post('/user/booking-rest-payment/{id}', [OrderController::class, 'bookingRestPayment']);
    Route::post('/user/confirm-provider-pricing-request/{id}', [OrderController::class, 'confirmEstimationRequest']);
    Route::post('/user/reject-provider-pricing-request/{id}', [OrderController::class, 'rejectEstimationrequest']);
    Route::post('/user/order-tip/{id}', [OrderController::class, 'orderTip']);
    // End Of Order Route

    // Start Of Request Offer Route
    Route::post('/user/request-offer', [RequestOfferController::class, 'store']);
    Route::get('/user/offers-request/pending', [RequestOfferController::class, 'getUserPendingOffers']);
    Route::get('/user/offers-request/waiting-to-visit', [RequestOfferController::class, 'getUserWaitingRequestedOffers']);
    Route::get('/user/offers-request/unpaid', [RequestOfferController::class, 'getUserUnPaidOffers']);
    // End Of f Request Offer Route

    // billings
    Route::get('/user/billings', [BillingController::class, 'index']);
    Route::post('/user/request-invoice/{id}', [BillingController::class, 'requestInvoice']);

    // add to favoraites
    Route::post('/user/add-to-favorites', [FavoritesController::class, 'addToFavorites']);
    Route::get('/user/all-favorites', [FavoritesController::class, 'allFavorites']);
    // end

    Route::post('/book-offer-appointments/{id}', [OfferController::class, 'book_appointment']);

    // Consultation
    Route::post('/consultations/{type}', [ConsultationController::class, 'store']);

    // User Locations
    Route::post('/locations', [UserLocationController::class, 'store']);
    Route::get('/locations', [UserLocationController::class, 'index']);
    // Route::get('/locations/{id}', [UserLocationController::class, 'show']);
    Route::post('/edit-locations', [UserLocationController::class, 'update']);
    Route::post('/delete-location', [UserLocationController::class, 'destroy']);

    // User Coupons (authenticated)
    // Route::post('/validate-coupon', [CouponController::class, 'validateCoupon']);
    Route::get('/user/get-works', [TaskController::class, 'getMonthlyCalendarUser']);
});
Route::get('/users/{user_id}', [UserController::class, 'getUserById']);
Route::get('/users/{user_id}/locations', [LocationController::class, 'getUserLocations']);
Route::get('/get-provider-work-times/{providerId}', [UserController::class, 'getProviderWorkTime']);
Route::get('/get-provider-work-cities/{providerId}', [UserController::class, 'getProviderWorkCity']);
Route::get('/get-service-provider-price/{providerId}/{serviceId}', [ServiceGeneralController::class, 'getServicePrice']);

Route::get('/offers', [OfferController::class, 'index']);
Route::get('/offer/{id}', [OfferController::class, 'show']);
Route::get('/offer-appointments/{id}', [OfferController::class, 'show_appointments']);

// Chat For order
Route::get('chat', [ChatController::class, 'index']);
// new message
Route::post('send-message', [ChatController::class, 'store']);

// Notifications
// Route::get('notifications', [NotificationController::class, 'index']);
// Read Notification
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/notifications', [NotificationController::class, 'index']);
    Route::post('/notifications/read', [NotificationController::class, 'readNotification']);
    Route::post('read-notification', [NotificationController::class, 'readNotification']);
    // profile setting
    Route::post('update_fcm_token', [UserController::class, 'update_fcm_token']);
});

// Services Providers
Route::get('/provider/services', [ServiceController::class, 'index']);
// pages
Route::get('page/{slug}', [PageController::class, 'index']);
// FAQ
Route::get('faq', [PageController::class, 'faq']);
// Home
Route::get('home', [PageController::class, 'home']);
// Home
Route::get('contact_info', [PageController::class, 'contactInfo']);

// Public Coupon Validation (for testing purposes)
Route::post('/validate-coupon', [CouponController::class, 'validateCoupon']);

/*********************** End of user route  *************/

/*********************** Start of Data route  *************/
Route::controller(DataController::class)->group(function () {
    Route::get('/cities', 'getCitiesByDistrictId');
    Route::get('/districts', 'getDistricts');
    Route::get('/districts/{districtId}/cities', 'getCitiesByDistrictId');
    Route::get('/cities/{cityId}/districts', 'getDistrictsByCityId');
    Route::get('/services', 'getServices');
    Route::get('/service-by-id/{id}', 'getServiceById');
    Route::get('/skills', 'getSkills');
    Route::get('/languages', 'getLanguages');
});
/*********************** End of Data route  *************/

/*********************** Start of Mobile Job Application route  *************/
Route::middleware(['auth:sanctum'])->group(function () {
    // Mobile Job Applications - Original format
    Route::get('/mobile-job-applications', [MobileJobApplicationController::class, 'index']);
    Route::post('/mobile-job-applications', [MobileJobApplicationController::class, 'store']);
    Route::get('/mobile-job-applications/{id}', [MobileJobApplicationController::class, 'show']);
    Route::put('/mobile-job-applications/{id}', [MobileJobApplicationController::class, 'update']);
    Route::delete('/mobile-job-applications/{id}', [MobileJobApplicationController::class, 'destroy']);
    Route::post('/mobile-job-applications/delete', [MobileJobApplicationController::class, 'deleteWithId']);

    // Mobile Job Applications - Daily schedule format
    Route::post('/mobile-job-applications-daily', [MobileJobApplicationController::class, 'storeDailySchedule']);
    Route::put('/mobile-job-applications-daily/{id}', [MobileJobApplicationController::class, 'updateDailySchedule']);

    // Additional endpoints
    Route::get('/mobile-job-services', [MobileJobApplicationController::class, 'getServices']);
    Route::get('/mobile-job-weekdays', [MobileJobApplicationController::class, 'getWeekdays']);
    Route::get('/mobile-job-locations', [MobileJobApplicationController::class, 'getUserLocations']);

    // Schedule management
    Route::post('/mobile-job-weekly-schedule', [MobileJobApplicationController::class, 'setWeeklySchedule']);
    Route::post('/mobile-job-daily-schedule', [MobileJobApplicationController::class, 'setDailySchedule']);

    // Conflict checking
    Route::post('/mobile-job-check-conflicts', [MobileJobApplicationController::class, 'checkScheduleConflicts']);
    Route::post('/mobile-job-check-daily-conflicts', [MobileJobApplicationController::class, 'checkDailyScheduleConflicts']);
});

// Job Application Routes
Route::post('/job-applications/check-availability', [JobApplicationController::class, 'checkAvailability']);
Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('/job-applications', [JobApplicationController::class, 'index']);
    Route::get('/job-applications/{id}', [JobApplicationController::class, 'show']);
});
/*********************** End of Mobile Job Application route  *************/

/*********************** Start of Task API route  *************/

// Task API Routes - No authentication required for simplicity
Route::get('/tasks/job-application/{jobApplicationId}', [TaskApiController::class, 'getTasksByJobApplication']);

// Task Management Endpoint - Requires authentication
Route::middleware(['auth:sanctum'])->group(function () {
    Route::post('/tasks/update-time', [TaskController::class, 'updateTaskTime']);
});
/*********************** End of Task API route  *************/

/*********************** Start of Financial Close API route  *************/

// Financial close check routes
Route::get('/financial-close/check-provider/{providerId}/{month}/{year}', [FinancialCloseController::class, 'checkProviderTasksClosed']);
Route::get('/financial-close/check-user/{userId}/{month}/{year}', [FinancialCloseController::class, 'checkUserTasksClosed']);
/*********************** End of Financial Close API route  *************/

/*********************** Start of Financial Report API route  *************/

// Financial Report Routes - Requires authentication
Route::middleware(['auth:sanctum', 'checkUser'])->group(function () {
    Route::post('/financial-report', [FinancialReportController::class, 'getReport']);
});
/*********************** End of Financial Report API route  *************/

/*********************** Start of Customer Financial Close API route  *************/

// Customer Financial Close Routes - Requires authentication
Route::middleware(['auth:sanctum', 'checkUser'])->group(function () {
    Route::post('/customer-financial-close', [CustomerFinancialCloseController::class, 'closeCustomerFinancial']);
});
/*********************** End of Customer Financial Close API route  *************/

// /*********************** Start of Admin route  *************/
// Route::middleware(['auth:sanctum'])->prefix('admin')->group(function () {
//     // Admin User Locations
//     Route::get('/locations/all', [UserLocationController::class, 'getAllLocations']);
//     Route::get('/locations/user/{userId}', [UserLocationController::class, 'getUserLocations']);

//     // Admin User Locations with Query Parameters
//     Route::get('/locations/user', [UserLocationController::class, 'getUserLocations']); // ?user_id=123
// });
// /*********************** End of Admin route  *************/

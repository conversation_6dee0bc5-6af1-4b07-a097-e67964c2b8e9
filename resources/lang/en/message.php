<?php

return array (
  'register_success' => 'Register success',
  'Admin created successfully' => 'Admin created successfully',
  'Admin has been updated successfully' => 'Admin has been updated successfully',
  'Car Service created successfully' => 'Car Service created successfully',
  'Car Service has been updated successfully' => 'Car Service has been updated successfully',
  'Category has been updated successfully' => 'Category has been updated successfully',
  'city created successfully' => 'city created successfully',
  'City has been updated successfully' => 'City has been updated successfully',
  'district created successfully' => 'District created successfully',
  'District has been updated successfully' => 'District has been updated successfully',
  'Language created successfully' => 'Language created successfully',
  'Language has been updated successfully' => 'Language has been updated successfully',
  'provider not found' => 'Provider not found',
  'provider approved successfully' => 'Provider approved successfully',
  'provider blocked successfully' => 'Provider blocked successfully',
  'provider unblocked successfully' => 'Provider unblocked successfully',
  'successfully' => 'Successfully',
  'Settings updated successfully' => 'Settings updated successfully',
  'Role deleted successfully' => 'Role deleted successfully',
  'Service created successfully' => 'Service created successfully',
  'Service has been updated successfully' => 'Service has been updated successfully',
  'skill created successfully' => 'skill created successfully',
  'Skill has been updated successfully' => 'Skill has been updated successfully',
  'Social created successfully' => 'Social created successfully',
  'Social has been updated successfully' => 'Social has been updated successfully',
  'Sub Category created successfully' => 'Sub Category created successfully',
  'Sub Category has been updated successfully' => 'Sub Category has been updated successfully',
  'user not found' => 'User not found',
  'user blocked successfully' => 'User blocked successfully',
  'user unblocked successfully' => 'User unblocked successfully',
  'user_created_successfully' => 'User created successfully',
  'user_updated_successfully' => 'User updated successfully',
  'editUser' => 'Edit User',
  'leaveBlankToKeepCurrent' => 'Leave blank to keep current password',
  'providerInfo' => 'Provider Information',
  'providerName' => 'Name',
  'providerPhone' => 'Provider Phone',
  'providerIdNumber' => 'Provider ID Number',
  'providerDetails' => 'Provider Details',
  'providers' => 'Providers',
  'overview' => 'Overview',
  'userDetails' => 'User Details',
  'notSpecified' => 'Not Specified',
  'noProviderInfo' => 'No provider information available',
  'status' => 'Status',
  'approved' => 'Approved',
  'pending' => 'Pending',
  'active' => 'Active',
  'blocked' => 'Blocked',
  'birthDate' => 'Birth Date',
  'area' => 'Area',
  'commission' => 'Commission',
  'idNumber' => 'ID Number',
  'workZones' => 'Work Zones',
  'workSchedule' => 'Work Schedule',
  'skillsInfo' => 'Provider skills information will be displayed here',
  'workZonesInfo' => 'Provider work zones information will be displayed here',
  'workScheduleInfo' => 'Provider work schedule information will be displayed here',
  'servicesInfo' => 'Provider services information will be displayed here',
  'Please Choose Invoice File' => 'Please Choose Invoice File',
  'Please Choose Valid Invoice File' => 'Please Choose Valid Invoice File',
  'orders' => 'Orders',
  'showAllOrders' => 'Show All Orders',
  'User' => 'User',
  'All Users' => 'All Users',
  'Provider' => 'Provider',
  'allProviders' => 'All Providers',
  'districts' => 'Districts',
  'allDistricts' => 'All Districts',
  'City' => 'City',
  'allCities' => 'All Cities',
  'Providers Invoices' => 'Providers Invoices',
  'offer' => 'Offer',
  'service' => 'Service',
  'addAdmin' => 'Add Admin',
  'admins' => 'Admins',
  'addAdmins' => 'Add Admins',
  'adminName' => 'Name',
  'adminEmail' => 'Email',
  'password' => 'Password',
  'passwordConfirmation' => 'Password Confirmation',
  'adminRole' => 'Role',
  'chooseRole' => 'Choose Role',
  'noRoleFound' => 'No Role Found',
  'chooseType' => 'Choose Type',
  'chooseGender' => 'Choose Gender',
  'chooseCity' => 'Choose City',
  'code' => 'Code',
  'address' => 'Address',
  'updateAdmin' => 'Update Admin',
  'showAllAdmins' => 'Show All Admins',
  'action' => 'Action',
  'confirmAction' => 'Confirm Action',
  'areYouSureYouWantToDeleteThisadmin' => 'Are You Sure You Want To Delete This  Admin',
  'close' => 'Close',
  'delete' => 'Delete',
  'addCarService' => 'Add Car Service',
  'carServices' => 'Car Services',
  'addCarServices' => 'Add Car Services',
  'carServiceName' => 'Name',
  'updateCarService' => 'Update Car Service',
  'updateCarServices' => 'Update Car Services',
  'CarServiceName' => 'CarServiceName',
  'carServicesOf ' => 'Car Services Of',
  'carService' => 'Car Service',
  'carServicesId' => 'ID',
  'carServicesName' => 'Name',
  'areYouSureYouWantToDeleteThisCarService' => 'Are You Sure You Want To Delete This Car Service',
  'Nothing to show' => 'Nothing To Show',
  'addCategory' => 'Add Category',
  'categories' => 'Categories',
  'categoryName' => 'Name',
  'categoryHasSubs' => 'Category Has Subs',
  'updateCategory' => 'Update Category',
  'showAllCategories' => 'show All Categories',
  'categoryId' => 'ID',
  'addCity' => 'Add City',
  'cities' => 'Cities',
  'addCitys' => 'Add Cities',
  'cityName' => 'Name',
  'cityIsActive' => 'Is Active',
  'updateCity' => 'Update City',
  'updateCitys' => 'Update Cities',
  'citiesOf ' => 'Cities Of',
  'citiesId' => 'ID',
  'citiesName' => 'Name',
  'citiesIsActive' => 'Is Active',
  'areYouSureYouWantToDeleteThisCity' => 'Are You Sure You Want To Delete This City',
  'addDistrict' => 'Add District',
  'addDistricts' => 'Add DistrictS',
  'districtName' => 'Name',
  'districtIsActive' => 'Is Active',
  'updateDistrict' => 'Update District',
  'updateDistricts' => 'Update Districts',
  'showAllDistrict' => 'Show All District',
  'districtId' => 'ID',
  'districtCityCount' => 'City Count',
  'addLanguage' => 'Add Language',
  'languages' => 'Languages',
  'addLanguages' => 'Add LanguageS',
  'languageName' => 'Name',
  'languageShort' => 'Short',
  'languageDirection' => 'Language Direction',
  'languageIsActive' => 'Is Active',
  'languageIsDefault' => 'Is Default',
  'editLanguage' => 'Edit Language',
  'editLanguages' => 'Edit Languages',
  'showAllLanguages' => 'Show All Languages',
  'languageId' => 'ID',
  'areYouSureYouWantToDeleteThisLanguage' => 'Are You Sure You Want To Delete This Language',
  'Status' => 'Status',
  'Pending' => 'Pending',
  'Accepted' => 'Accepted',
  'Canceled' => 'Canceled',
  'Confirmed' => 'Confirmed',
  'Done' => 'Done',
  'invoice' => 'Invoice',
  'allInvoices' => 'All Invoices',
  'Completed' => 'Completed',
  'orderUserName' => 'User Name',
  'orderProviderName' => 'Provider Name',
  'orderType' => 'Type',
  'orderStatus' => 'Status',
  'orderTotalAmount' => 'Total Amount',
  'orderProviderAmount' => 'Provider Amount',
  'orderBookedAt' => 'Booked At',
  'orderIvStatus' => 'Status',
  'orderInvoiceLink' => 'Invoice Link',
  'paid' => 'Paid',
  'User Details' => 'User Details',
  'Provider Details' => 'Provider Details',
  'Service Details' => 'Service Details',
  'Price Details' => 'Price Details',
  'type' => 'Type',
  'Offer' => 'Offer',
  'Service' => 'Service',
  'Start Time' => 'Start Time',
  'Starts At' => 'Starts At',
  'Ends At' => 'Ends At',
  'Need Materials' => 'Need Materials',
  'true' => 'True',
  'false' => 'False',
  'Sub-Total' => 'Sub-Total',
  'Material Price' => 'Material Price',
  'Deposit' => 'Deposit',
  'Commission' => 'Commission',
  'Tax' => 'Tax',
  'Tips' => 'Tips',
  'Paid Amount' => 'Paid Amount',
  'Total' => 'Total',
  'showAllProviders' => 'Show All Providers',
  'providerAccountType' => 'Account Type',
  'providerOrders' => 'Orders',
  'providerWallet' => 'Wallet',
  'providerRegisterdAt' => 'Registerd At',
  'providerStatus' => 'Status',
  'pendingProviders' => 'Pending Providers',
  'showAllPendingProviders' => 'Show All Pending Providers',
  'Dashboard' => 'Dashboard',
  'General_details' => 'General Details',
  'addRole' => 'Add Role',
  'roleName' => 'Name',
  'save' => 'Save',
  'editRole' => 'Edit Role',
  'showAllRole' => 'Show All Role',
  'roleKey' => 'Key',
  'addService' => 'Add Service',
  'services' => 'Services',
  'addServices' => 'Add Services',
  'addservice' => 'Add Service',
  'serviceName' => 'Name',
  'serviceCategory' => 'Service Category',
  'chooseCategory' => 'Choose Category',
  'serviceRanking' => 'Ranking',
  'serviceIsActive' => 'Is Active',
  'serviceIsHome' => 'Is Home',
  'showAllservices' => 'Show All Services',
  'serviceId' => 'ID',
  'areYouSureYouWantToDeleteThisService' => 'Are You Sure You Want To Delete This Service',
  'configuration' => 'Configuration',
  'configurations' => 'Configurations',
  'UpdateConfiguration' => 'Update Configuration',
  'contactInfo' => 'Contact Info',
  'contactInfos' => 'Contact Info',
  'ContactInfoUpdate' => 'Contact Info Update',
  'addSkill' => 'Add Skill',
  'skills' => 'Skills',
  'addSkills' => 'Add SkillS',
  'skillName' => 'Name',
  'updateSkill' => 'Update Skill',
  'updateSkills' => 'Update Skills',
  'showAllSkill' => 'Show All Skill',
  'skillId' => 'ID',
  'areYouSureYouWantToDeleteThisSkill' => 'Are You Sure You Want To Delete This Skill',
  'addSlider' => 'Add Slider',
  'editSlider' => 'Edit Slider',
  'sliders' => 'Sliders',
  'showAllSliders' => 'Show All Sliders',
  'sliderImage' => 'Image',
  'sliderClickable' => 'Clickable',
  'slideIsActive' => 'Is Active',
  'addSocial' => 'Add Social',
  'socials' => 'Socials',
  'addSocials' => 'Add Socials',
  'socialName' => 'Name',
  'socialUrl' => 'Url',
  'editSocial' => 'Edit Social',
  'editSocials' => 'Edit Socials',
  'showAllSocials' => 'Show All Socials',
  'socialId' => 'ID',
  'areYouSureYouWantToDeleteThisSocial' => 'Are You Sure You Want To Delete This Social',
  'addSubCategory' => 'Add Sub Category',
  'subCategories' => 'sub Categories',
  'addSubCategorys' => 'Add Sub Categories',
  'subCategoryName' => 'Name',
  'updateSubCategory' => 'Update Sub Category',
  'updateSubCategorys' => 'Update Sub Categories',
  'SubCategoryName' => 'Name',
  'subCategoriesOf ' => 'Sub Categories Of',
  'subCategory' => 'sub Category',
  'subCategoriesId' => 'ID',
  'subCategoriesName' => 'Name',
  'areYouSureYouWantToDeleteThisSubCategory' => 'Are You Sure You Want To Delete This Sub Category',
  'users' => 'Users',
  'showAllUsers' => 'Show All Users',
  'userName' => 'Name',
  'userAccountType' => 'Account Type',
  'userOrders' => 'Orders',
  'userWallet' => 'Wallet',
  'userRegisterdAt' => 'Registerd At',
  'userStatus' => 'Status',
  'Discount' => 'Discount',
  'profileDetails' => 'Profile Details',
  'phone' => 'Phone',
  'email' => 'Email',
  'gender' => 'Gender',
  'male' => 'Male',
  'female' => 'Female',
  'accountType' => 'Account Type',
  'city' => 'City',
  'district' => 'District',
  'banned' => 'Banned',
  'dicount_list' => 'Discount List',
  'add_discount' => 'Add Discount',
  'discount_percentage' => 'Discount Percentage',
  'from_date' => 'From Date',
  'to_date' => 'To date',
  'actions' => 'Action',
  'inactive' => 'Inactive',
  'edit' => 'Edit',
  'discount_model_update_title' => 'Update Discount',
  'discount_from_date' => 'From Date',
  'discount_to_date' => 'To Date',
  'Nothing To Show' => 'Nothing To Show',
  'discount_model_add_title' => 'Add Discount',
  'Mark as read' => 'Mark as read',
  'settings' => 'Settings',
  'logout' => 'logout',
  'administration' => 'Administration',
  'roles' => 'Roles',
  'showAllServices' => 'Show All Services',
  'showAllReviews' => 'Show All Reviews',
  'accountings' => 'Accountings',
  'showAccountings' => 'Show Accountings',
  'providersInvoices' => 'Providers Invoices',
  'usersInvoices' => 'Users Invoices',
  'financial Consultations' => 'Financial Consultations',
  'index financial Consultations' => 'Index Financial Consultations',
  'user name' => 'User Name',
  'date' => 'date',
  'legal Advice' => 'Legal Advice',
  'index legal Advice' => 'Index Legal Advice',
  'addFaq' => 'Add Faq',
  'faqs' => 'faqs',
  'add faqs' => 'add faqs',
  'faqTitle' => 'Title',
  'faqText' => 'Text',
  'userType' => 'User Type',
  'all' => 'all',
  'user' => 'user',
  'provider' => 'Provider',
  'company' => 'company',
  'updateFaq' => 'Update Faq',
  'updateFaqs' => 'Update Faqs',
  'FaqTitle' => 'Title',
  'FaqText' => 'Text',
  'add faq' => 'add faq',
  'faqsId' => 'ID',
  'faqs title' => 'title',
  'documents' => 'Documents',
  'providerDocuments' => 'Provider Documents',
  'idDocument' => 'ID Document',
  'noDocumentUploaded' => 'No document has been uploaded yet',
  'upload' => 'Upload',
  'document_uploaded_successfully' => 'Document uploaded successfully',
  'provider_info_not_found' => 'Provider information not found',
  'acceptedFileTypes' => 'Accepted file types: JPG, PNG, WEBP, PDF (max 5MB)',
  'viewDocument' => 'View Document',
  'New Section' => 'New Section',
  'Landing About' => 'About',
  'Content ' => 'Content',
  'Icon' => 'Icon',
  'Save' => 'Save',
  'New Feature' => 'New Feature',
  'Edit Feature' => 'Edit Feature',
  'Edit Image' => 'Edit Image',
  'Image' => 'Image',
  'Landing Feature' => 'Feature',
  'Feature Image' => 'Feature Image',
  'Title' => 'Title',
  'Text ' => 'Text',
  'Edit Section' => 'Edit Section',
  'Landing How Its Work' => 'How Its Work',
  'Landing Intro' => 'Intro',
  'New Screen' => 'New Section',
  'App Screen' => 'App Screen',
  'New Image' => 'New Image',
  'Transactions' => 'Transactions',
  'showAllTransactions' => 'Show All Transactions',
  'Payment Transaction' => 'Payment Transaction',
  'pages' => 'Pages',
  'privacy policy' => 'Privacy Policy',
  'terms of service' => 'Terms Of Service',
  'about us' => 'about us',
  'Consultation Request' => 'Consultation Request',
  'financial consultations' => 'Financial Consultations',
  'legal consultations' => 'Legal Consultations',
  'Landing' => 'Landing',
  'about' => 'about',
  'intro' => 'Intro',
  'screen' => 'Screen',
  'feature' => 'Feature',
  'how its work' => 'How its work',
  'Admins' => 'Admins',
  'Users' => 'Users',
  'Clients' => 'Clients',
  'Providers' => 'Providers',
  'Companies' => 'Companies',
  'Pending Providers' => 'Pending Providers',
  'createUser' => 'Create User',
  'Services' => 'Services',
  'Orders' => 'Orders',
  'Completed Orders' => 'Completed Orders',
  'Confirmed Orders' => 'Confirmed Orders',
  'Back' => 'Back',
  'Notifications Details' => 'Notifications Details',
  'Body' => 'Body',
  'Created At' => 'Created At',
  'Sending bulk notifications' => 'Sending bulk notifications',
  'Customize sending notifications' => 'Customize sending notifications',
  'Send To All' => 'Send To All',
  'ALL Type' => 'All Type',
  'All District' => 'All District',
  'All Cities' => 'All Cities',
  'Type' => 'Type',
  'Send' => 'Send',
  'Show Details' => 'Show Details',
  'Notification' => 'Notification',
  'Send Notification' => 'Send Notification',
  'Notification History' => 'Notification History',
  'perPage' => 'Number of items per page',
  'info' => 'Showing {start} to {end} of {rows} items',
  'placeholder' => 'Search',
  'terms of payment' => 'terms of payment',
  'documentsAvailableInProviderDetails' => 'Documents are available in the Provider Details tab',
  'viewProviderDetails' => 'View Provider Details',
  'chooseDistrict' => 'Choose District',
  'providerAddress' => 'Provider Address',
  'providerCity' => 'Provider City',
  'providerDistrict' => 'Provider District',
  'companyInfo' => 'Company Information',
  'companyName' => 'Company Name',
  'companyPhone' => 'Company Phone',
  'companyIdNumber' => 'Company ID Number',
  'companyAddress' => 'Company Address',
  'companyDistrict' => 'Company District',
  'companyCity' => 'Company City',
  'companyIdDocument' => 'Company ID Document',
  'leaveEmptyForAutoRanking' => 'Leave empty for automatic ranking',
  'customerLocations' => 'Customer Locations',
  'locationName' => 'Location Name',
  'longitude' => 'Longitude',
  'latitude' => 'Latitude',
  'addDate' => 'Add Date',
  'noLocationsFound' => 'No locations found',
  'employee' => 'Employee',
  'not_employee' => 'Not Employee',
  'employee_status_updated_successfully' => 'Employee status updated successfully',
  'provider_set_as_employee' => 'Provider has been set as an employee successfully',
  'provider_unset_as_employee' => 'Provider has been unset as an employee successfully',
  'error_updating_status' => 'Error updating employee status',
  'job_applications' => 'Job Applications',
  'all_job_applications' => 'All Job Applications',
  'add_job_application' => 'Add Job Application',
  'job_application_details' => 'Job Application Details',
  'job_title' => 'Job Title',
  'applicant_name' => 'Applicant Name',
  'applicant_email' => 'Applicant Email',
  'applicant_phone' => 'Applicant Phone',
  'application_date' => 'Application Date',
  'resume' => 'Resume',
  'cover_letter' => 'Cover Letter',
  'notes' => 'Notes',
  'under_review' => 'Under Review',
  'interviewed' => 'Interviewed',
  'accepted' => 'Accepted',
  'rejected' => 'Rejected',
  'location_name' => 'Location Name',
  'schedule' => 'Schedule',
  'day' => 'Day',
  'start_time' => 'Start Time',
  'hours' => 'Hours',
  'end_time' => 'End Time',
  'sunday' => 'Sunday',
  'monday' => 'Monday',
  'tuesday' => 'Tuesday',
  'wednesday' => 'Wednesday',
  'thursday' => 'Thursday',
  'friday' => 'Friday',
  'saturday' => 'Saturday',
  'job_application_created_successfully' => 'Job application created successfully',
  'job_application_updated_successfully' => 'Job application updated successfully',
  'job_application_deleted_successfully' => 'Job application deleted successfully',
  'job_application_set_as_employee' => 'Job applicant has been set as an employee successfully',
  'job_application_unset_as_employee' => 'Job applicant has been unset as an employee successfully',
  'no_job_applications_found' => 'No job applications found',
  'view_resume' => 'View Resume',
  'basic_information' => 'Basic Information',
  'applicant_information' => 'Applicant Information',
  'application_details' => 'Application Details',
  'add_schedule' => 'Add Schedule',
  'remove' => 'Remove',
  'edit_job_application' => 'Edit Job Application',
  'view' => 'View',
  'coordinates' => 'Coordinates',
  'select_user' => 'Select User',
  'select_service' => 'Select Service',
  'mobile_job_applications' => 'Mobile Job Applications',
  'pending_applications' => 'Pending Applications',
  'approved_applications' => 'Approved Applications',
  'rejected_applications' => 'Rejected Applications',
  'mobile_job_application_details' => 'Mobile Job Application Details',
  'approve' => 'Approve',
  'reject' => 'Reject',
  'admin_notes' => 'Admin Notes',
  'approval_details' => 'Approval Details',
  'approved_by' => 'Approved By',
  'approved_at' => 'Approved At',
  'user_schedules' => 'User Schedules',
  'provider_schedule' => 'Provider Schedule',
  'order' => 'Order',
  'free_time' => 'Free Time',
  'job_application_approved_successfully' => 'Job application approved successfully',
  'job_application_rejected_successfully' => 'Job application rejected successfully',
  'approve_application' => 'Approve Application',
  'reject_application' => 'Reject Application',
  'confirm_approval' => 'Are you sure you want to approve this application?',
  'confirm_rejection' => 'Are you sure you want to reject this application?',
  'view_user_schedules' => 'View User Schedules',
  'select_user_to_view_schedules' => 'Select a user to view their schedules',
  'description' => 'Description',
  'all_categories' => 'All Categories',
  'category' => 'Category',
  'view_on_map' => 'View on Map',
  'select_provider' => 'Select Provider',
  'individual' => 'Individual',
  'duration' => 'Duration',
  'hour' => 'Hour',
  'meter' => 'Meter',
  'selected_schedules' => 'Selected Schedules',
  'no_schedules_selected' => 'No schedules selected',
  'select_time' => 'Select Time',
  'selected_time' => 'Selected Time',
  'time_conflict' => 'Time Conflict',
  'remove_selected_time' => 'Do you want to remove this selected time?',
  'day_schedule' => 'Day Schedule',
  'time' => 'Time',
  'appointment' => 'Appointment',
  'add_appointment' => 'Add Appointment',
  'set_location' => 'Set Location',
  'search_address' => 'Search Address',
  'address_not_found' => 'Address not found',
  'error_searching_address' => 'Error searching address',
  'please_select_location' => 'Please select a location',
  'unnamed_location' => 'Unnamed Location',
  'conflicting_events' => 'Conflicting Events',
  'please_select_different_time' => 'Please select a different time',
  'order_details' => 'Order Details',
  'provider_schedule_details' => 'Provider Schedule Details',
  'schedule_id' => 'Schedule ID',
  'select_day' => 'Select a day',
  'select_day_to_view_schedule' => 'Select a day to view the schedule',
  'hour_schedule' => 'Hour Schedule',
  'available_slot' => 'Available Slot',
  'location_map' => 'Location Map',
  'service_id_required' => 'Service is required',
  'service_id_exists' => 'Selected service does not exist',
  'location_name_required' => 'Location name is required',
  'latitude_required' => 'Latitude is required',
  'longitude_required' => 'Longitude is required',
  'schedule_required' => 'Schedule is required',
  'schedule_array' => 'Schedule must be an array',
  'schedule_day_required' => 'Day is required for each schedule item',
  'schedule_day_in' => 'Day must be a valid day of the week',
  'schedule_hours_required' => 'Hours are required for each day',
  'schedule_hours_array' => 'Hours must be an array',
  'weekly_schedule_set' => 'Weekly schedule set successfully',
  'daily_schedule_set' => 'Daily schedule set successfully',
  'schedule_conflicts_found' => 'Schedule conflicts found',
  'no_schedule_conflicts' => 'No schedule conflicts found',
  'mobile_job_application_created' => 'Mobile job application created successfully',
  'mobile_job_application_updated' => 'Mobile job application updated successfully',
  'mobile_job_application_deleted' => 'Mobile job application deleted successfully',
  'edit_mobile_job_application' => 'Edit Mobile Job Application',
  'delete_application' => 'Delete Application',
  'confirm_delete' => 'Are you sure you want to delete this application?',
  'only_pending_applications_can_be_updated' => 'Only pending applications can be updated',
  'only_pending_applications_can_be_deleted' => 'Only pending applications can be deleted',
  'schedule_cannot_be_edited' => 'Schedule cannot be edited. If you need to change the schedule, please delete this application and create a new one.',
  'view_schedule' => 'View Schedule',
  'schedule_for' => 'Schedule for',
  'schedule_start_time_required' => 'Start time is required for each schedule item',
  'schedule_start_time_format' => 'Start time must be in HH:MM format',
  'schedule_duration_required' => 'Duration is required for each schedule item',
  'schedule_duration_integer' => 'Duration must be an integer',
  'schedule_duration_min' => 'Duration must be at least 1 hour',
  'schedule_duration_max' => 'Duration cannot exceed 12 hours',
  'at_least_one_day_required' => 'At least one day schedule is required',
  'locationId' => 'id',
  'search' => 'Search',
  'all_types' => 'All Types',
  'locations' => 'Locations',
  'no_locations_found' => 'No locations found',
  'loading' => 'Loading',
  'select_location' => 'Select Location',
  'error_loading_locations' => 'Error loading locations',
  'user_not_found' => 'User not found',
  'user_locations_retrieved_successfully' => 'User locations retrieved successfully',
  'failed_to_retrieve_user_locations' => 'Failed to retrieve user locations',
  'searching' => 'Searching',
  'no_results_found' => 'No results found',
  'please_enter_more_characters' => 'Please enter more characters',
  'not_available' => 'Not available',
  'default_location' => 'Default Location',
  'is_recurring' => 'Recurring Job',
  'recurring_job_description' => 'This job will be automatically renewed each month',
  'end_date' => 'End Date',
  'cancellation_reason' => 'Cancellation Reason',
  'recurring' => 'Recurring',
  'one_time' => 'One Time',
  'until' => 'Until',
  'stop_recurring' => 'Stop Recurring',
  'stop_recurring_job' => 'Stop Recurring Job',
  'stop_recurring_confirmation' => 'Are you sure you want to stop this recurring job? Please provide an end date and reason.',
  'job_application_recurring_stopped' => 'Job application recurring has been stopped successfully.',
  'available_times_for_day' => 'Available times for',
  'add_another_time' => 'Add another time',
  'available' => 'Available',
  'unavailable' => 'Unavailable',
  'conflict_with_order' => 'Conflict with order',
  'conflict_with_job' => 'Conflict with job application',
  'locations_for' => 'Locations for',
  'select_user_first' => 'Please select a user first',
  'include_transportation_in_total' => 'Include Transportation in Total',
  'currency' => 'Currency',
  'back' => 'Back',
  'morning_period' => 'Morning Period',
  'afternoon_period' => 'Afternoon Period',
  'unavailable_times' => 'Unavailable Times',
  'request_information' => 'Request Information',
  'provider_selection' => 'Provider Selection',
  'cost_calculation' => 'Cost Calculation',
  'transportation_costs' => 'Transportation Costs',
  'select_service_provider' => 'Select Service Provider',
  'calculation_type' => 'Calculation Type',
  'hourly_calculation' => 'Hourly Calculation',
  'fixed_amount' => 'Fixed Amount',
  'system_hourly_cost' => 'System Hourly Cost',
  'provider_hourly_cost' => 'Provider Hourly Cost',
  'total_client_amount' => 'Total Client Amount',
  'provider_payment_method' => 'Provider Payment Method',
  'provider_amount' => 'Provider Amount',
  'system_transportation_cost' => 'System Transportation Cost',
  'provider_transportation_cost' => 'Provider Transportation Cost',
  'check_availability' => 'Check Availability',
  'requested_schedule' => 'Requested Schedule',
  'select_provider_and_check' => 'Select a provider and click Check Availability',
  'please_select_provider_first' => 'Please select a provider first',
  'checking_availability' => 'Checking availability...',
  'no_conflicts_found' => 'No conflicts found. The schedule is available.',
  'conflicts_found' => 'Conflicts found with existing schedules',
  'conflict_type' => 'Conflict Type',
  'details' => 'Details',
  'current_provider_schedule' => 'Current Provider Schedule',
  'no_schedules_found' => 'No schedules found for this month',
  'error_checking_availability' => 'Error checking availability',
  'already_selected_in_this_request' => 'Already selected in this request',
  'stop_recurring_warning' => 'Warning: This will stop the job from recurring in future months. This action cannot be undone.',
  'stop_date' => 'Stop Date',
  'stop_date_help' => 'The job will continue until this date, then stop recurring.',
  'stop_reason' => 'Stop Reason',
  'confirm_stop_recurring' => 'Confirm Stop Recurring',
  'error_approving_application' => 'Error approving application. Please try again.',
  'hourly' => 'Hourly',
  'provider_fixed_cost' => 'Provider Fixed Cost',
  'cost' => 'Cost',
  'activate' => 'Activate',
  'deactivate' => 'Deactivate',
  'job_application_status_updated' => 'Job application status updated successfully',
  'view_monthly_schedule' => 'View Monthly Schedule',
  'monthly_schedule' => 'Monthly Schedule',
  'loading_schedule' => 'Loading schedule...',
  'error_loading_schedule' => 'Error loading schedule. Please try again.',
  'filter_by_user' => 'Filter by User',
  'all_users' => 'All Users',
  'filter' => 'Filter',
  'no_records_found' => 'No records found',
  'days_required' => 'Please select at least one day',
  'days_must_be_array' => 'Days must be provided as a list',
  'select_at_least_one_day' => 'Please select at least one day',
  'invalid_day' => 'One or more selected days are invalid',
  'invalid_day_value' => 'Contains an invalid day value',
  'schedule_saved' => 'Schedule saved successfully',
  'schedule_error' => 'Error saving schedule',
  'Sunday' => 'Sunday',
  'Monday' => 'Monday',
  'Tuesday' => 'Tuesday',
  'Wednesday' => 'Wednesday',
  'Thursday' => 'Thursday',
  'Friday' => 'Friday',
  'Saturday' => 'Saturday',
  'create_job_application' => 'Create Job Application',
  'added_schedules' => 'Added Schedules',
  'view_all_schedules' => 'View All Schedules',
  'select_provider_to_see_available_times' => 'Select a provider to see available times',
  'order_conflicts' => 'Order Conflicts',
  'job_application_conflicts' => 'Job Application Conflicts',
  'time_slot_instructions' => 'Select an available time (in blue). Times in gray with a red border conflict with orders, and times with a yellow border conflict with other job applications.',
  'error_getting_selected_time' => 'Error getting selected time',
  'work_location' => 'Work Location',
  'cost_calculation_method' => 'Cost Calculation Method',
  'total_amount_for_client' => 'Total Amount for Client',
  'network_error' => 'Network error occurred. Please check your connection and try again.',
  'no_add_time' => 'No time to add',
  'create' => 'Create',
  'created_at' => 'Created at',
  'no_available_times' => 'No available times',
  'edit_slider_button' => 'Edit',
  'delete_slider_button' => 'Delete',
  'delete_slider_title' => 'Delete Slider',
  'delete_slider_confirmation' => 'Are you sure?',
  'save_slider' => 'Save',
  'once_per_job' => 'Once per job',
  'transportation' => 'Transportation',
  'completed' => 'Completed',
  'incomplete' => 'Incomplete',
  'cancelled' => 'Cancelled',
  'not_executed' => 'Not Executed',
  'task_status_started' => 'Started',
  'task_status_pending' => 'Pending',
  'task_status_ended' => 'Ended',
  'task_status_stopped' => 'Stopped',
  'task_status_cancelled' => 'Cancelled',
  'my_profile' => 'my profile',
  'change_password' => 'Change Password',
  'en' => 'English',
  'ar' => 'Arabic',
  'he' => 'Hebrew',
  'you_have' => 'You have',
  'new_notifications' => 'new notifications',
  'no_new_notifications' => 'No new notifications',
  'profile_photo_updated' => 'Profile photo updated successfully',
  'profile_information_updated' => 'Profile information updated successfully',
  'password_updated' => 'Password updated successfully',
  'dashboard' => 'Dashboard',
  'click_to_change_photo' => 'Click to change photo',
  'verified' => 'Verified',
  'not_verified' => 'Not Verified',
  'edit_profile' => 'Edit Profile',
  'two_factor_auth' => 'Two Factor Auth',
  'profile_information' => 'Profile Information',
  'update_profile_info_text' => 'Update your account\'s profile information and email address',
  'profile_photo' => 'Profile Photo',
  'select_new_photo' => 'Select New Photo',
  'name' => 'Name',
  'email_unverified' => 'Your email address is unverified',
  'resend_verification_email' => 'Click here to re-send the verification email',
  'verification_link_sent' => 'A new verification link has been sent to your email address',
  'save_changes' => 'Save Changes',
  'saving' => 'Saving...',
  'update_password' => 'Update Password',
  'password_security_text' => 'Ensure your account is using a long, random password to stay secure',
  'password_tips' => 'Password Tips',
  'password_tip_1' => 'Use at least 8 characters',
  'password_tip_2' => 'Include uppercase and lowercase letters',
  'password_tip_3' => 'Add numbers and special characters',
  'password_tip_4' => 'Avoid using personal information',
  'current_password' => 'Current Password',
  'new_password' => 'New Password',
  'confirm_password' => 'Confirm Password',
  'updating' => 'Updating...',
  'two_factor_authentication' => 'Two Factor Authentication',
  'two_factor_security_text' => 'Add additional security to your account using two factor authentication.',
  'finish_enabling_2fa' => 'Finish enabling two factor authentication.',
  '2fa_enabled' => 'Two factor authentication is enabled.',
  '2fa_not_enabled' => 'Two factor authentication is not enabled.',
  '2fa_explanation' => 'When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone\'s Google Authenticator application.',
  'scan_qr_code' => 'Scan QR Code',
  '2fa_finish_instruction' => 'To finish enabling two factor authentication, scan the following QR code using your phone\'s authenticator application or enter the setup key and provide the generated OTP code.',
  '2fa_enabled_instruction' => 'Two factor authentication is now enabled. Scan the following QR code using your phone\'s authenticator application or enter the setup key.',
  'setup_key' => 'Setup Key',
  'setup_key_instruction' => 'If you cannot scan the QR code, use this setup key instead:',
  'verification' => 'Verification',
  '2fa_verification_instruction' => 'Enter the 6-digit code from your authenticator app to confirm setup.',
  'recovery_codes' => 'Recovery Codes',
  'recovery_codes_instruction' => 'Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.',
  'copy_to_clipboard' => 'Copy to clipboard',
  'manage_2fa' => 'Manage Two Factor Authentication',
  'enable_2fa' => 'Enable Two Factor Authentication',
  'enabling' => 'Enabling...',
  'regenerate_recovery_codes' => 'Regenerate Recovery Codes',
  'confirm' => 'Confirm',
  'confirming' => 'Confirming...',
  'show_recovery_codes' => 'Show Recovery Codes',
  'cancel' => 'Cancel',
  'disable_2fa' => 'Disable Two Factor Authentication',
  'disabling' => 'Disabling...',
  'profile' => 'Profile',
  'api_tokens' => 'API Tokens',
  'log_out' => 'Log Out',
  'loading_text' => 'Loading...',
  'import_groups' => 'Import groups',
  'find_translations' => 'Find translations in files',
  'enter_target_locale' => 'Enter target locale key',
  'auto_translate_missing' => 'Auto translate missing translations',
  'delete_translation_confirm' => 'Are you sure you want to delete this translation?',
  'current_supported_locales' => 'Current supported locales',
  'remove_locale_confirm' => 'Are you sure to remove this locale and all of data?',
  'notification' => 'Notifications',
  'send_notification' => 'Send Notification',
  'notification_history' => 'Notification History',
  'payment_transaction' => 'Payment Transactions',
  'consultation_request' => 'Consultation Requests',
  'landing' => 'Landing Page',
  'Edit types' => 'Edit Types',
  'error_uploading_photo' => 'Error uploading photo. Please ensure the image is less than 1MB and is in JPG, PNG, or JPEG format. Please try again.',
  'tab_not_found' => 'Tab not found. Please try again.',
  'profile_photo_selected' => 'Photo selected. Click "Save Changes" to update your profile photo.',
  'export' => 'Export',
  'location' => 'Location',
  'service_provider' => 'Service Provider',
  'employee_tasks_report' => 'Employee Tasks Report',
  'coupons' => 'Coupons',
  'all_coupons' => 'All Coupons',
  'start time' => 'startTime',
  'provider financial report' => 'Provider Financial Report',
  'total_hours' => 'total_hours',
  'add_time' => 'added_schedules',
  'available_times_for' => 'available_times_for',
  'basic_info' => 'basic_info',
  'confirm_delete_schedule' => 'confirm_delete_schedule',
  'contract_information' => 'contract_information',
  'customer' => 'Customer',
  'day_invalid' => 'day_invalid',
  'day_required' => 'day_required',
  'endTime' => 'endTime',
  'end_time_next_day_error' => 'end_time_next_day_error',
  'feature_not_implemented' => 'feature_not_implemented',
  'fixed' => 'fixed',
  'hours_exceed_day_error' => 'hours_exceed_day_error',
  'hours_max' => 'hours_max',
  'hours_must_be_integer' => 'hours_must_be_integer',
  'hours_min' => 'hours_min',
  'hours_required' => 'hours_required',
  'loading_locations' => 'loading_locations',
  'meters' => 'meters',
  'month' => 'Month',
  'month_already_used' => 'month_already_used',
  'noSkillsFound' => 'noSkillsFound',
  'noWorkScheduleFound' => 'noWorkScheduleFound',
  'noServicesFound' => 'noServicesFound',
  'noWorkZonesFound' => 'noWorkZonesFound',
  'optional' => 'optional',
  'please_select_duration' => 'please_select_duration',
  'please_select_time' => 'please_select_time',
  'register_success_otp_sent' => 'register_success_otp_sent',
  'save_contract' => 'save_contract',
  'schedule_at_least_one' => 'schedule_at_least_one',
  'schedule_details' => 'schedule_details',
  'schedule_must_be_array' => 'schedule_must_be_array',
  'schedule_overlap_error' => 'schedule_required',
  'search_location' => 'search_location',
  'search_provider' => 'search_provider',
  'search_service' => 'search_service',
  'search_user' => 'search_user',
  'start_time_format' => 'start_time_format',
  'startTime' => 'startTime',
  'start_time_required' => 'start_time_required',
  'this_will_update_all_applications_in_current_filter' => 'this_will_update_all_applications_in_current_filter',
  'time_slots' => 'time_slots',
  'used_months' => 'used_months',
  'customer financial report' => 'Customer Financial Report',
  'all customers' => 'All Customers',
  'year' => 'Year',
  'reset_filter' => 'Reset Filter',
  'total amount' => 'Total Amount',
  'monthly_transportation' => 'Monthly Transportation',
  'total hours' => 'Total Hours',
  'includes_monthly_transportation' => 'Includes Monthly Transportation',
  'once_per_job_application' => 'Once Per Job Application',
  'service_name' => 'Service Name',
  'provider_name' => 'Provider Name',
  'customer_name' => 'Customer Name',
  'hourly_rate' => 'Hourly Rate',
  'select' => 'Select',
  'select_all' => 'Select All',
  'all providers' => 'All Providers',
  'amount' => 'Amount',
  'customer name' => 'Customer Name',
  'provider name' => 'Provider Name',
  'service name' => 'Service Name',
  'end time' => 'End Time',
  'price details' => 'Price Details',
  'customer_task_details' => 'Customer Task Details',
  'service details' => 'Service Details',
  'task_type' => 'Task Type',
  'user details' => 'User Details',
  'need materials:' => 'Need Materials:',
  'provider details' => 'Provider Details',
  'ends at:' => 'Ends At:',
  'h' => 'h',
  'no' => 'No',
  'financially_closed' => 'Financially Closed',
  'view_all_tasks' => 'View All Tasks',
  'payment_method' => 'Payment Method',
  'provider_task_details' => 'Provider Task Details',
  'customer details' => 'Customer Details',
  'view_work_history' => 'View Work History',
  'ends at' => 'Ends At',
  'need materials' => 'Need Materials',
  'financially_open' => 'Financially Open',
  'work_history' => 'Work History',
  'fixed_amount_job_note' => 'Fixed Amount Job Note',
  'job_type' => 'Job Type',
  'fixed_amount_calculation_note' => 'Fixed amount and transportation calculated once per job application per month',
  'total_fixed_amount' => 'Total Fixed Amount',
  'total:' => 'Total:',
  'note:' => 'Note:',
  'fixed_payment_note' => 'Fixed Payment Note',
  'reset' => 'Reset',
  'all_tasks_for_job' => 'All Tasks for Job',
  'no_tasks_found' => 'No Tasks Found',
  'all_providers' => 'All Providers',
  'all_days' => 'All Days',
  'success' => 'Success',
  'error' => 'Error',
  'ok' => 'OK',
  'something_went_wrong' => 'Something went wrong',
  'administrator' => 'Administrator',
  'hebrew' => 'Hebrew',
  'arabic' => 'Arabic',
  'english' => 'English',
  'language' => 'Language',
  'welcome' => 'Welcome',
  'hello' => 'Hello',
  'good_morning' => 'Good Morning',
  'good_evening' => 'Good Evening',
  'today' => 'Today',
  'yesterday' => 'Yesterday',
  'tomorrow' => 'Tomorrow',
  'this_week' => 'This Week',
  'this_month' => 'This Month',
  'this_year' => 'This Year',
  'statistics' => 'Statistics',
  'reports' => 'Reports',
  'analytics' => 'Analytics',
  'summary' => 'Summary',
  'recent' => 'Recent',
  'latest' => 'Latest',
  'new' => 'New',
  'old' => 'Old',
  'previous' => 'Previous',
  'next' => 'Next',
  'first' => 'First',
  'last' => 'Last',
  'yes' => 'Yes',
  'maybe' => 'Maybe',
  'unknown' => 'Unknown',
  'User Invoices' => 'User Invoices',
  'Provider Invoices' => 'Provider Invoices',
  'serviceImage' => 'Service Image',
  'add_coupon' => 'Add Coupon',
  'discount_type' => 'Discount Type',
  'discount_value' => 'Discount Value',
  'usage_limit' => 'Usage Limit',
  'start_date' => 'Start Date',
  'contact_info' => 'Contact Info',
  'Wallet Balance' => 'Wallet Balance',
  'Uploaded At' => 'Uploaded At',
  'Invoice' => 'Invoice',
  'ID' => 'ID',
  'Actions' => 'Actions',
  'Booked At' => 'Booked At',
  'Published At' => 'Published At',
  'Comment' => 'Comment',
  'Rating' => 'Rating',
  'Dates' => 'Dates',
  'Phone' => 'Phone',
  'Email' => 'Email',
  ' TransactionsID' => 'Transaction ID',
  ' TransactionsUserName' => 'User Name',
  ' TransactionsProviderName' => 'Provider Name',
  ' TransactionsAmount' => 'Amount',
  ' TransactionsDate' => 'Date',
  ' TransactionsReason' => 'Reason',
  'Name' => 'Name',
  'Total Amount' => 'Total Amount',
  'TransactionsID' => 'Transaction ID',
  'TransactionsUserName' => 'User Name',
  'TransactionsProviderName' => 'Provider Name',
  'TransactionsAmount' => 'Amount',
  'TransactionsDate' => 'Date',
  'TransactionsReason' => 'Reason',
  'Nothing to show.....' => 'Nothing to show.....',
  'Total :' => 'Total :',
  'All Providers' => 'All Providers',
  'Provider Amount' => 'Provider Amount',
  'Total Price' => 'Total Price',
  'Details' => 'Details',
  'Approve Invoice' => 'Approve Invoice',
  'Privacy policy' => 'Privacy Policy',
  'Delete Section' => 'Delete Section',
  'Delete Feature' => 'Delete Feature',
  'Are you sure ?' => 'Are you sure?',
  'Delete' => 'Delete',
  'Edit' => 'Edit',
  'Upload Invoice' => 'Upload Invoice',
  'Download Invoice' => 'Download Invoice',
  'Invoice File' => 'Invoice File',
  'Delete Question' => 'Delete Question',
  'edit_page' => 'Edit Page',
  'back_to_dashboard' => 'Back to Dashboard',
  'debug_information' => 'Debug Information',
  'try_alternative_submission' => 'Try Alternative Submission',
  'page_updated_successfully' => 'Page updated successfully',
  'about_us' => 'About Us',
  'privacy_policy' => 'Privacy Policy',
  'terms' => 'Terms',
  'terms_of_service' => 'Terms of Service',
  'terms_of_payment' => 'Terms of Payment',
  'page_title' => 'Page Title',
  'page_content_not_available' => 'Page content not available',
  'note' => 'Note',
  'total' => 'Total',
  'financial_close_user' => 'Financial Close User',
  'confirm_financial_close' => 'Confirm Financial Close',
  'financial_close_details' => 'Financial Close Details',
  'transportation_costs_included' => 'Transportation Costs Included',
  'only_unclosed_tasks_included' => 'Only Unclosed Tasks Included',
  'total_amount_to_pay' => 'Total Amount to Pay',
  'close_selected_tasks' => 'Close Selected Tasks',
  'selected_month' => 'Selected Month',
  'moderator' => 'Moderator',
  'id' => 'ID',
  'admin' => 'Admin',
  'Print' => 'Print',
  'PDF' => 'PDF',
  'CSV' => 'CSV',
  'Excel' => 'Excel',
  'Copy' => 'Copy',
  'confirm_payment' => 'Confirm Payment',
  'financial_close_provider' => 'Financial Close Provider',
  'month is required' => 'Month is required',
  'year is required' => 'Year is required',
  'financial_report_retrieved_successfully' => 'Financial report retrieved successfully',
  'unauthorized_access' => 'Unauthorized access',
  'transportation_calculation_note' => 'Transportation costs are calculated once per job application. Fixed amounts are calculated once per job application monthly.',
  'hourly_calculation_note' => 'Hourly amount calculated per task, transportation once per job application',
  'unpaid' => 'Unpaid',
  'show_mobile_payment_button' => 'Show Mobile Payment Button',
  'mobile_payment_enabled' => 'Mobile Payment Enabled',
  'mobile_payment_disabled' => 'Mobile Payment Disabled',
  'customer_id_required' => 'Customer ID is required',
  'customer_not_found' => 'Customer not found',
  'month_required' => 'Month is required',
  'month_must_be_integer' => 'Month must be an integer',
  'month_min_value' => 'Month must be at least 1',
  'month_max_value' => 'Month must not exceed 12',
  'year_required' => 'Year is required',
  'year_must_be_integer' => 'Year must be an integer',
  'year_min_value' => 'Year must be at least 2000',
  'year_max_value' => 'Year must not exceed 2040',
  'customer_financial_close_success' => 'Customer financial closure completed successfully',
  'customer_financial_close_failed' => 'Failed to close customer financial records',
  'no_tasks_found_for_closure' => 'No tasks found for financial closure',
  'no_tasks_found_for_month_year' => 'No tasks found for the specified month and year',
  'tasks_already_closed' => 'Tasks are already financially closed',
  'month_already_closed' => 'The specified month has already been financially closed',
  'no_unclosed_tasks_found' => 'No unclosed tasks found for financial closure',
  'payer_name_required' => 'Payer name is required',
  'payer_name_must_be_string' => 'Payer name must be a string',
  'payer_name_max_length' => 'Payer name must not exceed 255 characters',
  'payment_notes_must_be_string' => 'Payment notes must be a string',
  'payment_notes_max_length' => 'Payment notes must not exceed 1000 characters',
  'grouped_fixed_task' => 'Grouped Fixed Amount Task',
  'individual_task' => 'Individual Task',
  'total_allowed_hours' => 'Total Allowed Hours',
  'worked_hours_this_month' => 'Worked Hours This Month',
  'remaining_hours' => 'Remaining Hours',
  'hours_percentage' => 'Hours Percentage',
  'hours_exceeded_warning' => 'Warning: Hours exceeded the allowed limit!',
  'hours_completed_warning' => 'All allocated hours have been completed.',
  'hours_almost_finished_warning' => 'Warning: Only 10% of hours remaining!',
  'not_specified' => 'Not Specified',
  'fixed_amount_tasks' => 'Fixed Amount Tasks',
  'hourly_tasks' => 'Hourly Tasks',
);

<?php

return array (
  'Notification Read Successfully' => 'Notification Read Successfully',
  'Offer Not Exists' => 'Offer Not Exists',
  'Cannot Book Your Offer' => 'Cannot Book Your Offer',
  'Offer Not Available At Selected Date' => 'Offer Not Available At Selected Date',
  'Data_Retrived' => 'Data_Retrived',
  'Album deleted successfully' => 'Album deleted successfully',
  'Data_Stored' => 'Data_Stored',
  'New Offer Added Successfully' => 'New Offer Added Successfully',
  'Offer Updated Successfully' => 'Offer Updated Successfully',
  'Offer Deleted Successfully' => 'Offer Deleted Successfully',
  'Order Has Been Canceled' => 'Order Has Been Canceled',
  'Order Has Been Approved' => 'Order Has Been Approved',
  'Order Has Been Completed' => 'Order Has Been Completed',
  'Order Tracking Steps Has Been Updated' => 'Order Tracking Steps Has Been Updated',
  'Order Should Be Confirmed' => 'Order Should Be Confirmed',
  'Order Is Not For Cloth Service' => 'Order Is Not For Cloth Service',
  'Order Status Has Been Updated' => 'Order Status Has Been Updated',
  'Enter price for all sub category' => 'Enter price for all sub category',
  'From Feiled Is Required' => 'From Feiled Is Required',
  'Invoice Uploaded Successfully' => 'Invoice Uploaded Successfully',
  'Data_Retrieved' => 'Data_Retrieved',
  'Offer request retrived successfully' => 'Offer request retrived successfully',
  'Invoice Request Has Been Sent' => 'Invoice Request Has Been Sent',
  'Order Sent Successfully To Provider' => 'Order Sent Successfully To Provider',
  'Error In Payment Transaction' => 'Error In Payment Transaction',
  'Cannot Change Acount Type To User' => 'Cannot Change Acount Type To User',
  'Not Allowed To Access This Route' => 'Not Allowed To Access This Route',
  'Waiting Provider to Accept / Cancel Your Order' => 'Waiting Provider to Accept / Cancel Your Order',
  'Provider Accepted Your Order Pay To Confirm' => 'Provider Accepted Your Order Pay To Confirm',
  'Canceled By Provider' => 'Canceled By Provider',
  'Confirmed' => 'Confirmed',
  'Canceled By User' => 'Canceled By User',
  'Mon' => 'Monday',
  'Fri' => 'Friday',
  'New Booking From' => 'New Booking From',
  'Accepted Your Booking' => 'Accepted Your Booking',
  'Canceled Your Booking' => 'Canceled Your Booking',
  'Canceled Booking' => 'Canceled Booking',
  'Confirmed Order And Paid The Deposit' => 'Confirmed Order And Paid The Deposit',
  'New Urgent Request From' => 'New Urgent Request From',
  'Accepted Pricing Request' => 'Accepted Pricing Request',
  'saturday' => 'Saturday',
  'sunday' => 'Sunday',
  'monday' => 'Monday',
  'tuesday' => 'Tuesday',
  'wednesday' => 'Wednesday',
  'thursday' => 'Thursday',
  'friday' => 'Friday',
  'register_success_otp_sent' => 'register_success_otp_sent',
  'pleaseEnterLocationName' => 'Please enter location name',
  'pleaseSelectLocation' => 'Please select location',
  'pleaseEnter' => 'Please enter',
  'deleteLocation' => 'Delete location',
  'deleteLocationConfirmation' => 'Are you sure you want to delete this location?',
  'jobs' => 'Jobs',
  'myJobs' => 'My Jobs',
  'addJob' => 'Add Job',
  'editJob' => 'Edit Job',
  'deleteJob' => 'Delete Job',
  'deleteJobConfirmation' => 'Are you sure you want to delete this job?',
  'jobDetails' => 'Job Details',
  'noJobsFound' => 'No jobs found',
  'selectService' => 'Select Service',
  'selectLocation' => 'Select Location',
  'notes' => 'Notes',
  'schedule' => 'Schedule',
  'startTime' => 'Start Time',
  'hours' => 'Hours',
  'pleaseSetSchedule' => 'Please set schedule',
  'jobApplications' => 'Job Applications',
  'myJobApplications' => 'My Job Applications',
  'noJobApplicationsFound' => 'No job applications found',
  'jobApplicationDetails' => 'Job Application Details',
  'workDays' => 'Work Days',
  'scheduleHours' => 'Schedule Hours',
  'cost' => 'Cost',
  'calendarJobs' => 'Calendar Jobs',
  'jobsFor' => 'Jobs for',
  'noJobsForThisDay' => 'No jobs for this day',
  'order' => 'Order',
  'jobApplication' => 'Job Application',
  'orderNumber' => 'Order Number',
  'start' => 'Start',
  'end' => 'End',
  'approved' => 'Approved',
  'absent' => 'Absent',
  'notStarted' => 'Not Started',
  'ended' => 'Ended',
  'confirmed' => 'Confirmed',
  'notWithinWorkLocation' => 'Not within work location',
  'taskStartedSuccessfully' => 'Task started successfully',
  'taskEndedSuccessfully' => 'Task ended successfully',
  'success' => 'Success',
  'workLocations' => 'Work Locations',
  'noWorkLocationsFound' => 'No work locations found',
  'enterLocationName' => 'Enter location name',
  'locationName' => 'Location Name',
  'longitude' => 'Longitude',
  'latitude' => 'Latitude',
  'viewOnMap' => 'View on map',
  'warning' => 'Warning',
  'areYouSureYouWantToMakeThisAction' => 'Are you sure you want to make this action?',
  'financialReport' => 'Financial Report',
  'filterByPeriod' => 'Filter by period',
  'month' => 'Month',
  'year' => 'Year',
  'totalTransportation' => 'Total Transportation',
  'totalAmount' => 'Total Amount',
  'grandTotal' => 'Grand Total',
  'totalTasks' => 'Total Tasks',
  'totalHours' => 'Total Hours',
  'noTasksFound' => 'No tasks found',
  'showAllTasks' => 'Show all tasks',
  'hideAllTasks' => 'Hide all tasks',
  'executionDate' => 'Execution Date',
  'time' => 'Time',
  'location' => 'Location',
  'minutes' => 'Minutes',
  'unpaidAmount' => 'Unpaid Amount',
  'payNow' => 'Pay Now',
  'closedFinancially' => 'Closed Financially',
  'unClosedFinancially' => 'Not Closed Financially',
  'sent_at' => 'Sent at',
  'is_read' => 'Is Read',
  'paymentPolicy' => 'paymentPolicy',
   "name"=> "Name",
    "login"=> "Login",
    "yourPhoneNumberOrEmail"=> "Your Phone Number Or E-Mail",
    "password"=> "Password",
    "rememberMe"=> "Remember Me",
    "forgetPassword"=> "Forget Password ?",
    "signup"=> "Sign Up",
    "selectedLanguage"=> "Selected Language",
    "skip"=> "Skip",
    "aboutUs"=> "About Us",
    "forMoreInfoContactUs"=> "For More Info Contact Us => ",
    "email"=> "Email Address",
    "phone"=> "Phone",
    "popularServices"=> "Preferred Services",
    "services"=> "Services",
    "congratulation"=> "Congratulation",
    "welcomeToHomePage"=> "Welcome to Vish Vish",
    "colsultationRequest"=> "Colsultation Request",
    "askForYourAdvice"=> "Ask For An Advice",
    "financialConsulting"=> "Financial Consulting",
    "legalAdvice"=> "Legal Advice",
    "faq"=> "FAQ",
    "viewAll"=> "View All",
    "home"=> "Home",
    "myOrders"=> "My Orders",
    "packages"=> "Packages",
    "languages"=> "Languages",
    "pending"=> "Pending",
    "accepted"=> "Accepted",
    "oldOrders"=> "Past orders",
    "notifications"=> "Notifications",
    "privacyPolicy"=> "Privacy Policy",
    "search"=> "Search",
    "createAnAccount"=> "Create An Account",
    "fullName"=> "Full Name",
    "yourEmail"=> "Your E-Mail",
    "yourPhoneNumber"=> "Your Phone Number",
    "type"=> "Type",
    "user"=> "Customer",
    "provider"=> "Provider",
    "company"=> "Company",
    "termsOfService"=> "Terms Of Service",
    "verificationCode"=> "Verification Code",
    "pleaseEnterVerificationCode"=> "The Verification Code Sent To Your Phone",
    "resendCode"=> "Resend Code",
    "submit"=> "Submit",
    "companyName"=> "Company Name",
    "chooseArea"=> "Choose Area",
    "area"=> "Area",
    "chooseCity"=> "Choose City",
    "city"=> "City",
    "companyAddress"=> "Company Address",
    "companyNumber"=> "Company Number",
    "companyId"=> "Company Id",
    "idFile"=> "ID File",
    "companyPicture"=> "Company Picture",
    "myProfile"=> "My Profile",
    "myBooking"=> "My Booking",
    "wallet"=> "Wallet",
    "language"=> "Language",
    "support"=> "Support",
    "logout"=> "Logout",
    "termsAndConditions"=> "Terms And Conditions",
    "details"=> "Details",
    "confirm"=> "Confirm",
    "providerName"=> "Provider Name",
    "providerAddress"=> "Provider Address",
    "providerNumber"=> "Provider Number",
    "providerId"=> "Provider ID",
    "providerPicture"=> "Provider Picture",
    "selectedDay"=> "Select Day",
    "done"=> "Done",
    "address"=> "Address",
    "requestSentSuccessfully"=> "Your Request Sent Successfully",
    "agreeWithTermsAndPrivacy"=> "I Agree With Privacy Policy And Terms",
    "service"=> "Service",
    "howManyHours"=> "How Long For?",
    "hour"=> "Hours",
    "whatTime"=> "What Time",
    "pay"=> "Pay",
    "cancelAllAppointments"=> "Cancel All Appointments",
    "serviceType"=> "Service Type",
    "frequency"=> "Frequency",
    "duration"=> "Duration",
    "cleaningMaterial"=> "Cleaning Material",
    "paymentDetails"=> "Payment Details",
    "finalPrice"=> "Final Price",
    "deposit20"=> "Deposit (20%)",
    "status"=> "Status => ",
    "payment"=> "Payment",
    "payTheRestOfTheAmount"=> "Pay The Rest Of The Amount=>",
    "next"=> "Next",
    "cancel"=> "Cancel",
    "addCard"=> "Add Card",
    "weAccept"=> "We Accept",
    "rememberThisCard"=> "Remember This Card",
    "rememberThisCardText"=> "VishVIsh Will Securely App This Card For A Faster Payment Experience Your Cvv Number Will Not Be Stored",
    "paymentCompeletedSuccessfully"=> "Payment Completed Successfully",
    "rateYourProvider"=> "Rate The Service",
    "writeComment"=> "Write A Comment",
    "rate"=> "Rate",
    "yes"=> "Yes",
    "no"=> "No",
    "cancelRequestMessage"=> "Are You Sure You Want To Cancel Request?",
    "ifYouCancelYouLoseMoney"=> "If You Choose To Cancel, Your Deposit Will Be Forfeited",
    "orderCanceledSuccessfully"=> "The cancellation of your order was successful",
    "cancelThisAppointment"=> "Cancel This Appointment",
    "orderDetails"=> "Order Details",
    "paymentMethod"=> "Payment Method",
    "cash"=> "Cash",
    "deposit"=> "Deposit",
    "total"=> "Total",
    "pleaseEnterPhoneNumber"=> "Enter Your Phone Number To Receive a Verification Code.",
    "phoneNumber"=> "Phone Number",
    "didntreceiverotp"=> "Didn't Receive An Otp?",
    "newPassword"=> "New Password",
    "passwordChangedSuccessfully"=> "You Password Has Been Successfully Changed",
    "workingTimes"=> "Working Times",
    "holidays"=> "Holidays",
    "makeRequest"=> "Make A Request",
    "skills"=> "Skills => ",
    "workAreas"=> "Work Areas => ",
    "salary"=> "Salary => ",
    "days"=> "Days",
    "open"=> "Open",
    "close"=> "Close",
    "from"=> "From",
    "to"=> "To",
    "reviews"=> "Reviews",
    "changePassword"=> "Change Password",
    "gender"=> "Gender",
    "age"=> "Age",
    "save"=> "Save",
    "providerPackages"=> "Provider Packages",
    "urgentRequest"=> "Urgent Request",
    "today"=> "Today",
    "iNeedMaterial"=> "With Materials",
    "howManyTimes"=> "How frequently will you require this service?",
    "onece"=> "Once",
    "weekly"=> "Weekly",
    "monthly"=> "Monthly",
    "note"=> "Note",
    "selectDays"=> "Select One Or More Days For The Services?",
    "mon"=> "Monday",
    "tue"=> "Tuesday",
    "wed"=> "Wednesday",
    "thu"=> "Thursday",
    "fri"=> "Friday",
    "sat"=> "Saturday",
    "sun"=> "Sunday",
    "providerWillContactYou"=> "The Provider Will Reach Out To You To Verify Your Request.",
    "gotoOrderDetialsPage"=> "Go To Your Order Details",
    "pleaseEnterFullName"=> "Full Name",
    "pleaseEnterEmail"=> "Email Address",
    "pleaseEnterPhone"=> "Phone Number",
    "pleaseEnterPassword"=> "Password",
    "pleaseEnterYourAddress"=> "Enter Your Address",
    "pleaseChooseCity"=> "Choose City",
    "pleaseChooseArea"=> "Choose Area",
    "pleaseEnterProviderName"=> "Provider Name",
    "pleaseEnterProviderAddress"=> "Enter Provider Address",
    "pleaseEnterProviderPhoneNumber"=> "Provider Phone Number",
    "pleaseEnterProviderIdNumber"=> "Enter Provider Id Number",
    "pleaseEnterChooseProviderImage"=> "Select Provider Image",
    "pleaseEnterCompanyName"=> "Provide Company Name",
    "pleaseEnterCompanyAddress"=> "Enter Company Address",
    "pleaseEnterCompanyPhoneNumber"=> "Enter The Company Phone Number",
    "pleaseEnterCompanyIdNumber"=> "Add Company Registration Number",
    "pleaseEnterChooseCompanyImage"=> "Select Corporate Identity",
    "pleaseEnterEmailOrPassword"=> "Email and password",
    "pleaseInsertCode"=> "Enter OTP Code First",
    "pleaseInsertCorrectCode"=> "Enter Correct OTP Code",
    "newPasswordConfirmation"=> "Confirm New Password",
    "pleaseInsertPassword"=> "Insert New Password",
    "pleaseInsertPasswordConfirmation"=> "Confirm New Password",
    "yourBirthDate"=> "Date Of Birth",
    "pleaseInsertYourBirthDate"=> "Choose Your Birth Date",
    "ok"=> "OK",
    "add"=> "Add",
    "editSchduel"=> "Edit Schedule",
    "schduelYourOrders"=> "Set Your Schedule",
    "failedToLoad"=> "Failed To Load Data Please Try Again",
    "deleteWorkingTime"=> "Delete Working Time",
    "deleteWorkingTimeMessage"=> "Are You Sure To Delete This Working Time",
    "fromDateHoliday"=> "Choose The Beginning Date Of Holiday",
    "toDateHoliday"=> "Choose The End Date Of Holiday",
    "deleteHoliday"=> "Delete Holiday",
    "deleteHolidayMessage"=> "Are You Sure You Need To Delete This Holiday",
    "pleaseChooseDaysFirst"=> "Select Days",
    "pricing"=> "Pricing",
    "addYourPriceForEachCategory"=> "Add Your Price For Each Category",
    "news"=> "New +",
    "addNewService"=> "Add New Service",
    "materialPrice"=> "Material price",
    "price"=> "Price",
    "sofaType"=> "Sofa Type",
    "withTax"=> "With Tax",
    "withoutTax"=> "Without Tax",
    "deleteService"=> "Delete Service",
    "deleteServiceMessage"=> "Are You Sure To Delete This Service ?",
    "offers"=> "Offers",
    "myOffers"=> "My Offers",
    "bookNow"=> "Book Now",
    "addOffer"=> "Add Offer",
    "offerDetails"=> "Offer Details",
    "averageTime"=> "Average Time",
    "description"=> "Description",
    "deleteOffer"=> "Delete Offer",
    "deleteOfferMessage"=> "Are You Sure To Delete This Offer ?",
    "edit"=> "Edit",
    "delete"=> "Delete",
    "isActive"=> "Active",
    "male"=> "Male",
    "female"=> "Female",
    "editService"=> "Edit Service",
    "newOffers"=> "New offers",
    "onlyFor"=> "Only For",
    "ils"=> "ILS",
    "providerAvilableTimes"=> "Provider Available Time",
    "howManyMeters"=> "How Many Meters?",
    "pleaseWait"=> "Please Wait",
    "calculatePrice"=> "Please Wait While Calculate Price",
    "approveThisOrder"=> "Approve This Order",
    "orderApprovedSuccessfully"=> "Order Successfully Approved",
    "compeleted"=> "Compeleted",
    "orderCompeletedSuccessfully"=> "Order Compeleted Successfully",
    "meter"=> "Meter",
    "sendToAll"=> "Send To All",
    "message"=> "Message",
    "sofas"=> "Choose The Number Of Each Type",
    "everyOne"=> "Every One",
    "noAvilableTimes"=> "No Appointments Available",
    "submitNewOffer"=> "Add New Offer",
    "trackYourOrder"=> "Track Your Order",
    "uploadInvoice"=> "Upload Invoice",
    "orderSchedule"=> "Order Schedule",
    "startsAt"=> "Starts At",
    "endsAt"=> "Ends At",
    "addTip"=> "Tip",
    "payDeposit"=> "Pay Deposit",
    "requestInvoice"=> "Request Invoice",
    "amount"=> "Amount",
    "requestExtraTime"=> "Request For Extra Time",
    "theRequired"=> "The Required",
    "requestedExtraTime"=> "Requests Extra Time",
    "areYouAccept"=> "Do You Agree?",
    "accept"=> "Accept",
    "reject"=> "Reject",
    "showInvoice"=> "Show Invoice",
    "date"=> "Date",
    "count"=> "The Count",
    "bookingTime"=> "Appointment Time",
    "noAvilableProviders"=> "No providers currently available",
    "tips"=> "Not Paid",
    "unPaidOrders"=> "UN-Paid Orders",
    "more"=> "More",
    "meterPrice"=> "Price Per Meter",
    "hourPrice"=> "Price per Hour",
    "hoursNumber"=> "Number Of Hours",
    "metersNumber"=> "How Many Meters?"
);

@extends('layouts.content')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-0">
                    <div class="row g-0">
                        <!-- Left Side - Service Details -->
                        <div class="col-md-7 border-end">
                            <div class="p-4">
                                <!-- Page Title -->
                                <div class="text-center mb-4">
                                    <h4 class="text-primary">
                                        <i class="bi bi-person-fill me-2"></i>{{ __('message.customer_task_details') }}
                                    </h4>
                                    <div class="border-bottom pb-2 mb-3"></div>
                                </div>

                                <!-- Task Type Header -->
                                <div class="alert alert-info mb-4">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-info-circle-fill me-2"></i>
                                        <div>
                                            <h6 class="mb-0">{{ __('message.task_type') }}: <strong>{{ $task->jobApplication && $task->jobApplication->service ? $task->jobApplication->service->name : 'N/A' }}</strong></h6>
                                            <small>{{ __('message.calculation_type') }}: <strong>{{ $task->calculation_type == 'fixed' ? __('message.fixed_amount') : __('message.hourly') }}</strong></small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Price Details Section -->
                                <div class="mb-5">
                                    <h5 class="text-center mb-4">
                                        <i class="bi bi-currency-dollar me-1"></i> {{ __('message.price details') }}
                                    </h5>
                                    <div class="row">
                                        <div class="col-12">
                                            <table class="table table-striped">
                                                <tbody>
                                                    <tr>
                                                        <td class="text-muted" width="40%">{{ __('message.calculation_type') }}:</td>
                                                        <td class="fw-bold">{{ $task->calculation_type == 'fixed' ? __('message.fixed_amount') : __('message.hourly') }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-muted">{{ __('message.system_hourly_cost') }}:</td>
                                                        <td class="fw-bold text-center">
                                                            @if($task->calculation_type != 'hourly')
                                                                <span class="text-danger fw-bold">-</span>
                                                            @else
                                                                {{ number_format($task->system_hourly_cost ?? 0, 2) }}
                                                            @endif
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-muted">{{ __('message.hours') }}:</td>
                                                        <td class="fw-bold">{{ number_format($task->duration_minutes / 60, 2) }}</td>
                                                    </tr>
                                                    @if($task->calculation_type == 'hourly')
                                                        <tr>
                                                            <td class="text-muted">{{ __('message.system_actual_cost') }}:</td>
                                                            <td class="fw-bold">
                                                                {{ number_format(($task->duration_minutes / 60) * ($task->system_hourly_cost ?? 0), 2) }}
                                                            </td>
                                                        </tr>
                                                    @else
                                                        <tr>
                                                            <td class="text-muted">{{ __('message.system_fixed_cost') }}:</td>
                                                            <td class="fw-bold">
                                                                {{ number_format($task->fixed_amount, 2) }}
                                                            </td>
                                                        </tr>
                                                    @endif
                                                    <tr>
                                                        <td class="text-muted">{{ __('message.monthly_transportation') }}:</td>
                                                        <td class="fw-bold">
                                                            {{ number_format($task->system_transportation_cost, 2) }}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="row mt-3">
                                        <div class="col-12 text-center">
                                            <button type="button" class="btn btn-sm btn-primary me-2" data-bs-toggle="modal" data-bs-target="#workHistoryModal">
                                                <i class="bi bi-clock-history me-1"></i> {{ __('message.view_work_history') }}
                                            </button>

                                            @if($task->calculation_type == 'fixed')
                                            <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#allTasksModal">
                                                <i class="bi bi-list-ul me-1"></i> {{ __('message.view_all_tasks') }}
                                            </button>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Spacing for better layout -->
                                    <div class="row mt-4"></div>
                                </div>

                                <!-- Service Details Section -->
                                <div>
                                    <h5 class="text-center mb-4">
                                        <i class="bi bi-gear me-1"></i> {{ __('message.service details') }}
                                    </h5>
                                    <div class="row">
                                        <div class="col-12">
                                            <table class="table table-striped">
                                                <tbody>

                                                    <tr>
                                                        <td class="text-muted">{{ __('message.address') }}:</td>
                                                        <td class="fw-bold">
                                                            {{ $task->jobApplication && $task->jobApplication->location ? $task->jobApplication->location->name : 'N/A' }}
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-muted">{{ __('message.start time') }}:</td>
                                                        <td class="fw-bold">{{ date('H:i', strtotime($task->start_time)) }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-muted">{{ __('message.ends at') }}:</td>
                                                        <td class="fw-bold">{{ $task->end_time ? date('H:i', strtotime($task->end_time)) : 'N/A' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-muted">{{ __('message.duration') }}:</td>
                                                        <td class="fw-bold">{{ number_format($task->duration_minutes / 60, 2) }} {{ __('message.h') }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-muted">{{ __('message.status') }}:</td>
                                                        <td class="fw-bold">{{ $task->status }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-muted">{{ __('message.need materials') }}:</td>
                                                        <td class="fw-bold">{{ $task->need_materials ? __('message.yes') : __('message.no') }}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Side - User Details -->
                        <div class="col-md-5">
                            <div class="p-4">
                                <h5 class="text-center mb-4">{{ __('message.user details') }}</h5>
                                <div class="text-center mb-4">
                                    <div class="avatar-placeholder mb-3">
                                        <div class="rounded-circle bg-light d-inline-flex justify-content-center align-items-center" style="width: 100px; height: 100px;">
                                            <i class="bi bi-person-fill fa-4x text-secondary" style="font-size: 3rem;"></i>
                                        </div>
                                    </div>
                                    <h4 class="mb-3">{{ $task->user ? $task->user->name : 'N/A' }}</h4>
                                    <div class="d-flex justify-content-center align-items-center mb-2">
                                        <i class="bi bi-telephone-fill me-2 text-primary"></i>
                                        <span class="fw-bold">{{ $task->user && $task->user->phone ? $task->user->phone : 'N/A' }}</span>
                                    </div>
                                    <div class="d-flex justify-content-center align-items-center mb-2">
                                        <i class="bi bi-envelope-fill me-2 text-primary"></i>
                                        <span class="fw-bold">{{ $task->user ? $task->user->email : 'N/A' }}</span>
                                    </div>
                                    <div class="d-flex justify-content-center align-items-center">
                                        <i class="bi bi-gender-ambiguous me-2 text-primary"></i>
                                        <span class="fw-bold">{{ $task->user && $task->user->gender ? __('message.' . $task->user->gender) : 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="border-top p-4">
                                <h5 class="text-center mb-4">{{ __('message.provider details') }}</h5>
                                <div class="text-center mb-4">
                                    <div class="avatar-placeholder mb-3">
                                        <div class="rounded-circle bg-light d-inline-flex justify-content-center align-items-center" style="width: 100px; height: 100px;">
                                            <i class="bi bi-person-workspace text-secondary" style="font-size: 3rem;"></i>
                                        </div>
                                    </div>
                                    <h4 class="mb-3">{{ $task->provider && $task->provider->providerInfo ? $task->provider->providerInfo->name : ($task->provider ? $task->provider->name : 'N/A') }}</h4>
                                    <div class="d-flex justify-content-center align-items-center mb-2">
                                        <i class="bi bi-telephone-fill me-2 text-primary"></i>
                                        <span class="fw-bold">{{ $task->provider && $task->provider->phone ? $task->provider->phone : 'N/A' }}</span>
                                    </div>
                                    <div class="d-flex justify-content-center align-items-center">
                                        <i class="bi bi-briefcase-fill me-2 text-primary"></i>
                                        <span class="fw-bold">{{ __('message.provider') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Back Button -->
                    <div class="row p-3">
                        <div class="col-12">
                            <a href="{{ url()->previous() }}" class="btn btn-secondary">{{ __('message.back') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Work History Modal -->
<div class="modal fade" id="workHistoryModal" tabindex="-1" aria-labelledby="workHistoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="workHistoryModalLabel">
                    <i class="bi bi-clock-history me-1"></i> {{ __('message.work_history') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @php
                    // Get all tasks with the same job_application_id for the selected month and year
                    $relatedTasks = \App\Models\Task::where('job_application_id', $task->job_application_id)
                        ->whereMonth('execution_date', $selectedMonth)
                        ->whereYear('execution_date', $selectedYear)
                        ->orderBy('execution_date')
                        ->orderBy('start_time')
                        ->get();
                @endphp

                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>{{ __('message.date') }}</th>
                                <th>{{ __('message.start time') }}</th>
                                <th>{{ __('message.end time') }}</th>
                                <th>{{ __('message.duration') }}</th>
                                <th>{{ __('message.status') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($relatedTasks as $relatedTask)
                                <tr>
                                    <td>{{ date('d-m-Y', strtotime($relatedTask->execution_date)) }}</td>
                                    <td>{{ date('H:i', strtotime($relatedTask->start_time)) }}</td>
                                    <td>{{ $relatedTask->end_time ? date('H:i', strtotime($relatedTask->end_time)) : 'N/A' }}</td>
                                    <td>{{ number_format($relatedTask->duration_minutes / 60, 2) }} {{ __('message.h') }}</td>
                                    <td>{{ $relatedTask->status }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="text-center">{{ __('message.no_records_found') }}</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                @if($task->calculation_type == 'fixed')
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-info-circle me-2"></i>
                        {{ __('message.fixed_amount_job_note') }}
                    </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('message.close') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- All Tasks Modal for Fixed Amount Jobs -->
<div class="modal fade" id="allTasksModal" tabindex="-1" aria-labelledby="allTasksModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="allTasksModalLabel">
                    <i class="bi bi-list-ul me-1"></i> {{ __('message.all_tasks_for_job') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @php
                    // Get all tasks with the same job_application_id for the selected month and year
                    $allFixedTasks = \App\Models\Task::where('job_application_id', $task->job_application_id)
                        ->where('calculation_type', 'fixed')
                        ->whereMonth('execution_date', $selectedMonth)
                        ->whereYear('execution_date', $selectedYear)
                        ->with(['user', 'provider', 'jobApplication.service'])
                        ->orderBy('execution_date')
                        ->orderBy('start_time')
                        ->get();

                    // Calculate total amount (only once for fixed amount)
                    $totalFixedAmount = $task->fixed_amount;
                @endphp

                <div class="alert alert-info mb-4">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        <div>
                            <h6 class="mb-0">{{ __('message.job_type') }}: <strong>{{ $task->jobApplication && $task->jobApplication->service ? $task->jobApplication->service->name : 'N/A' }}</strong></h6>
                            <small>{{ __('message.total_fixed_amount') }}: <strong>{{ number_format($totalFixedAmount, 2) }}</strong></small>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>{{ __('message.date') }}</th>
                                <th>{{ __('message.start time') }}</th>
                                <th>{{ __('message.end time') }}</th>
                                <th>{{ __('message.duration') }}</th>
                                <th>{{ __('message.status') }}</th>
                                <th>{{ __('message.notes') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($allFixedTasks as $fixedTask)
                                <tr>
                                    <td>{{ date('d-m-Y', strtotime($fixedTask->execution_date)) }}</td>
                                    <td>{{ date('H:i', strtotime($fixedTask->start_time)) }}</td>
                                    <td>{{ $fixedTask->end_time ? date('H:i', strtotime($fixedTask->end_time)) : 'N/A' }}</td>
                                    <td>{{ number_format($fixedTask->duration_minutes / 60, 2) }} {{ __('message.h') }}</td>
                                    <td>{{ $fixedTask->status }}</td>
                                    <td>{{ $fixedTask->notes ?: 'N/A' }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">{{ __('message.no_records_found') }}</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-warning mt-3">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    {{ __('message.fixed_amount_calculation_note') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('message.close') }}</button>
            </div>
        </div>
    </div>
</div>
@endsection

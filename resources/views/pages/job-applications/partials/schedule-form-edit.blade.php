@push('styles')
    <style>
        .time-slot-btn {
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            transition: all 0.2s ease-in-out;
            font-weight: 500;
            padding: 8px 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: inline-block;
            text-align: center;
            min-width: 120px;
        }

        .time-slot-btn.available {
            background-color: #f8f9fa;
            color: #495057;
            border-color: #ced4da;
        }

        .time-slot-btn.available:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
        }

        .time-slot-btn.unavailable {
            background-color: #e9ecef;
            color: #adb5bd;
            cursor: not-allowed;
            opacity: 0.7;
            box-shadow: none;
        }

        .time-slot-btn.selected {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
            box-shadow: 0 2px 5px rgba(13, 110, 253, 0.3);
        }

        /* أنماط جديدة لأزرار الوقت مثل الصورة */
        .available-times-container {
            text-align: right;
            margin-top: 10px;
            margin-bottom: 10px;
        }

        .available-times-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .time-slots-wrapper {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: flex-end;
        }

        .time-slot-btn-new {
            background-color: #e7f5ff;
            border: 1px solid #4dabf7 !important;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-block;
            text-align: center;
            min-width: 80px;
            margin: 2px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .time-slot-btn-new:hover {
            background-color: #4dabf7;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .time-slot-btn-new.selected {
            background-color: #0d6efd;
            color: white;
            border-color: #0d6efd !important;
            box-shadow: 0 2px 5px rgba(13, 110, 253, 0.3);
        }

        .time-slot-btn-new.unavailable {
            background-color: #f8f9fa;
            border-color: #dee2e6 !important;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.7;
            box-shadow: none;
        }

        .time-slot-btn-new.stored-slot {
            background-color: #6c757d;
            color: white;
            opacity: 0.7;
            cursor: not-allowed;
        }

        .time-slot-btn-new.unavailable-order {
            background-color: #f8f9fa;
            border-left: 3px solid #dc3545 !important;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.7;
            box-shadow: none;
        }

        .time-slot-btn-new.unavailable-job {
            background-color: #f8f9fa;
            border-left: 3px solid #ffc107 !important;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.7;
            box-shadow: none;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        /* أنماط أزرار الأيام */
        .btn-group {
            flex-wrap: wrap;
        }

        .day-btn {
            flex: 1;
            min-width: 80px;
            margin: 2px;
            border-radius: 4px !important;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .day-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .day-btn.active {
            background-color: #0d6efd;
            color: white;
            border-color: #0d6efd;
            box-shadow: 0 2px 5px rgba(13, 110, 253, 0.3);
        }

        /* تم إزالة حقل التاريخ */

        /* تحسين مظهر حاوية الأوقات */
        #time-slots-container {
            max-height: 300px;
            overflow-y: auto;
            padding: 8px;
            border-radius: 6px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }

        /* تصغير حجم الجدول */
        #scheduleTable {
            font-size: 0.85rem;
        }

        #scheduleTable th,
        #scheduleTable td {
            padding: 0.4rem 0.5rem;
        }

        /* تصغير حجم الأزرار في الجدول */
        #scheduleTable .btn-sm {
            padding: 0.2rem 0.4rem;
            font-size: 0.75rem;
        }

        /* تصغير حجم النص في التنبيهات */
        .alert {
            padding: 0.5rem 0.75rem;
            font-size: 0.85rem;
        }

        /* أنماط جديدة للعنوان والشارة */
        .time-slots-title {
            margin-bottom: 10px;
        }

        .time-slots-legend {
            margin-top: 10px;
            margin-bottom: 15px;
        }

        .legend-color {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 5px;
        }
    </style>
@endpush

<!-- Add CSRF token meta tag in the head -->
@push('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

<div class="card">
    <div class="card-body">
        <h5 class="card-title text-end">{{ __('message.schedule') }}</h5>

        {{-- <div class="row mb-3">
            <div class="col-md-4">
                <label for="duration" class="form-label text-end d-block">{{ __('message.duration') }}</label>
                <input type="number" class="form-control form-control-sm" id="duration" name="duration" value="2"
                    min="1" max="12">
            </div>
            <div class="col-md-5">
                <label class="form-label text-end d-block">{{ __('message.select_day') }}</label>
                @php
                    use Carbon\Carbon;

                    $days = collect([
                        'saturday' => 6,
                        'sunday' => 0,
                        'monday' => 1,
                        'tuesday' => 2,
                        'wednesday' => 3,
                        'thursday' => 4,
                        'friday' => 5,
                    ])->map(function ($dayNumber, $dayName) {
                        return [
                            'name' => __('message.' . $dayName),
                            'value' => $dayName,
                            'number' => $dayNumber,
                        ];
                    });

                    $today = Carbon::now()->timezone(config('app.timezone'));
                @endphp

                <select class="form-select form-select-sm" id="day_select" name="day_select">
                    <option value="">{{ __('message.select_day') }}</option>
                    @foreach ($days as $day)
                        <option value="{{ $day['value'] }}">
                            {{ $day['name'] }}
                        </option>
                    @endforeach
                </select>

                <input type="hidden" id="selected_day" name="selected_day" value="">
            </div>
            <div class="col-md-3">
                <label class="form-label text-end d-block">&nbsp;</label>
                <button type="button" class="btn btn-primary btn-sm w-100" id="check-availability-btn">
                    {{ __('message.check_availability') }}
                </button>
            </div>
        </div> --}}

        {{-- <div class="row mb-3">
            <div class="col-md-12">
                <div class="position-relative">
                    <div id="time-slots-container" class="mt-3">
                        <div class="alert alert-info text-center p-2 small">
                            {{ __('message.select_day_to_view_schedule') }}
                        </div>
                    </div>
                    <div class="loading-overlay d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">{{ __('message.loading') }}...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}

        {{-- <div class="row">
            <div class="col-md-12">
                <button type="button" class="btn btn-success btn-sm float-end" id="add-time-btn" disabled>
                    <i class="bi bi-plus-circle me-1"></i>
                    {{ __('message.add_time') }}
                </button>
            </div>
        </div> --}}

        <!-- Hidden fields for selected time -->
        <input type="hidden" id="selected_start_time" name="selected_start_time" value="">
        <input type="hidden" id="selected_end_time" name="selected_end_time" value="">

        <hr>

        <h6 class="text-end">{{ __('message.added_schedules') }}</h6>
        <div class="table-responsive">
            <table class="table table-bordered" id="scheduleTable">
                <thead>
                    <tr>
                        <th class="text-center">{{ __('message.day') }}</th>
                        <th class="text-center">{{ __('message.time') }}</th>
                        <th class="text-center">{{ __('message.duration') }}</th>
                        {{-- <th class="text-center">{{ __('message.actions') }}</th> --}}
                    </tr>
                </thead>
                <tbody id="scheduleTableBody">
                </tbody>
            </table>
        </div>

        <!-- Hidden container for schedule data -->
        <div id="schedule-container">
            <!-- Schedule items will be added here as hidden inputs -->
        </div>
    </div>
</div>

@push('scripts')
    <!-- Pass schedule data to JavaScript -->
    <script>
        // تمرير بيانات الجدول الزمني من النموذج إلى JavaScript
        var jobApplicationSchedule = {!! json_encode($jobApplication->schedule ?? []) !!};
        console.log('Schedule data passed to JavaScript:', jobApplicationSchedule);
    </script>

    <!-- Load the job-application-form-edit.js file directly -->
    <script src="{{ asset('js/job-application-form-edit.js') }}"></script>
@endpush

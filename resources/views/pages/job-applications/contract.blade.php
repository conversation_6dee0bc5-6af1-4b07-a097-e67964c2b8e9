<style>
    .contract-container {
        background: transparent;
        padding: 0;
        margin: 0;
    }

    .contract-card {
        border: none;
        box-shadow: none;
    }

    .contract-card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .contract-form-control:focus,
    .contract-form-select:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
    }

    .contract-btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .contract-btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0a58ca;
    }
</style>
<div class="contract-container">
    <div class="row">
        <div class="col-12">
            <div class="card contract-card mb-3">
                <div class="card-header contract-card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 fw-bold">{{ __('message.contract_information') }}</h6>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="mb-3">
                        <strong>{{ __('message.month') }}:</strong>
                        <p>{{ isset($contract) && $contract->month ? date('Y-m', strtotime($contract->month)) : date('Y-m') }}
                        </p>
                    </div>

                    <div class="mb-3">
                        <strong>{{ __('message.fixed_amount') }}:</strong>
                        <p>{{ $contract->fixed_amount ?? ($jobApplication->fixed_amount ?? 0) }}</p>
                    </div>

                    @if (($contract->provider_calculation_type ?? ($jobApplication->provider_payment_method ?? 'hourly')) == 'hourly')
                        <div class="mb-3">
                            <strong>{{ __('message.provider_hourly_cost') }}:</strong>
                            <p>{{ $contract->provider_hourly_cost ?? ($jobApplication->provider_hourly_cost ?? 0) }}</p>
                        </div>
                    @else
                        <div class="mb-3">
                            <strong>{{ __('message.provider_fixed_cost') }}:</strong>
                            <p>{{ $contract->provider_fixed_cost ?? ($jobApplication->provider_fixed_cost ?? 0) }}</p>
                        </div>
                    @endif

                    <form action="{{ route('job-applications.store-contract', $jobApplication->id) }}" method="POST"
                        class="mt-4">
                        @csrf
                        <div class="mb-3">
                            <label for="month" class="form-label">{{ __('message.month') }}</label>
                            <div class="row">
                                <div class="col-md-12">
                                    <input type="month" class="form-control" id="month" name="month"
                                        value="{{ old('month', isset($contract) && $contract->month ? date('Y-m', strtotime($contract->month)) : date('Y-m')) }}"
                                        min="{{ date('Y-m') }}" required>
                                    @error('month')
                                        <div class="text-danger mt-1">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            @if (!empty($usedMonths))
                                <div class="mt-2">
                                    <small class="text-muted">{{ __('message.used_months') }}:</small>
                                    <div class="d-flex flex-wrap mt-1">
                                        @foreach ($usedMonths as $usedMonth)
                                            <span class="badge bg-secondary me-1 mb-1">{{ $usedMonth }}</span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="mb-3">
                            <label for="fixed_amount" class="form-label">{{ __('message.fixed_amount') }}</label>
                            <input type="number" step="0.01" class="form-control" id="fixed_amount"
                                name="fixed_amount"
                                value="{{ old('fixed_amount', $contract->fixed_amount ?? ($jobApplication->fixed_amount ?? 0)) }}">
                        </div>

                        <div class="mb-3">
                            <label for="provider_calculation_type"
                                class="form-label">{{ __('message.provider_payment_method') }}</label>
                            <select class="form-select" id="provider_calculation_type" name="provider_calculation_type">
                                <option value="fixed"
                                    {{ old('provider_calculation_type', $contract->provider_calculation_type ?? ($jobApplication->provider_payment_method ?? '')) == 'fixed' ? 'selected' : '' }}>
                                    {{ __('message.fixed') }}</option>
                                <option value="hourly"
                                    {{ old('provider_calculation_type', $contract->provider_calculation_type ?? ($jobApplication->provider_payment_method ?? '')) == 'hourly' ? 'selected' : '' }}>
                                    {{ __('message.hourly') }}</option>
                            </select>
                        </div>

                        <div class="mb-3" id="provider_fixed_cost_container">
                            <label for="provider_fixed_cost"
                                class="form-label">{{ __('message.provider_fixed_cost') }}</label>
                            <input type="number" step="0.01" class="form-control" id="provider_fixed_cost"
                                name="provider_fixed_cost"
                                value="{{ old('provider_fixed_cost', $contract->provider_fixed_cost ?? ($jobApplication->provider_fixed_cost ?? 0)) }}">
                        </div>

                        <div class="mb-3" id="provider_hourly_cost_container">
                            <label for="provider_hourly_cost"
                                class="form-label">{{ __('message.provider_hourly_cost') }}</label>
                            <input type="number" step="0.01" class="form-control" id="provider_hourly_cost"
                                name="provider_hourly_cost"
                                value="{{ old('provider_hourly_cost', $contract->provider_hourly_cost ?? ($jobApplication->provider_hourly_cost ?? 0)) }}">
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-1"></i> {{ __('message.save_contract') }}
                            </button>
                        </div>
                    </form>


                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Toggle provider cost fields based on calculation type
        function toggleProviderCostFields() {
            const calculationType = $('#provider_calculation_type').val();

            if (calculationType === 'fixed') {
                $('#provider_fixed_cost_container').show();
                $('#provider_hourly_cost_container').hide();
            } else {
                $('#provider_fixed_cost_container').hide();
                $('#provider_hourly_cost_container').show();
            }
        }

        // Check if selected month is already used or is a past month
        function checkMonthAvailability() {
            const selectedMonth = $('#month').val();
            const usedMonths = @json($usedMonths ?? []);
            const currentMonth = new Date().toISOString().slice(0, 7); // Format: YYYY-MM

            // Remove any existing feedback first
            $('#month').removeClass('is-invalid');
            $('.invalid-feedback').remove();

            // Check if month is already used
            if (Array.isArray(usedMonths) && usedMonths.includes(selectedMonth)) {
                $('#month').addClass('is-invalid');
                $('#month').after(
                '<div class="invalid-feedback">{{ __('message.month_already_used') }}</div>');
                $('#contractModalBody button[type="submit"]').prop('disabled', true);
                return false;
            }
            // Check if month is in the past
            else if (selectedMonth < currentMonth) {
                $('#month').addClass('is-invalid');
                $('#month').after(
                    '<div class="invalid-feedback">{{ __('message.cannot_select_past_month') }}</div>');
                $('#contractModalBody button[type="submit"]').prop('disabled', true);
                return false;
            } else {
                $('#contractModalBody button[type="submit"]').prop('disabled', false);
                return true;
            }
        }

        // Initial toggle
        toggleProviderCostFields();

        // Initial month check
        checkMonthAvailability();

        // Toggle on change
        $('#provider_calculation_type').on('change', toggleProviderCostFields);

        // Check month on change
        $('#month').on('change', checkMonthAvailability);

        // Form submission validation
        $('form').on('submit', function(e) {
            if (!checkMonthAvailability()) {
                e.preventDefault();
                return false;
            }
        });
    });
</script>

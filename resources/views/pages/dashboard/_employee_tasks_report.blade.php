<div class="card card-xl-stretch-50 mb-3">
    <div class="card-header border-0 pt-1 pb-0 text-center">
        <h3 class="card-title w-100 justify-content-center">
            <span class="card-label fw-bold fs-4 mb-0 report-title">{{ __('message.employee_tasks_report') }}</span>
        </h3>
    </div>
    <div class="card-body pt-0">
        <!-- Filter Form -->
        <form action="{{ route('dashboard') }}" method="GET" class="mb-2 mt-0" id="filter-form">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group mb-1">
                        <label for="provider_id" class="mb-1 small">{{ __('message.service_provider') }}</label>
                        <select name="provider_id" id="provider_id" class="form-control form-control-sm select2">
                            <option value="">{{ __('message.all_providers') }}</option>
                            @foreach($employeeProviders as $provider)
                                <option value="{{ $provider->id }}" {{ $providerId == $provider->id ? 'selected' : '' }}>
                                    {{ $provider->providerInfo->name ?? $provider->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group mb-1">
                        <label for="day" class="mb-1 small">{{ __('message.day') }}</label>
                        <select name="day" id="day" class="form-control form-control-sm">
                            <option value="">{{ __('message.all_days') }}</option>
                            @foreach($days as $d)
                                <option value="{{ $d }}" {{ $day == $d ? 'selected' : '' }}>{{ $d }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group mb-1">
                        <label for="month" class="mb-1 small">{{ __('message.month') }}</label>
                        <select name="month" id="month" class="form-control form-control-sm">
                            @foreach($months as $key => $m)
                                <option value="{{ $key }}" {{ $month == $key ? 'selected' : '' }}>{{ $m }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group mb-1">
                        <label for="year" class="mb-1 small">{{ __('message.year') }}</label>
                        <select name="year" id="year" class="form-control form-control-sm">
                            @foreach($years as $y)
                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-2 d-flex align-items-end pb-1">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="bi bi-filter"></i> {{ __('message.filter') }}
                    </button>
                    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary btn-sm ms-1">
                        <i class="bi bi-arrow-counterclockwise"></i> {{ __('message.reset') }}
                    </a>
                </div>
            </div>

            <!-- Keep the current page when filtering -->
            @if(request()->has('page'))
                <input type="hidden" name="page" value="{{ request('page') }}">
            @endif
        </form>

        @push('css')
        <style>
            /* Pagination styles */
            .pagination-container {
                margin-top: 20px;
            }
            .pagination {
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .pagination .page-item .page-link {
                padding: 8px 16px;
                margin: 0 4px;
                border-radius: 4px;
                color: #4154f1;
                background-color: #fff;
                border: 1px solid #dee2e6;
                transition: all 0.3s ease;
            }
            .pagination .page-item.active .page-link {
                background-color: #4154f1;
                border-color: #4154f1;
                color: white;
            }
            .pagination .page-item .page-link:hover:not(.disabled) {
                background-color: #e9ecef;
                color: #0d6efd;
                border-color: #dee2e6;
                z-index: 2;
            }
            .pagination .page-item.disabled .page-link {
                color: #6c757d;
                pointer-events: none;
                background-color: #fff;
                border-color: #dee2e6;
            }

            /* Table styles */
            .table {
                vertical-align: middle;
            }
            .table th, .table td {
                vertical-align: middle !important;
                padding: 12px 8px;
                text-align: center !important;
            }

            /* Report title styles */
            .report-title {
                color: #ffffff !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                font-size: 1.1rem !important;
                line-height: 1.2;
                font-weight: 600;
                letter-spacing: 0.5px;
                display: inline-block;
                padding: 2px 15px;
                border-radius: 20px;
                background: linear-gradient(135deg, #4154f1, #2536b3);
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                position: relative;
                overflow: hidden;
            }

            .report-title::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxjaXJjbGUgY3g9IjEwIiBjeT0iMTAiIHI9IjEwIiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkiIC8+CiAgICA8Y2lyY2xlIGN4PSI0MCIgY3k9IjIwIiByPSI4IiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkiIC8+CiAgICA8Y2lyY2xlIGN4PSI3MCIgY3k9IjMwIiByPSIxMiIgZmlsbD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIiAvPgogICAgPGNpcmNsZSBjeD0iOTAiIGN5PSI0MCIgcj0iNiIgZmlsbD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIiAvPgogICAgPGNpcmNsZSBjeD0iMzAiIGN5PSI1MCIgcj0iOSIgZmlsbD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIiAvPgogICAgPGNpcmNsZSBjeD0iNjAiIGN5PSI2MCIgcj0iMTUiIGZpbGw9InJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSIgLz4KICAgIDxjaXJjbGUgY3g9IjgwIiBjeT0iNzAiIHI9IjciIGZpbGw9InJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSIgLz4KICAgIDxjaXJjbGUgY3g9IjIwIiBjeT0iODAiIHI9IjExIiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkiIC8+CiAgICA8Y2lyY2xlIGN4PSI1MCIgY3k9IjkwIiByPSI5IiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkiIC8+Cjwvc3ZnPg==');
                opacity: 0.3;
                z-index: -1;
            }

            /* Select dropdown arrow styles */
            select.form-control-sm:not(.select2-hidden-accessible) {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23464a4e' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
                background-repeat: no-repeat;
                background-position: right 0.5rem center;
                background-size: 1em;
                padding-right: 1.5rem;
            }

            /* Select2 custom styles */
            .select2-container--bootstrap-5 .select2-selection {
                min-height: calc(1.5em + 0.5rem + 2px) !important;
                padding: 0.25rem 0.5rem !important;
                font-size: 0.875rem !important;
                border-radius: 0.25rem !important;
            }

            .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
                padding-left: 0 !important;
                padding-right: 0 !important;
            }

            .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
                height: calc(1.5em + 0.5rem + 2px) !important;
            }

            .select2-dropdown-sm {
                font-size: 0.875rem !important;
            }

            .select2-container--bootstrap-5 .select2-dropdown .select2-results__options .select2-results__option {
                padding: 0.25rem 0.5rem !important;
            }

            /* Status badge styles */
            .badge {
                padding: 6px 10px;
                font-size: 0.85rem;
                font-weight: 500;
                border-radius: 4px;
                display: inline-block;
                min-width: 100px;
                text-align: center;
            }
            .badge.bg-success {
                background-color: #28a745 !important;
            }
            .badge.bg-warning {
                background-color: #fd7e14 !important;
            }
            .badge.bg-danger {
                background-color: #dc3545 !important;
            }
            .badge.bg-secondary {
                background-color: #6c757d !important;
            }
            .badge.text-dark {
                color: #000 !important;
                font-weight: 600;
            }
        </style>
        @endpush

        @push('scripts')
        <script>
            $(document).ready(function() {
                // Initialize Select2
                $('.select2').select2({
                    placeholder: "{{ __('message.all_providers') }}",
                    allowClear: true,
                    theme: "bootstrap-5",
                    dropdownCssClass: "select2-dropdown-sm",
                    width: '100%'
                });

                // Handle filter changes
                $('#provider_id, #day, #month, #year').on('change', function() {
                    // Remove page parameter when changing filters
                    if ($('input[name="page"]').length > 0) {
                        $('input[name="page"]').val(1);
                    }
                });

                // Add click event for pagination links to preserve filters
                $(document).on('click', '.pagination a', function(e) {
                    e.preventDefault();
                    var url = $(this).attr('href');

                    // Debug: Log the URL to console
                    console.log('Pagination URL:', url);

                    // Extract page number from URL
                    var pageMatch = url.match(/[?&]page=(\d+)/);
                    if (pageMatch && pageMatch[1]) {
                        var page = pageMatch[1];

                        // Create a form and submit it to preserve all filters
                        var $form = $('<form>', {
                            'method': 'GET',
                            'action': '{{ request()->url() }}'
                        });

                        // Add all current query parameters
                        @foreach(request()->except('page') as $key => $value)
                            $form.append($('<input>', {
                                'type': 'hidden',
                                'name': '{{ $key }}',
                                'value': '{{ $value }}'
                            }));
                        @endforeach

                        // Add the page parameter
                        $form.append($('<input>', {
                            'type': 'hidden',
                            'name': 'page',
                            'value': page
                        }));

                        // Append to body and submit
                        $form.appendTo('body').submit();
                    } else {
                        // Fallback to direct navigation if page extraction fails
                        window.location.href = url;
                    }
                });

                // Handle save task button click (cancel button)
                $(document).on('click', '.save-task-btn', function() {
                    var button = $(this);
                    var providerId = button.data('provider-id');
                    var userId = button.data('user-id');
                    var serviceId = button.data('service-id');
                    var locationId = button.data('location-id');
                    var date = button.data('date');
                    var jobApplicationId = button.data('job-application-id');
                    var dId = button.data('d-id');

                    // Show confirmation dialog with notes input
                    Swal.fire({
                        title: '{{ __("message.confirm") }}',
                        html: 'هل أنت متأكد من الإلغاء؟<br><br>' +
                              '<label for="cancel-notes" class="form-label">سبب الإلغاء:</label>' +
                              '<textarea id="cancel-notes" class="form-control" rows="3" placeholder="أدخل سبب الإلغاء هنا..."></textarea>',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'نعم',
                        cancelButtonText: 'لا',
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        focusConfirm: false,
                        preConfirm: () => {
                            return document.getElementById('cancel-notes').value;
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Disable the button to prevent multiple clicks
                            button.prop('disabled', true);

                            // Send AJAX request to save the task
                            $.ajax({
                                url: '{{ route("tasks.save") }}',
                                type: 'POST',
                                data: {
                                    _token: '{{ csrf_token() }}',
                                    provider_id: providerId,
                                    user_id: userId,
                                    service_id: serviceId,
                                    location_id: locationId,
                                    execution_date: date,
                                    job_application_id: jobApplicationId,
                                    d_id: dId,
                                    notes: result.value // Add the cancellation notes
                                },
                                success: function(response) {
                                    if (response.success) {
                                        // Show success message
                                        Swal.fire({
                                            title: '{{ __("message.success") }}',
                                            text: response.message,
                                            icon: 'success',
                                            confirmButtonText: '{{ __("message.ok") }}'
                                        }).then(function() {
                                            // Reload the page to refresh the data
                                            window.location.reload();
                                        });
                                    } else {
                                        // Show error message
                                        Swal.fire({
                                            title: '{{ __("message.error") }}',
                                            text: response.message,
                                            icon: 'error',
                                            confirmButtonText: '{{ __("message.ok") }}'
                                        });
                                        // Re-enable the button
                                        button.prop('disabled', false);
                                    }
                                },
                                error: function(xhr) {
                                    // Show error message
                                    var errorMessage = xhr.responseJSON ? xhr.responseJSON.message : '{{ __("message.something_went_wrong") }}';
                                    Swal.fire({
                                        title: '{{ __("message.error") }}',
                                        text: errorMessage,
                                        icon: 'error',
                                        confirmButtonText: '{{ __("message.ok") }}'
                                    });
                                    // Re-enable the button
                                    button.prop('disabled', false);
                                }
                            });
                        }
                    });
                });

                // Handle time edit button click
                $(document).on('click', '.time-edit-btn', function() {
                    var button = $(this);
                    var providerId = button.data('provider-id');
                    var userId = button.data('user-id');
                    var serviceId = button.data('service-id');
                    var locationId = button.data('location-id');
                    var date = button.data('date');
                    var jobApplicationId = button.data('job-application-id');
                    var dId = button.data('d-id');
                    var startTime = button.data('start-time');
                    var endTime = button.data('end-time');
                    var taskId = button.data('task-id');
                    var calculationType = button.data('calculation-type');

                    // Show time edit dialog
                    Swal.fire({
                        title: 'تعديل أوقات العمل',
                        html: '<div class="mb-3">' +
                              '<label for="start-time" class="form-label">وقت البداية:</label>' +
                              '<input type="time" id="start-time" class="form-control" value="' + startTime + '">' +
                              '</div>' +
                              '<div class="mb-3">' +
                              '<label for="end-time" class="form-label">وقت النهاية:</label>' +
                              '<input type="time" id="end-time" class="form-control" value="' + endTime + '">' +
                              '</div>',
                        showCancelButton: true,
                        confirmButtonText: 'حفظ',
                        cancelButtonText: 'إلغاء',
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        focusConfirm: false,
                        preConfirm: () => {
                            return {
                                startTime: document.getElementById('start-time').value,
                                endTime: document.getElementById('end-time').value
                            };
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Validate times - only start time is required
                            if (!result.value.startTime) {
                                Swal.fire({
                                    title: '{{ __("message.error") }}',
                                    text: 'يجب إدخال وقت البداية',
                                    icon: 'error',
                                    confirmButtonText: '{{ __("message.ok") }}'
                                });
                                return;
                            }

                            // Check hours limit for fixed calculation type
                            if (calculationType === 'fixed' && result.value.startTime && result.value.endTime) {
                                // Calculate hours for this task
                                var startTime = new Date('2000-01-01 ' + result.value.startTime);
                                var endTime = new Date('2000-01-01 ' + result.value.endTime);

                                // Handle overnight shifts
                                if (endTime < startTime) {
                                    endTime.setDate(endTime.getDate() + 1);
                                }

                                var taskHours = (endTime - startTime) / (1000 * 60 * 60); // Convert to hours

                                // Get current month's used hours via AJAX
                                $.ajax({
                                    url: '{{ route("tasks.check-hours") }}',
                                    type: 'POST',
                                    data: {
                                        _token: '{{ csrf_token() }}',
                                        job_application_id: jobApplicationId,
                                        current_task_id: taskId,
                                        new_task_hours: taskHours,
                                        execution_date: date
                                    },
                                    success: function(hoursResponse) {
                                        if (hoursResponse.success) {
                                            var totalAllowedHours = hoursResponse.total_allowed_hours;
                                            var usedHours = hoursResponse.used_hours;
                                            var newTotalHours = usedHours + taskHours;
                                            var remainingHours = totalAllowedHours - newTotalHours;
                                            var transportationPaid = hoursResponse.transportation_paid;

                                            // Check transportation payment status
                                            var transportationMessage = '';
                                            if (transportationPaid && transportationPaid.paid) {
                                                var paidByText = transportationPaid.paid_by.includes('customer') && transportationPaid.paid_by.includes('provider')
                                                    ? 'العميل ومقدم الخدمة'
                                                    : transportationPaid.paid_by.includes('customer')
                                                        ? 'العميل'
                                                        : 'مقدم الخدمة';
                                                transportationMessage = `<p class="text-success"><i class="bi bi-check-circle"></i> <strong>تم دفع المواصلات من قبل:</strong> ${paidByText}</p>`;
                                            }

                                            // Check if hours exceeded
                                            if (newTotalHours > totalAllowedHours) {
                                                Swal.fire({
                                                    title: 'تجاوز عدد الساعات المسموح!',
                                                    html: `
                                                        <div class="text-start">
                                                            <p><strong>إجمالي الساعات المسموح بها:</strong> ${totalAllowedHours} ساعة</p>
                                                            <p><strong>الساعات المستخدمة حالياً:</strong> ${usedHours.toFixed(2)} ساعة</p>
                                                            <p><strong>ساعات هذه المهمة:</strong> ${taskHours.toFixed(2)} ساعة</p>
                                                            <p><strong>المجموع الجديد:</strong> ${newTotalHours.toFixed(2)} ساعة</p>
                                                            <p class="text-danger"><strong>تجاوز بـ:</strong> ${(newTotalHours - totalAllowedHours).toFixed(2)} ساعة</p>
                                                            ${transportationMessage}
                                                        </div>
                                                    `,
                                                    icon: 'error',
                                                    showCancelButton: true,
                                                    confirmButtonText: 'متابعة رغم التجاوز',
                                                    cancelButtonText: 'إلغاء',
                                                    confirmButtonColor: '#d33'
                                                }).then((confirmResult) => {
                                                    if (confirmResult.isConfirmed) {
                                                        proceedWithSave();
                                                    } else {
                                                        button.prop('disabled', false);
                                                    }
                                                });
                                                return;
                                            }
                                            // Check if approaching limit (less than 2 hours remaining)
                                            else if (remainingHours <= 2 && remainingHours > 0) {
                                                Swal.fire({
                                                    title: 'تنبيه: اقتراب من حد الساعات!',
                                                    html: `
                                                        <div class="text-start">
                                                            <p><strong>إجمالي الساعات المسموح بها:</strong> ${totalAllowedHours} ساعة</p>
                                                            <p><strong>الساعات المستخدمة حالياً:</strong> ${usedHours.toFixed(2)} ساعة</p>
                                                            <p><strong>ساعات هذه المهمة:</strong> ${taskHours.toFixed(2)} ساعة</p>
                                                            <p><strong>المجموع الجديد:</strong> ${newTotalHours.toFixed(2)} ساعة</p>
                                                            <p class="text-warning"><strong>الساعات المتبقية:</strong> ${remainingHours.toFixed(2)} ساعة</p>
                                                            ${transportationMessage}
                                                        </div>
                                                    `,
                                                    icon: 'warning',
                                                    showCancelButton: true,
                                                    confirmButtonText: 'متابعة',
                                                    cancelButtonText: 'إلغاء',
                                                    confirmButtonColor: '#3085d6'
                                                }).then((confirmResult) => {
                                                    if (confirmResult.isConfirmed) {
                                                        proceedWithSave();
                                                    } else {
                                                        button.prop('disabled', false);
                                                    }
                                                });
                                                return;
                                            }
                                            // Hours are within limit, check if transportation was paid and show info
                                            else {
                                                if (transportationPaid && transportationPaid.paid) {
                                                    Swal.fire({
                                                        title: 'ℹ️ معلومات المواصلات',
                                                        html: `
                                                            <div class="text-start">
                                                                ${transportationMessage}
                                                                <p class="text-muted"><small>لن يتم احتساب تكلفة المواصلات مرة أخرى لهذه الوظيفة في هذا الشهر.</small></p>
                                                            </div>
                                                        `,
                                                        icon: 'info',
                                                        showCancelButton: true,
                                                        confirmButtonText: 'متابعة',
                                                        cancelButtonText: 'إلغاء',
                                                        confirmButtonColor: '#0d6efd',
                                                        cancelButtonColor: '#6c757d'
                                                    }).then((confirmResult) => {
                                                        if (confirmResult.isConfirmed) {
                                                            proceedWithSave();
                                                        } else {
                                                            button.prop('disabled', false);
                                                        }
                                                    });
                                                } else {
                                                    // Transportation not paid - proceed directly
                                                    proceedWithSave();
                                                }
                                            }
                                        } else {
                                            // Error getting hours info, proceed anyway
                                            proceedWithSave();
                                        }
                                    },
                                    error: function() {
                                        // Error in AJAX, proceed anyway
                                        proceedWithSave();
                                    }
                                });
                                return; // Don't proceed immediately, wait for AJAX response
                            }

                            // For hourly calculation type or when no end time, proceed normally
                            proceedWithSave();

                            function proceedWithSave() {
                                // Prepare data for the request
                                var requestData = {
                                    _token: '{{ csrf_token() }}',
                                    provider_id: providerId,
                                    user_id: userId,
                                    service_id: serviceId,
                                    location_id: locationId,
                                    execution_date: date,
                                    job_application_id: jobApplicationId,
                                    d_id: dId,
                                    start_time: result.value.startTime,
                                    end_time: result.value.endTime || null
                                };
                                
                                // إضافة معرف المهمة عند تحديث مهمة محددة
                                if (taskId) {
                                    requestData.task_id = taskId;
                                }

                                // If task already exists, update it
                                var url = taskId ? '{{ url("tasks/update-time") }}/' + taskId : '{{ route("tasks.save") }}';

                                // Send AJAX request to save/update the task
                                $.ajax({
                                    url: url,
                                    type: 'POST',
                                    data: requestData,
                                    success: function(response) {
                                        if (response.success) {
                                            // Show success message that auto-closes
                                            Swal.fire({
                                                title: '{{ __("message.success") }}',
                                                text: response.message,
                                                icon: 'success',
                                                timer: 2000, // Auto close after 2 seconds
                                                showConfirmButton: false, // Hide the OK button
                                                timerProgressBar: true // Show progress bar
                                            }).then(function() {
                                                // Reload the page to refresh the data
                                                window.location.reload();
                                            });
                                        } else {
                                            // Show error message
                                            Swal.fire({
                                                title: '{{ __("message.error") }}',
                                                text: response.message,
                                                icon: 'error',
                                                confirmButtonText: '{{ __("message.ok") }}'
                                            });
                                            // Re-enable the button
                                            button.prop('disabled', false);
                                        }
                                    },
                                error: function(xhr) {
                                    // Show error message
                                    var errorMessage = xhr.responseJSON ? xhr.responseJSON.message : '{{ __("message.something_went_wrong") }}';
                                    Swal.fire({
                                        title: '{{ __("message.error") }}',
                                        text: errorMessage,
                                        icon: 'error',
                                        confirmButtonText: '{{ __("message.ok") }}'
                                    });
                                    // Re-enable the button
                                    button.prop('disabled', false);
                                }
                            });
                            }
                        }
                    });
                });
            });
        </script>
        @endpush

        <!-- Tasks Table -->
        <div class="table-responsive">
            <table class="table table-striped table-bordered text-center">
                <thead>
                    <tr>
                        <th class="text-center">{{ __('message.service_provider') }}</th>
                        <th class="text-center">{{ __('message.customer') }}</th>
                        <th class="text-center">{{ __('message.service') }}</th>
                        <th class="text-center">{{ __('message.location') }}</th>
                        <th class="text-center">{{ __('message.date') }}</th>
                        <th class="text-center">{{ __('message.start_time') }}</th>
                        <th class="text-center">{{ __('message.end_time') }}</th>
                        <th class="text-center">{{ __('message.status') }}</th>
                        <th class="text-center">{{ __('message.actions') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @if(count($tasks) > 0)
                        @foreach($tasks as $task)
                            <tr>
                                <td class="text-center">{{ $task['provider']->providerInfo->name ?? $task['provider']->name }}</td>
                                <td class="text-center">{{ $task['user']->name }}</td>
                                <td class="text-center">{{ $task['service']->getTranslation('name', app()->getLocale()) }}</td>
                                <td class="text-center">
                                    @if($task['location'])
                                        {{ $task['location']->name }}
                                    @else
                                        -
                                    @endif
                                </td>
                                <td class="text-center">{{ $task['date'] }}</td>
                                <td class="text-center">{{ $task['start_time'] }}</td>
                                <td class="text-center">{{ $task['end_time'] }}</td>
                                <td class="text-center">
                                    @if($task['display_status'] == 'تم التنفيذ' || $task['display_status'] == __('message.completed'))
                                        <span class="badge" style="background-color: #28a745; color: #FFFFFF;">{{ $task['display_status'] }}</span>
                                    @elseif($task['display_status'] == 'غير مكتمل' || $task['display_status'] == __('message.incomplete'))
                                        <span class="badge" style="background-color: #9c27b0; color: #FFFFFF;">{{ $task['display_status'] }}</span>
                                    @elseif($task['display_status'] == 'لم تُنفذ' || $task['display_status'] == __('message.not_executed'))
                                        <span class="badge" style="background-color: #007bff; color: #FFFFFF;">{{ $task['display_status'] }}</span>
                                    @elseif($task['display_status'] == 'تم الالغاء' || $task['display_status'] == __('message.cancelled'))
                                        <span class="badge" style="background-color: #dc3545; color: #FFFFFF;">{{ $task['display_status'] }}</span>
                                    @else
                                        <span class="badge" style="background-color: #17a2b8; color: #FFFFFF;">{{ $task['display_status'] }}</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    <div class="d-flex justify-content-center">
                                        @if($task['display_status'] !== 'تم الالغاء')
                                            @php
                                                // Check if both financial close fields are set to 1
                                                $isFinanciallyClosed = false;
                                                if ($task['task_id']) {
                                                    $taskModel = \App\Models\Task::find($task['task_id']);
                                                    if ($taskModel && $taskModel->is_financially_closed_by_user == 1 && $taskModel->is_financially_closed_by_provider == 1) {
                                                        $isFinanciallyClosed = true;
                                                    }
                                                }
                                            @endphp
                                            @if(!$isFinanciallyClosed)
                                        <button type="button" class="btn btn-primary btn-sm time-edit-btn ms-1"
                                            data-provider-id="{{ $task['provider']->id }}"
                                            data-user-id="{{ $task['user']->id }}"
                                            data-service-id="{{ $task['service']->id }}"
                                            data-location-id="{{ $task['location']->id ?? '' }}"
                                            data-date="{{ $task['date'] }}"
                                            data-job-application-id="{{ $task['job_application_id'] ?? '' }}"
                                            data-d-id="{{ $task['d_id'] ?? '' }}"
                                            data-start-time="{{ $task['task'] && $task['task']['start_time'] ? date('H:i', strtotime($task['task']['start_time'])) : '' }}"
                                            data-end-time="{{ $task['task'] && $task['task']['end_time'] ? date('H:i', strtotime($task['task']['end_time'])) : '' }}"
                                            data-task-id="{{ $task['task_id'] }}"
                                            data-calculation-type="@php
                                                $calculationType = '';
                                                if (isset($task['job_application_id']) && !empty($task['job_application_id'])) {
                                                    $jobApp = \App\Models\JobApplication::find($task['job_application_id']);
                                                    if ($jobApp) {
                                                        $calculationType = $jobApp->calculation_type;
                                                    }
                                                }
                                                echo $calculationType;
                                            @endphp">
                                            <i class="bi bi-clock"></i>
                                        </button>
                                            @endif
                                        @endif

                                        @if($task['display_status'] !== 'تم الالغاء' && $task['display_status'] !== __('message.cancelled') && !$task['task_id'])
                                        <button type="button" class="btn btn-danger btn-sm save-task-btn"
                                            data-provider-id="{{ $task['provider']->id }}"
                                            data-user-id="{{ $task['user']->id }}"
                                            data-service-id="{{ $task['service']->id }}"
                                            data-location-id="{{ $task['location']->id ?? '' }}"
                                            data-date="{{ $task['date'] }}"
                                            data-job-application-id="{{ $task['job_application_id'] ?? '' }}"
                                            data-d-id="{{ $task['d_id'] ?? '' }}">
                                            <i class="bi bi-x-lg"></i>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    @else
                        <tr>
                            <td colspan="9" class="text-center">{{ __('message.no_tasks_found') }}</td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-center mt-2">
            <div class="pagination-container">
                {{ $tasks->onEachSide(1)->links('vendor.pagination.bootstrap-4') }}
            </div>
        </div>
    </div>
</div>

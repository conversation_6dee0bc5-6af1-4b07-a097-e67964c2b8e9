<div class="card-body border-top p-9">
    <div class="row mt-4">
        <div class="col-lg-6 mb-10">
            <label for="name" class="form-label">{{ __('message.name') }}</label>
            <input type="text" name="name" id="name" value="{{ isset($coupon) ? $coupon->name : old('name') }}" class="form-control" required>
            @error('name')
                <div class="text-danger text-xs">{{ $message }}</div>
            @enderror
        </div>

        @if(isset($coupon))
        <div class="col-lg-6 mb-10">
            <label for="code" class="form-label">{{ __('message.code') }}</label>
            <input type="text" name="code" id="code" value="{{ $coupon->code }}" class="form-control" readonly>
            <small class="text-muted">{{ __('message.code_auto_generated') }}</small>
            @error('code')
                <div class="text-danger text-xs">{{ $message }}</div>
            @enderror
        </div>
        @endif
    </div>

    <div class="row">
        <div class="col-lg-6 mb-10">
            <label for="discount_value" class="form-label">{{ __('message.discount_value') }}</label>
            <input type="number" name="discount_value" id="discount_value" value="{{ isset($coupon) ? $coupon->discount_value : old('discount_value') }}" class="form-control {{ isset($coupon) ? 'bg-light' : '' }}" min="0" step="0.01" required {{ isset($coupon) ? 'disabled' : '' }}>
            @error('discount_value')
                <div class="text-danger text-xs">{{ $message }}</div>
            @enderror
        </div>

        <div class="col-lg-6 mb-10">
            <label for="discount_type" class="form-label">{{ __('message.discount_type') }}</label>
            <select name="discount_type" id="discount_type" class="form-select {{ isset($coupon) ? 'bg-light' : '' }}" required {{ isset($coupon) ? 'disabled' : '' }}>
                <option value="fixed" {{ (isset($coupon) && $coupon->discount_type == 'fixed') || old('discount_type') == 'fixed' ? 'selected' : '' }}>{{ __('message.fixed_amount') }}</option>
                <option value="percentage" {{ (isset($coupon) && $coupon->discount_type == 'percentage') || old('discount_type') == 'percentage' ? 'selected' : '' }}>{{ __('message.percentage') }}</option>
            </select>
            @error('discount_type')
                <div class="text-danger text-xs">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mb-10">
            <label for="usage_limit" class="form-label">{{ __('message.usage_limit') }}</label>
            <input type="number" name="usage_limit" id="usage_limit" value="{{ isset($coupon) ? $coupon->usage_limit : old('usage_limit', 1) }}" class="form-control" min="1" required>
            @error('usage_limit')
                <div class="text-danger text-xs">{{ $message }}</div>
            @enderror
        </div>

        <div class="col-lg-6 mb-10">
            <label for="is_active" class="form-label">{{ __('message.status') }}</label>
            <select name="is_active" id="is_active" class="form-select">
                <option value="1" {{ (isset($coupon) && $coupon->is_active) || old('is_active') == '1' ? 'selected' : '' }}>{{ __('message.active') }}</option>
                <option value="0" {{ (isset($coupon) && !$coupon->is_active) || old('is_active') == '0' ? 'selected' : '' }}>{{ __('message.inactive') }}</option>
            </select>
            @error('is_active')
                <div class="text-danger text-xs">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mb-10">
            <label for="start_date" class="form-label">{{ __('message.start_date') }}</label>
            <input type="date" name="start_date" id="start_date" value="{{ isset($coupon) ? $coupon->start_date->format('Y-m-d') : old('start_date') }}" class="form-control" required>
            @error('start_date')
                <div class="text-danger text-xs">{{ $message }}</div>
            @enderror
        </div>

        <div class="col-lg-6 mb-10">
            <label for="end_date" class="form-label">{{ __('message.end_date') }}</label>
            <input type="date" name="end_date" id="end_date" value="{{ isset($coupon) ? $coupon->end_date->format('Y-m-d') : old('end_date') }}" class="form-control" required>
            @error('end_date')
                <div class="text-danger text-xs">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

<div class="card-footer d-flex justify-content-end py-6 px-9">
    <button type="submit" class="btn btn-success btn-sm" id="kt_submit">{{ __('message.save') }}</button>
</div>

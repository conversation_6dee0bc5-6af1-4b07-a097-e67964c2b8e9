<x-app-layout>
    <div class="pagetitle">
        <h1>{{ __('message.coupons') }}</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('message.dashboard') }}</a></li>
                <li class="breadcrumb-item active">{{ __('message.coupons') }}</li>
            </ol>
        </nav>
    </div><!-- End Page Title -->
    <!-- component -->
    <section class="section">
        <div class="row">
            <div class="col-lg-12">
                <div class="card w-fit">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="card-title">{{ __('message.all_coupons') }}</h5>
                            <a href="{{ route('coupons.create') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-1"></i>
                                {{ __('message.add_coupon') }}
                            </a>
                        </div>

                        <!-- Table with stripped rows -->
                        <div class="table-responsive">
                            <table class="table-bordered table datatable text-center table-striped">
                                <thead>
                                    <tr>
                                        <th scope="col">{{ __('message.id') }}</th>
                                        <th scope="col">{{ __('message.name') }}</th>
                                        <th scope="col">{{ __('message.code') }}</th>
                                        <th scope="col">{{ __('message.discount_value') }}</th>
                                        <th scope="col">{{ __('message.discount_type') }}</th>
                                        <th scope="col">{{ __('message.usage_limit') }}</th>
                                        <th scope="col">{{ __('message.start_date') }}</th>
                                        <th scope="col">{{ __('message.end_date') }}</th>
                                        <th scope="col">{{ __('message.status') }}</th>
                                        <th scope="col" data-sortable="false">{{ __('message.action') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($coupons as $coupon)
                                        <tr>
                                            <td>{{ $coupon->id }}</td>
                                            <td>{{ $coupon->name }}</td>
                                            <td>{{ $coupon->code }}</td>
                                            <td>{{ $coupon->discount_value }}</td>
                                            <td>
                                                @if($coupon->discount_type == 'fixed')
                                                    {{ __('message.fixed_amount') }}
                                                @else
                                                    {{ __('message.percentage') }}
                                                @endif
                                            </td>
                                            <td>{{ $coupon->usage_limit }}</td>
                                            <td>{{ $coupon->start_date->format('Y-m-d') }}</td>
                                            <td>{{ $coupon->end_date->format('Y-m-d') }}</td>
                                            <td>
                                                @if($coupon->is_active)
                                                    <span class="badge bg-success">{{ __('message.active') }}</span>
                                                @else
                                                    <span class="badge bg-danger">{{ __('message.inactive') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center justify-content-center">
                                                    <div class="w-4 mr-2 mx-2">
                                                        <a href="{{ route('coupons.edit', $coupon->id) }}" class="btn btn-link p-0">
                                                            <i class="bi bi-pencil-square text-primary fs-5"></i>
                                                        </a>
                                                    </div>
                                                    <div class="w-4 mr-2 mx-2">
                                                        <form method="POST" action="{{ route('coupons.destroy', $coupon->id) }}" onsubmit="return confirm('{{ __('message.delete_coupon_confirmation') }}')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-link p-0">
                                                                <i class="bi bi-trash text-danger fs-5"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="10" class="text-center">{{ __('message.no_coupons_found') }}</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                        <!-- End Table with stripped rows -->
                    </div>
                </div>
            </div>
        </div>
    </section>
</x-app-layout>

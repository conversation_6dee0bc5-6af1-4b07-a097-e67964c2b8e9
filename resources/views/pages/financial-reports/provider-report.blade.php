<x-app-layout>
    <section class="section">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ __('message.provider financial report') }}</h5>

                        @if($selectedProvider)
                            @php
                                $provider = $providers->where('id', $selectedProvider)->first();
                                $providerName = $provider ? ($provider->providerInfo->name ?? $provider->name) : '';
                            @endphp
                            @if($providerName)
                                <div class="alert alert-info mb-4">
                                    <h6 class="mb-0">{{ __('message.provider') }}: <strong>{{ $providerName }}</strong></h6>
                                </div>
                            @endif
                        @endif

                        <!-- Filter Form -->
                        <form action="{{ route('financial-reports.provider') }}" method="GET" class="mb-4">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="provider_id" class="form-label">{{ __('message.provider') }}</label>
                                    <select name="provider_id" id="provider_id" class="form-select select2">
                                        <option value="">{{ __('message.all providers') }}</option>
                                        @foreach($providers as $provider)
                                            <option value="{{ $provider->id }}" @selected($selectedProvider == $provider->id)>
                                                {{ $provider->providerInfo->name ?? $provider->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2 mb-3">
                                    <label for="calculation_type" class="form-label">{{ __('message.payment_method') }}</label>
                                    <select name="calculation_type" id="calculation_type" class="form-select">
                                        @foreach($calculationTypes as $value => $label)
                                            <option value="{{ $value }}" @selected($selectedCalculationType == $value)>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2 mb-3">
                                    <label for="month" class="form-label">{{ __('message.month') }}</label>
                                    <select name="month" id="month" class="form-select">
                                        @foreach($months as $key => $month)
                                            <option value="{{ $key }}" @selected($selectedMonth == $key)>
                                                {{ $month }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="year" class="form-label">{{ __('message.year') }}</label>
                                    <select name="year" id="year" class="form-select">
                                        @foreach($years as $year)
                                            <option value="{{ $year }}" @selected($selectedYear == $year)>
                                                {{ $year }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3 d-flex align-items-end gap-2">
                                    <button type="submit" class="btn btn-primary flex-grow-1">
                                        <i class="bi bi-filter"></i> {{ __('message.filter') }}
                                    </button>
                                    <a href="{{ route('financial-reports.provider') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-counterclockwise"></i> {{ __('message.reset_filter') }}
                                    </a>
                                </div>
                                @if($selectedProvider && $selectedMonth && $selectedYear && $showFinancialCloseButton)
                                <div class="col-md-3 mb-3 d-flex align-items-end" id="financialCloseButtonContainer">
                                    <div class="btn-group w-100">
                                        <button type="button" class="btn btn-primary" onclick="showFinancialCloseSelectedModal()">
                                            <i class="bi bi-check-circle me-1"></i> {{ __('message.close_selected_tasks') }}
                                        </button>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </form>

                        <!-- Summary Cards -->
                        <div class="row mb-4">
                            <!-- Total hourly rate is hidden but still calculated in the controller -->
                            <input type="hidden" id="totalHourlyRate" value="{{ $totalHourlyRate }}" />

                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-subtitle mb-2 text-muted">{{ __('message.total hours') }}</h6>
                                        <h3>{{ number_format($totalHours, 2) }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-subtitle mb-2 text-muted">{{ __('message.monthly_transportation') }}</h6>
                                        <h3>{{ number_format($monthlyTransportation, 2) }}</h3>
                                        <small class="text-muted">{{ __('message.once_per_job_application') }}</small>
                                        @if($monthlyTransportation == 0)
                                            <br><small class="text-danger">{{ __('message.no_transportation_tasks') }}</small>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-subtitle mb-2 text-muted">{{ __('message.total amount') }}</h6>
                                        <h3>{{ number_format($grandTotal, 2) }}</h3>
                                        <small class="text-muted">{{ __('message.includes_monthly_transportation') }} ({{ number_format($monthlyTransportation, 2) }})</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tasks Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>{{ __('message.select') }}</th>
                                        <th>{{ __('message.provider name') }}</th>
                                        <th>{{ __('message.customer name') }}</th>
                                        <th>{{ __('message.service name') }}</th>
                                        <th>{{ __('message.location') }}</th>
                                        <th>{{ __('message.date') }}</th>
                                        <th>{{ __('message.start time') }}</th>
                                        <th>{{ __('message.end time') }}</th>
                                        <th>{{ __('message.hours') }}</th>
                                        <th>{{ __('message.hourly_rate') }}</th>
                                        <th>{{ __('message.amount') }}</th>
                                        <th>{{ __('message.actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($tasks as $task)
                                        @php
                                            // Check if transportation has been paid for this job application in this month
                                            $transportationPaid = \App\Models\Task::where('job_application_id', $task->job_application_id)
                                                ->whereMonth('execution_date', $selectedMonth)
                                                ->whereYear('execution_date', $selectedYear)
                                                ->where('is_financially_closed_by_provider', 1)
                                                ->exists();
                                        @endphp
                                        <tr data-transportation-cost="{{ $task->provider_transportation_cost }}"
                                            data-transportation-paid="{{ $transportationPaid ? 'true' : 'false' }}">
                                            <td class="text-center">
                                                @if(!$task->is_financially_closed_by_provider)
                                                    <div class="form-check">
                                                        <input class="form-check-input task-checkbox" type="checkbox" value="{{ $task->id }}"
                                                            data-amount="@if($task->provider_payment_method == 'hourly')
                                                                {{ number_format(($task->duration_minutes / 60) * $task->provider_hourly_cost, 2) }}
                                                            @else
                                                                {{ number_format($task->provider_fixed_cost, 2) }}
                                                            @endif"
                                                            data-job-application-id="{{ $task->job_application_id }}"
                                                            id="task-{{ $task->id }}" name="selected_tasks[]">
                                                    </div>
                                                @endif
                                            </td>
                                            <td>{{ $task->provider && $task->provider->providerInfo ? $task->provider->providerInfo->name : ($task->provider ? $task->provider->name : 'N/A') }}</td>
                                            <td>{{ $task->user ? $task->user->name : 'N/A' }}</td>
                                            <td>{{ $task->jobApplication && $task->jobApplication->service ? $task->jobApplication->service->name : 'N/A' }}</td>
                                            <td>{{ $task->jobApplication && $task->jobApplication->location ? $task->jobApplication->location->name : 'N/A' }}</td>
                                            <td>{{ date('d-m-Y', strtotime($task->execution_date)) }}</td>
                                            <td>{{ date('H:i', strtotime($task->start_time)) }}</td>
                                            <td>{{ $task->end_time ? date('H:i', strtotime($task->end_time)) : 'N/A' }}</td>
                                            <td>{{ number_format($task->duration_minutes / 60, 2) }}</td>
                                            <td class="text-center">
                                                @if($task->provider_payment_method == 'hourly')
                                                    {{ number_format($task->provider_hourly_cost, 2) }}
                                                @else
                                                    <span class="text-danger fw-bold">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($task->provider_payment_method == 'hourly')
                                                    @if($task->status == 'cancelled')
                                                        <span class="text-danger fw-bold">0.00</span>
                                                    @else
                                                        {{ number_format(($task->duration_minutes / 60) * $task->provider_hourly_cost, 2) }}
                                                    @endif
                                                @else
                                                    @if($task->status == 'cancelled')
                                                        <span class="text-danger fw-bold">{{ number_format($task->provider_fixed_cost, 2) }}</span>
                                                    @else
                                                        {{ number_format($task->provider_fixed_cost, 2) }}
                                                    @endif
                                                @endif
                                            </td>

                                            <td class="text-center">
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('task.details.show', $task->id) }}?month={{ $selectedMonth }}&year={{ $selectedYear }}" class="btn btn-sm btn-info">
                                                        <i class="bi bi-info-circle me-1"></i>{{ __('message.details') }}
                                                    </a>
                                                    @if($task->provider_payment_method != 'hourly' && isset($fixedPaymentTasksGroups[$task->job_application_id]) && count($fixedPaymentTasksGroups[$task->job_application_id]) > 0)
                                                    <button type="button" class="btn btn-sm btn-primary" onclick="loadTaskDetails({{ $task->job_application_id }}, '{{ $task->provider_payment_method }}', {{ $task->provider_fixed_cost }}, '{{ $task->jobApplication && $task->jobApplication->service ? $task->jobApplication->service->name : 'N/A' }}', {{ $selectedMonth }}, {{ $selectedYear }})">
                                                        <i class="bi bi-list-ul me-1"></i> {{ __('message.view_all_tasks') }}
                                                        <span class="badge bg-light text-dark">{{ count($fixedPaymentTasksGroups[$task->job_application_id]) }}</span>
                                                    </button>
                                                    @endif

                                                    @if($task->is_financially_closed_by_provider)
                                                        <span class="badge bg-success ms-2">{{ __('message.financially_closed') }}</span>
                                                    @else
                                                        <span class="badge bg-warning ms-2">{{ __('message.financially_open') }}</span>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>

                                        <!-- Task Details Modal -->
                                        <div class="modal fade" id="taskDetailsModal{{ $task->id }}" tabindex="-1" aria-labelledby="taskDetailsModalLabel{{ $task->id }}" aria-hidden="true">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="taskDetailsModalLabel{{ $task->id }}">{{ __('message.task_details') }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <p><strong>{{ __('message.provider name') }}:</strong> {{ $task->provider && $task->provider->providerInfo ? $task->provider->providerInfo->name : ($task->provider ? $task->provider->name : 'N/A') }}</p>
                                                                <p><strong>{{ __('message.customer name') }}:</strong> {{ $task->user ? $task->user->name : 'N/A' }}</p>
                                                                <p><strong>{{ __('message.service name') }}:</strong> {{ $task->jobApplication && $task->jobApplication->service ? $task->jobApplication->service->name : 'N/A' }}</p>
                                                                <p><strong>{{ __('message.location') }}:</strong> {{ $task->jobApplication && $task->jobApplication->location ? $task->jobApplication->location->name : 'N/A' }}</p>
                                                                <p><strong>{{ __('message.day') }}:</strong> {{ date('l', strtotime($task->execution_date)) }}</p>
                                                                <p><strong>{{ __('message.date') }}:</strong> {{ date('d-m-Y', strtotime($task->execution_date)) }}</p>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <p><strong>{{ __('message.start time') }}:</strong> {{ date('H:i', strtotime($task->start_time)) }}</p>
                                                                <p><strong>{{ __('message.end time') }}:</strong> {{ $task->end_time ? date('H:i', strtotime($task->end_time)) : 'N/A' }}</p>
                                                                <p><strong>{{ __('message.hours') }}:</strong> {{ number_format($task->duration_minutes / 60, 2) }}</p>
                                                                <p><strong>{{ __('message.payment_method') }}:</strong> {{ $task->provider_payment_method == 'hourly' ? __('message.hourly') : __('message.fixed_amount') }}</p>
                                                                <p><strong>{{ __('message.calculation_type') }}:</strong> {{ $task->calculation_type == 'hourly' ? __('message.hourly') : __('message.fixed_amount') }}</p>

                                                                @if($task->provider_payment_method == 'hourly')
                                                                    <!-- Tipo de pago por hora -->
                                                                    <p><strong>{{ __('message.provider_hourly_cost') }}:</strong> {{ number_format($task->provider_hourly_cost, 2) }}</p>
                                                                    <p><strong>{{ __('message.hours') }}:</strong> {{ number_format($task->duration_minutes / 60, 2) }}</p>
                                                                    <p><strong>{{ __('message.amount') }}:</strong> {{ number_format(($task->duration_minutes / 60) * $task->provider_hourly_cost, 2) }}</p>
                                                                @else
                                                                    <!-- Tipo de pago fijo -->
                                                                    <p><strong>{{ __('message.provider_fixed_cost') }}:</strong> {{ number_format($task->provider_fixed_cost, 2) }}</p>
                                                                    <p><strong>{{ __('message.hours') }}:</strong> {{ number_format($task->duration_minutes / 60, 2) }}</p>
                                                                    <p><strong>{{ __('message.amount') }}:</strong> {{ number_format($task->provider_fixed_cost, 2) }}</p>
                                                                @endif
                                                                <p><strong>{{ __('message.transportation') }}:</strong>
                                                                    {{ number_format($task->provider_transportation_cost, 2) }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('message.close') }}</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                    @empty
                                        <tr>
                                            <td colspan="12" class="text-center">{{ __('message.no data available') }}</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                                <tfoot>
                                    <tr class="bg-light">
                                        <th>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="select-all-tasks">
                                                <label class="form-check-label" for="select-all-tasks">
                                                    {{ __('message.select_all') }}
                                                </label>
                                            </div>
                                        </th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th>{{ number_format($totalHours, 2) }}</th>
                                        <th>{{ number_format($totalHourlyRate, 2) }}</th>
                                        <th>{{ number_format($totalAmount, 2) }}</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Common Modal for All Tasks -->
    <div class="modal fade" id="allTasksModal" tabindex="-1" aria-labelledby="allTasksModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="allTasksModalLabel">
                        <i class="bi bi-list-ul me-1"></i> {{ __('message.all_tasks_for_job') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="taskDetailsLoading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">{{ __('message.loading_data') }}...</p>
                    </div>

                    <div id="taskDetailsContent" style="display: none;">
                        <div class="alert alert-info mb-4">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                <div>
                                    <h6 class="mb-0">{{ __('message.job_type') }}: <strong id="jobTypeName"></strong></h6>
                                    <p class="mb-0"><strong>{{ __('message.payment_method') }}:</strong> <span id="paymentMethod"></span></p>
                                    <p class="mb-0"><strong>{{ __('message.amount') }}:</strong> <span id="totalAmount"></span></p>
                                    <p class="mb-0"><strong>{{ __('message.transportation') }}:</strong> <span id="totalTransportation"></span></p>
                                    <p class="mb-0"><strong>{{ __('message.total') }}:</strong> <span id="grandTotal"></span></p>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th>{{ __('message.date') }}</th>
                                        <th>{{ __('message.start time') }}</th>
                                        <th>{{ __('message.end time') }}</th>
                                        <th>{{ __('message.duration') }}</th>
                                        <th>{{ __('message.amount') }}</th>
                                        <th>{{ __('message.transportation') }}</th>
                                        <th>{{ __('message.status') }}</th>
                                        <th>{{ __('message.notes') }}</th>
                                    </tr>
                                </thead>
                                <tbody id="taskDetailsTableBody">
                                    <!-- Task details will be loaded here -->
                                </tbody>
                                <tfoot>
                                    <tr class="bg-light">
                                        <th colspan="3" class="text-end">{{ __('message.total') }}:</th>
                                        <th id="totalHours">0.00</th>
                                        <th id="totalTaskAmount">0.00</th>
                                        <th id="totalTaskTransportation">0.00</th>
                                        <th colspan="2"></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <div class="alert alert-warning mt-3">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            {{ __('message.fixed_amount_job_note') }}
                            <p class="mb-0 mt-2">
                                <strong>{{ __('message.note') }}:</strong>
                                <span id="paymentMethodNote"></span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('message.close') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Close Options Modal -->
    <div class="modal fade" id="financialCloseOptionsModal" tabindex="-1" aria-labelledby="financialCloseOptionsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="financialCloseOptionsModalLabel">
                        <i class="bi bi-check-circle me-1"></i> {{ __('message.financial_close') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        {{ __('message.select_financial_close_type') }}
                    </div>
                    <div class="d-grid gap-3">
                        <button type="button" class="btn btn-outline-success btn-lg" onclick="showFinancialCloseModal('provider')">
                            <i class="bi bi-person-check me-2"></i> {{ __('message.financial_close_provider') }}
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-lg" onclick="showFinancialCloseModal('user')">
                            <i class="bi bi-person-check me-2"></i> {{ __('message.financial_close_user') }}
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('message.cancel') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Close Provider Modal -->
    <div class="modal fade" id="financialCloseProviderModal" tabindex="-1" aria-labelledby="financialCloseProviderModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="financialCloseProviderModalLabel">
                        <i class="bi bi-check-circle me-1"></i> {{ __('message.financial_close_provider') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        {{ __('message.confirm_payment') }}
                    </div>

                    <div class="card mb-3">
                        <div class="card-header bg-primary text-white">
                            <i class="bi bi-info-circle me-2"></i>{{ __('message.financial_close_details') }}
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>{{ __('message.provider') }}:</strong>
                                        @if($selectedProvider)
                                            @php
                                                $provider = $providers->where('id', $selectedProvider)->first();
                                                $providerInfo = $provider->providerInfo;
                                                $displayName = $providerInfo && $providerInfo->name ? $providerInfo->name : $provider->name;
                                            @endphp
                                            {{ $displayName }}
                                        @endif
                                    </p>
                                    <p><strong>{{ __('message.selected_month') }}:</strong> {{ $months[$selectedMonth] }} {{ $selectedYear }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>{{ __('message.total hours') }}:</strong> <span id="selected-hours">{{ number_format($totalHours, 2) }}</span></p>
                                    <p><strong>{{ __('message.transportation_costs_included') }}:</strong> <span id="selected-transportation">{{ number_format($monthlyTransportation, 2) }}</span></p>
                                </div>
                            </div>
                            <div class="alert alert-info mt-3 mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                {{ __('message.only_unclosed_tasks_included') }}
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-success">
                        <i class="bi bi-cash-coin me-2"></i>
                        {{ __('message.total_amount_to_pay') }}: <strong id="selected-total-amount">{{ number_format($grandTotal, 2) }}</strong>
                    </div>
                    <form id="financialCloseProviderForm" action="{{ route('financial-close.provider') }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <label for="provider_id" class="form-label">{{ __('message.provider') }}</label>
                            @if($selectedProvider)
                                <input type="hidden" name="provider_id" value="{{ $selectedProvider }}">
                                @php
                                    $provider = $providers->where('id', $selectedProvider)->first();
                                    $providerInfo = $provider->providerInfo;
                                    $displayName = $providerInfo && $providerInfo->name ? $providerInfo->name : $provider->name;
                                @endphp
                                <p class="form-control-static"><strong>{{ $displayName }}</strong></p>
                            @else
                                <select name="provider_id" id="provider_id" class="form-select" required>
                                    <option value="">{{ __('message.select_provider') }}</option>
                                    @foreach($providers as $provider)
                                        <option value="{{ $provider->id }}">
                                            @php
                                                $providerInfo = $provider->providerInfo;
                                                $displayName = $providerInfo && $providerInfo->name ? $providerInfo->name : $provider->name;
                                            @endphp
                                            {{ $displayName }}
                                        </option>
                                    @endforeach
                                </select>
                            @endif
                        </div>
                        <input type="hidden" name="month" value="{{ $selectedMonth }}">
                        <input type="hidden" name="year" value="{{ $selectedYear }}">

                        <!-- Selected tasks will be added here dynamically -->
                        <div id="selected-tasks-container"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('message.cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="submitFinancialCloseForm('provider', true)" id="close-selected-btn">
                        <i class="bi bi-check-circle me-1"></i> {{ __('message.close_selected_tasks') }}
                    </button>
                    <button type="button" class="btn btn-success" onclick="submitFinancialCloseForm('provider', false)" id="close-all-btn">
                        <i class="bi bi-check-circle me-1"></i> {{ __('message.financial_close') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Close User Modal -->
    <div class="modal fade" id="financialCloseUserModal" tabindex="-1" aria-labelledby="financialCloseUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="financialCloseUserModalLabel">
                        <i class="bi bi-check-circle me-1"></i> {{ __('message.financial_close_user') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        {{ __('message.confirm_financial_close_user') }}
                    </div>
                    <p class="text-muted">
                        <i class="bi bi-calendar-check me-1"></i> {{ __('message.selected_month') }}: <strong>{{ $months[$selectedMonth] }} {{ $selectedYear }}</strong>
                    </p>
                    <form id="financialCloseUserForm" action="{{ route('financial-close.user') }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <label for="user_id" class="form-label">{{ __('message.customer') }}</label>
                            <select name="user_id" id="user_id" class="form-select" required>
                                <option value="">{{ __('message.select_customer') }}</option>
                                @foreach($clients as $client)
                                    <option value="{{ $client->id }}">
                                        {{ $client->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <input type="hidden" name="month" value="{{ $selectedMonth }}">
                        <input type="hidden" name="year" value="{{ $selectedYear }}">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('message.cancel') }}</button>
                    <button type="button" class="btn btn-success" onclick="submitFinancialCloseForm('user')">
                        <i class="bi bi-check-circle me-1"></i> {{ __('message.financial_close') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for financial close and task details -->
    <script>
        // Function to calculate selected tasks total
        function calculateSelectedTotal() {
            const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
            let totalAmount = 0;
            let totalTransportation = 0;
            let totalHours = 0;
            const processedJobApplications = new Set();

            selectedCheckboxes.forEach(checkbox => {
                const taskRow = checkbox.closest('tr');

                // Get task data from the row
                const hoursCell = taskRow.querySelector('td:nth-child(9)');
                const amountCell = taskRow.querySelector('td:nth-child(11)');
                const jobApplicationId = checkbox.getAttribute('data-job-application-id');

                // Calculate hours
                if (hoursCell) {
                    const hours = parseFloat(hoursCell.textContent);
                    if (!isNaN(hours)) {
                        totalHours += hours;
                    }
                }

                // Calculate amount - for cancelled tasks, use the original amount (not 0)
                if (amountCell) {
                    let amountText = amountCell.textContent.trim();
                    // Remove any formatting and get the numeric value
                    amountText = amountText.replace(/[^\d.-]/g, '');
                    const amount = parseFloat(amountText);
                    if (!isNaN(amount)) {
                        totalAmount += amount;
                    }
                }

                // Calculate transportation (once per job application) - only if not already paid
                if (jobApplicationId && !processedJobApplications.has(jobApplicationId)) {
                    processedJobApplications.add(jobApplicationId);

                    // Check if transportation has already been paid for this job application
                    const transportationPaid = taskRow.getAttribute('data-transportation-paid') === 'true';

                    // Only add transportation cost if it hasn't been paid yet
                    if (!transportationPaid) {
                        totalTransportation += parseFloat(taskRow.getAttribute('data-transportation-cost')) || 0;
                    }
                }
            });

            // Update the modal with selected amounts
            if (document.getElementById('selected-total-amount')) {
                document.getElementById('selected-total-amount').textContent = totalAmount.toFixed(2);
            }
            if (document.getElementById('selected-transportation')) {
                document.getElementById('selected-transportation').textContent = totalTransportation.toFixed(2);
            }
            if (document.getElementById('selected-hours')) {
                document.getElementById('selected-hours').textContent = totalHours.toFixed(2);
            }
        }

        // Function to handle select all checkbox
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.getElementById('select-all-tasks');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
                    taskCheckboxes.forEach(checkbox => {
                        checkbox.checked = selectAllCheckbox.checked;
                    });
                    calculateSelectedTotal();
                });
            }

            // Add event listeners to all task checkboxes
            const taskCheckboxes = document.querySelectorAll('.task-checkbox');
            taskCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const jobApplicationId = this.getAttribute('data-job-application-id');
                    const isChecked = this.checked;

                    // Select/deselect all tasks with the same job application ID
                    const relatedCheckboxes = document.querySelectorAll(`.task-checkbox[data-job-application-id="${jobApplicationId}"]`);
                    relatedCheckboxes.forEach(relatedCheckbox => {
                        relatedCheckbox.checked = isChecked;
                    });

                    calculateSelectedTotal();
                });
            });
        });

        // Function to check if provider tasks are already closed
        function checkFinancialCloseProvider() {
            // Get selected provider, month and year
            const providerId = {{ $selectedProvider ?? 'null' }};
            const month = {{ $selectedMonth ?? 'null' }};
            const year = {{ $selectedYear ?? 'null' }};

            if (!providerId || !month || !year) {
                return;
            }

            // Show loading indicator
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'spinner-border spinner-border-sm text-light me-2';
            loadingIndicator.setAttribute('role', 'status');

            const closeButton = document.querySelector('#financialCloseButtonContainer button');
            const originalButtonContent = closeButton.innerHTML;
            closeButton.innerHTML = '';
            closeButton.appendChild(loadingIndicator);
            closeButton.appendChild(document.createTextNode(' {{ __("message.loading_data") }}...'));
            closeButton.disabled = true;

            // Check if tasks are already closed via API
            fetch(`/api/financial-close/check-provider/${providerId}/${month}/${year}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Restore button
                    closeButton.innerHTML = originalButtonContent;
                    closeButton.disabled = false;

                    if (data.status === 'closed') {
                        // If tasks are already closed, show a simple alert
                        alert(data.message);
                    } else {
                        // If tasks are not closed, show the financial close modal
                        var providerModal = new bootstrap.Modal(document.getElementById('financialCloseProviderModal'));
                        providerModal.show();
                    }
                })
                .catch(error => {
                    console.error('Error checking financial close status:', error);
                    // Restore button
                    closeButton.innerHTML = originalButtonContent;
                    closeButton.disabled = false;

                    // Show the provider modal even if there's an error
                    var providerModal = new bootstrap.Modal(document.getElementById('financialCloseProviderModal'));
                    providerModal.show();
                });
        }

        // Function to show financial close modal for all tasks
        function showFinancialCloseAllModal() {
            // Show only the "Close All" button
            document.getElementById('close-all-btn').style.display = 'inline-block';
            document.getElementById('close-selected-btn').style.display = 'none';

            var providerModal = new bootstrap.Modal(document.getElementById('financialCloseProviderModal'));
            providerModal.show();
        }

        // Function to show financial close modal for selected tasks
        function showFinancialCloseSelectedModal() {
            calculateSelectedTotal();

            // Show only the "Close Selected" button
            document.getElementById('close-all-btn').style.display = 'none';
            document.getElementById('close-selected-btn').style.display = 'inline-block';

            var providerModal = new bootstrap.Modal(document.getElementById('financialCloseProviderModal'));
            providerModal.show();
        }

        // Function to show the appropriate financial close modal
        function showFinancialCloseModal(type) {
            // Hide the options modal
            var optionsModal = bootstrap.Modal.getInstance(document.getElementById('financialCloseOptionsModal'));
            optionsModal.hide();

            // Show the appropriate modal
            if (type === 'provider') {
                var providerModal = new bootstrap.Modal(document.getElementById('financialCloseProviderModal'));
                providerModal.show();
            } else if (type === 'user') {
                var userModal = new bootstrap.Modal(document.getElementById('financialCloseUserModal'));
                userModal.show();
            }
        }

        // Function to submit the financial close form
        function submitFinancialCloseForm(type, isSelectedTasks = false) {
            var form;
            var selectElement;

            if (type === 'provider') {
                form = document.getElementById('financialCloseProviderForm');
                selectElement = document.getElementById('provider_id');

                if (isSelectedTasks) {
                    // Add selected tasks to the form
                    const selectedTasksContainer = document.getElementById('selected-tasks-container');
                    selectedTasksContainer.innerHTML = ''; // Clear previous selections

                    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
                    if (selectedCheckboxes.length === 0) {
                        // If no tasks are selected, show a warning
                        alert('{{ __("message.select_tasks_to_close") }}');
                        return;
                    }

                    // Collect all job application IDs from selected tasks
                    const jobApplicationIds = new Set();
                    selectedCheckboxes.forEach(checkbox => {
                        const jobApplicationId = checkbox.getAttribute('data-job-application-id');
                        if (jobApplicationId) {
                            jobApplicationIds.add(jobApplicationId);
                        }
                    });

                    // Add each job application ID as a hidden input to close all tasks with the same job application ID
                    jobApplicationIds.forEach(jobApplicationId => {
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = 'job_application_ids[]';
                        hiddenInput.value = jobApplicationId;
                        selectedTasksContainer.appendChild(hiddenInput);
                    });
                }

                // If provider is already selected in the filter, we don't need to validate
                if (!selectElement) {
                    form.submit();
                    return;
                }
            } else if (type === 'user') {
                form = document.getElementById('financialCloseUserForm');
                selectElement = document.getElementById('user_id');

                // If client is already selected in the filter, we don't need to validate
                if (!selectElement) {
                    form.submit();
                    return;
                }
            }

            // Validate the form if select element exists
            if (selectElement && selectElement.value === '') {
                alert(type === 'provider' ? '{{ __("message.please_select_provider") }}' : '{{ __("message.please_select_customer") }}');
                return;
            }

            // Submit the form
            form.submit();
        }

        // Function to load task details
        function loadTaskDetails(jobApplicationId, paymentMethod, amount, serviceName, month, year) {
            // Show the modal
            var modal = new bootstrap.Modal(document.getElementById('allTasksModal'));
            modal.show();

            // Show loading indicator
            document.getElementById('taskDetailsLoading').style.display = 'block';
            document.getElementById('taskDetailsContent').style.display = 'none';

            // Set job type, payment method and amount
            document.getElementById('jobTypeName').textContent = serviceName;
            document.getElementById('paymentMethod').textContent = paymentMethod === 'hourly' ?
                '{{ __("message.hourly") }}' : '{{ __("message.fixed_amount") }}';
            document.getElementById('totalAmount').textContent = amount.toFixed(2);

            // Initialize transportation and grand total
            document.getElementById('totalTransportation').textContent = '0.00';
            document.getElementById('grandTotal').textContent = amount.toFixed(2);

            // Set payment method note
            document.getElementById('paymentMethodNote').textContent = paymentMethod === 'hourly' ?
                '{{ __("message.hourly_payment_note") }}' : '{{ __("message.fixed_payment_note") }}';

            // Fetch task details via AJAX with month and year parameters
            fetch(`/api/tasks/job-application/${jobApplicationId}?month=${month}&year=${year}`)
                .then(response => response.json())
                .then(data => {
                    // Hide loading indicator
                    document.getElementById('taskDetailsLoading').style.display = 'none';
                    document.getElementById('taskDetailsContent').style.display = 'block';

                    // Clear existing table rows
                    const tableBody = document.getElementById('taskDetailsTableBody');
                    tableBody.innerHTML = '';

                    // Variables for totals
                    let totalHours = 0;
                    let totalTaskAmount = 0;
                    let totalTaskTransportation = 0;

                    // For transportation cost tracking (once per task or job application)
                    let processedDays = {};

                    // Log data for debugging
                    console.log('Task data:', data);

                    // Check if we have tasks
                    if (data.tasks && data.tasks.length > 0) {
                        // Add rows for each task
                        data.tasks.forEach(task => {
                            const row = document.createElement('tr');

                            // Format date
                            const executionDate = new Date(task.execution_date);
                            const formattedDate = executionDate.toLocaleDateString('en-GB'); // DD/MM/YYYY

                            // Format times
                            const startTime = task.start_time ? task.start_time.substring(0, 5) : 'N/A'; // HH:MM
                            const endTime = task.end_time ? task.end_time.substring(0, 5) : 'N/A'; // HH:MM

                            // Calculate duration in hours
                            const durationHours = (task.duration_minutes / 60);
                            const formattedDurationHours = durationHours.toFixed(2);

                            // Calculate amount based on payment method from provider_payment_method column
                            let taskAmount = 0;
                            if (task.provider_payment_method === 'hourly') {
                                // Para tipo de pago por hora: verificar si está cancelada
                                if (task.status === 'cancelled') {
                                    taskAmount = 0; // Si está cancelada, no se cuenta
                                } else {
                                    taskAmount = durationHours * parseFloat(task.provider_hourly_cost || 0);
                                }
                            } else {
                                // Para tipo de pago fijo, mostrar siempre el monto (incluso si está cancelada)
                                taskAmount = parseFloat(task.provider_fixed_cost || 0);
                            }

                            // Get transportation cost
                            const transportationCost = parseFloat(task.provider_transportation_cost) || 0;

                            // Add to totals
                            totalHours += durationHours;
                            totalTaskAmount += taskAmount;

                            // Add transportation cost once per job_application_id for all payment methods
                            const jobApplicationKey = 'job_' + task.job_application_id;

                            if (!processedDays[jobApplicationKey]) {
                                // Count transportation cost once per job_application_id
                                totalTaskTransportation += transportationCost;

                                // Mark this job application as processed
                                processedDays[jobApplicationKey] = true;
                            }

                            row.innerHTML = `
                                <td>${formattedDate}</td>
                                <td>${startTime}</td>
                                <td>${endTime}</td>
                                <td>${formattedDurationHours} {{ __('message.h') }}</td>
                                <td>${taskAmount.toFixed(2)}</td>
                                <td>${transportationCost.toFixed(2)}</td>
                                <td>${task.status || 'N/A'}</td>
                                <td>${task.notes || 'N/A'}</td>
                            `;

                            tableBody.appendChild(row);
                        });

                        // Update totals in footer
                        document.getElementById('totalHours').textContent = totalHours.toFixed(2);
                        document.getElementById('totalTaskAmount').textContent = totalTaskAmount.toFixed(2);
                        document.getElementById('totalTaskTransportation').textContent = totalTaskTransportation.toFixed(2);

                        // Update summary in header
                        document.getElementById('totalAmount').textContent = totalTaskAmount.toFixed(2);
                        document.getElementById('totalTransportation').textContent = totalTaskTransportation.toFixed(2);
                        document.getElementById('grandTotal').textContent = (totalTaskAmount + totalTaskTransportation).toFixed(2);
                    } else {
                        // No tasks found
                        const row = document.createElement('tr');
                        row.innerHTML = `<td colspan="8" class="text-center">{{ __('message.no_records_found') }}</td>`;
                        tableBody.appendChild(row);

                        // Reset totals
                        document.getElementById('totalHours').textContent = '0.00';
                        document.getElementById('totalTaskAmount').textContent = '0.00';
                        document.getElementById('totalTaskTransportation').textContent = '0.00';
                        document.getElementById('totalTransportation').textContent = '0.00';
                        document.getElementById('grandTotal').textContent = amount.toFixed(2);
                    }
                })
                .catch(error => {
                    console.error('Error fetching task details:', error);
                    document.getElementById('taskDetailsLoading').style.display = 'none';
                    document.getElementById('taskDetailsContent').style.display = 'block';

                    // Show error message
                    const tableBody = document.getElementById('taskDetailsTableBody');
                    tableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">{{ __('message.error_loading_data') }}</td></tr>`;

                    // Reset totals
                    document.getElementById('totalHours').textContent = '0.00';
                    document.getElementById('totalTaskAmount').textContent = '0.00';
                    document.getElementById('totalTaskTransportation').textContent = '0.00';
                    document.getElementById('totalTransportation').textContent = '0.00';
                    document.getElementById('grandTotal').textContent = amount.toFixed(2);
                });
        }
    </script>
</x-app-layout>

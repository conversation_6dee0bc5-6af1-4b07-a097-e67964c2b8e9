<?php

namespace App\Services;

use App\Models\PaymentTransaction;

class PaymentService
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
    }

    /**
     * Generate Payment Data For Transaction.
     */
    public function createPayment(PaymentTransaction $transaction, $Ttype = null)
    {
        $customer_name = '';
        $email = '';
        $phone = '';
        if ($Ttype == 'job') {
            $customer_name = $transaction->task?->user?->name;
            $email = $transaction->task?->user?->email;
            $phone = $transaction->task?->user?->phone;
            $toPayAmount = $transaction->amount;
        } else {
            $order_service_name = null;
            if ($transaction->order?->type == 'offer') {
                $order_service_name = $transaction->order?->typeable;
            } else {
                $order_service_name = $transaction->order?->service?->name;
            }

            $orderServiceName = class_basename($transaction->order?->typable?->type);
            $toPayAmount = 0;
            // new for multi order payment
            if ($transaction->order?->group != null) {
                $multiTransaction = PaymentTransaction::whereHas('order', function ($query) use ($transaction) {
                    $query->where('orders.group', $transaction->order->group);
                })->get();
                foreach ($multiTransaction as $index => $singleTransaction) {
                    // if ($index >= 1) {
                    $toPayAmount += $singleTransaction->amount;
                    // }
                }
            } else {
                $toPayAmount = $transaction->amount;
            }
            $customer_name = $transaction->order?->user?->name;
            $email = $transaction->order?->user?->email;
            $phone = $transaction->order?->user?->phone;
        }
        $curl = curl_init();
        curl_setopt_array($curl, [
            // CURLOPT_URL => 'https://restapi.payplus.co.il/api/v1.0/PaymentPages/generateLink', //production
            CURLOPT_URL => 'https://restapidev.payplus.co.il/api/v1.0/PaymentPages/generateLink',  // test
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                "payment_page_uid": "26df4cbe-84f7-419b-953a-b3504fab9c42",
                "refURL_success": "'.url('Successful-Payment/'.$transaction->id).'",
                "refURL_failure": "'.url('Failed-Payment/'.$transaction->id).'",
                "refURL_callback": "'.url('Payment-Process/'.$transaction->id).'",
                "customer": {
                    "customer_name":"'.$customer_name.'",
                    "email":"'.$email.'",
                    "phone":"'.$phone.'"
                },
                "amount": '.$toPayAmount.',
                "currency_code": "ILS",
                "sendEmailApproval": true,
                "sendEmailFailure": false,
                "hide_identification_id": false
            }',
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'Content-Type: application/json',
                // 'Authorization: {"api_key":"24cff241-ffc3-4108-ac06-bcd0d3681f5f","secret_key":"9935c3f3-692c-43b9-b3c5-6152124bee5e"}', //production
                'Authorization: {"api_key":"52d9814b-ef5a-4856-91ea-8242d9a0b52a","secret_key":"5356a55d-50fc-4bfb-b6b5-f39a6adedbea"}',  // test
            ],
        ]);
        $response = curl_exec($curl);
        curl_close($curl);
        $response = json_decode($response, true);

        return $response;
    }

    /**
     * Get Payment Data OF Transaction.
     */
    public function get_payment_status(PaymentTransaction $transaction)
    {
        $curl = curl_init();
        // Production URL : https://api.invoice4u.co.il/Services/ApiService.svc/VerifyLogin

        // Mocking Server URL : https://private-anon-da8ca6b39c-invoice4uclearingapis.apiary-mock.com/Services/ApiService.svc/VerifyLogin
        // Mocking Server Email : <EMAIL>
        // Mocking Server Password : Test1234
        curl_setopt_array($curl, [
            CURLOPT_URL => 'https://api.invoice4u.co.il/Services/ApiService.svc/VerifyLogin',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                "email": "<EMAIL>",
                "password": "VishVish@@2023"
            }',
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
            ],
        ]);
        $response = curl_exec($curl);
        curl_close($curl);

        $result = json_decode($response, true);
        if (is_array($result) && !array_key_exists('d', $result)) {
            return false;
        }
        $token = str_replace(['{', '}', '"'], '', $result['d']);
        $token = trim($token);
        $token = ltrim($token);
        if ($token == null) {
            return false;
        }
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => 'https://api.invoice4u.co.il/Services/ApiService.svc/GetClearingLogById',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                "clearingLogId": "'.$transaction->invoice_logid.'",
                "token": "'.$token.'"
            }',
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
            ],
        ]);

        $response = curl_exec($curl);
        $response = json_decode($response, true);
        curl_close($curl);
        if ($response['d']['IsSuccess']) {
            return true;
        } else {
            return false;
        }
    }
}

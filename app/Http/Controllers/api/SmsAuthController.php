<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
use App\Services\SmsAuthService;
use App\Http\Controllers\Controller;
use App\Http\Resources\authResource;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Validator;

class SmsAuthController extends Controller
{
    /**
     * The SMS authentication service.
     *
     * @var SmsAuthService
     */
    protected $smsAuthService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(SmsAuthService $smsAuthService)
    {
        $this->smsAuthService = $smsAuthService;
    }

    /**
     * Register a new user with SMS verification.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone_code' => 'required_with:phone|string|max:10',
            'phone' => 'required_without:email|string|max:20',
            'password' => 'required|string|min:8',
            'address' => 'nullable|string',
            'type' => 'nullable|in:provider,user',
            'city_id' => 'nullable|exists:cities,id',
            'gender' => 'nullable|in:male,female',
            'device_token' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'code' => -1,
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $stagingUser = $this->smsAuthService->register($request->all());

            return response()->json([
                'success' => true,
                'code' => 1,
                'message' => __('message.register_success_otp_sent'),
                'data' => [
                    'id' => $stagingUser->id,
                    'name' => $stagingUser->name,
                    'email' => $stagingUser->email,
                    'phone' => $stagingUser->phone,
                    'phone_code' => $stagingUser->phone_code,
                ],
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function forgetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_phone' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['code' => 0, 'errors' => $validator->errors()]);
        }
        $user = User::where('email', $request->email_phone)->orWhere('phone', $request->email_phone)->first();

        if ($user === null) {
            return response()->json(['success' => false, 'code' => 0, 'message' => 'Wrong Login Credentials']);
        } elseif ($user->is_verified == 0) {
            return response()->json(['success' => false, 'code' => 0, 'message' => 'Your Account Is Not Verified']);
        } elseif ($user->is_blocked) {
            return response()->json(['success' => false, 'code' => 0, 'message' => 'Your Account Has Been Blocked']);
        } else {
            $user['access_token'] = $user->createToken('auth_token')->plainTextToken;
            $user->otp = $this->generate_otp();
            $user->save();
            $user = new authResource($user);

            return response()->json(['success' => true, 'code' => 1, 'message' => 'Forget Password OTP Sent Successfully', 'data' => $user]);
        }
    }

    /**
     * Verify the OTP and complete the registration.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_phone' => 'required|string', // Email or phone
            'otp' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'code' => -1,
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $this->smsAuthService->verifyOtp($request->email_phone, $request->otp);

        if (!$user) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => 'Invalid OTP or OTP expired',
            ], 400);
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => 'OTP verified successfully',
            'data' => new authResource($user),
        ], 200);
    }

    /**
     * Resend the OTP to the user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function resendOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_phone' => 'required|string', // Email or phone
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'code' => -1,
                'errors' => $validator->errors(),
            ], 422);
        }

        $success = $this->smsAuthService->resendOtp($request->email_phone);

        if (!$success) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => 'User not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => 'OTP resent successfully',
        ], 200);
    }

    /**
     * Verify the OTP and complete the registration.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyOldOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_phone' => 'required|string', // Email or phone
            'otp' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'code' => -1,
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $this->smsAuthService->verifyOldOtp($request->email_phone, $request->otp);

        if (!$user) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => 'Invalid OTP or OTP expired',
            ], 400);
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => 'OTP verified successfully',
            'data' => new UserResource($user),
        ], 200);
    }

    /**
     * Resend the OTP to the user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function resendOldOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_phone' => 'required|string', // Email or phone
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'code' => -1,
                'errors' => $validator->errors(),
            ], 422);
        }

        $success = $this->smsAuthService->resendOldOtp($request->email_phone);

        if (!$success) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => 'User not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => 'OTP resent successfully',
        ], 200);
    }
}

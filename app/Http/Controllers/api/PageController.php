<?php

namespace App\Http\Controllers\api;

use App\Http\Resources\FaqResource;
use App\Http\Resources\HomeCategoryResource;
use App\Http\Resources\OfferResource;
use App\Http\Resources\PageResource;
use App\Http\Resources\ProviderHomeResource;
use App\Http\Resources\ProviderResource;
use App\Http\Resources\ServiceResource;
use App\Http\Resources\SliderResource;
use App\Models\Category;
use App\Models\Configuration;
use App\Models\Faq;
use App\Models\Offer;
use App\Models\Page;
use App\Models\Service;
use App\Models\Slider;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class PageController extends ApiController
{
    public function index($slug)
    {
        $page = Page::where('slug', $slug)->first();
        $page = new PageResource($page);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => null,
            'data' => $page,
        ], 200);
    }

    public function faq()
    {
        $questions = Faq::all();
        $questions = FaqResource::collection($questions);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => null,
            'data' => $questions,
        ], 200);
    }

    public function home()
    {
        $sliders = Slider::where('is_active', 1)->get();
        $sliders = SliderResource::collection($sliders);

        $category = Category::whereRelation('services', 'is_active', 1)->get();

        $services = HomeCategoryResource::collection($category);

        if (Auth::check()) {
            $offers = Offer::where('is_active', 1)
                ->whereDate('start_date', '<=', date('Y-m-d'))
                ->whereDate('end_date', '>', date('Y-m-d'))
                ->whereHas('active_provider', function ($query) {
                    $query->where('is_verified', 1)->where('is_blocked', 0)->whereHas('providerInfo', function ($provider_query) {
                        $provider_query->where('is_approved', 1)->whereHas('user', function ($use_query) {
                            $use_query->whereHas('work_zones', function ($work_query) {
                                $work_query->where('city_id', Auth::user()->city_id);
                            });
                        });
                    });
                })
                ->get();
        } else {
            $offers = Offer::where('is_active', 1)
                ->whereDate('start_date', '<=', date('Y-m-d'))
                ->whereDate('end_date', '>', date('Y-m-d'))
                ->whereHas('active_provider', function ($query) {
                    $query->where('is_verified', 1)->where('is_blocked', 0)->whereHas('providerInfo', function ($provider_query) {
                        $provider_query->where('is_approved', 1);
                    });
                })
                ->get();
        }

        $offers = OfferResource::collection($offers);

        $home_services = Service::where('is_home', 1)
            ->where('is_active', 1)
            ->withCount('serviceProviders')
            ->orderBy('ranking', 'ASC')
            ->get();
        // $home_providers = [];
        /* foreach ($home_services as $hs) {
            if (Auth::check()) {
                $providers = User::where('is_verified', 1)->where('is_blocked', 0)->whereIn('type', ['provider', 'company'])
                    ->whereHas('services', function ($query) use ($hs) {
                        $query->where('service_id', $hs->id);
                    })
                    ->whereHas('providerInfo', function ($query) {
                        $query->where('is_approved', 1);
                    })->whereHas('work_zones', function ($query) {
                        $query->where('city_id',  Auth::user()->city_id);
                    })
                    ->get();
            } else {
                $providers = User::where('is_verified', 1)->where('is_blocked', 0)->whereIn('type', ['provider', 'company'])
                    ->whereHas('services', function ($query) use ($hs) {
                        $query->where('service_id', $hs->id);
                    })
                    ->whereHas('providerInfo', function ($query) {
                        $query->where('is_approved', 1);
                    })
                    ->get();
            };
            $home_providers[] = [
                'service' => new ServiceResource($hs),
                //'providers' => ProviderResource::collection($providers)
            ];
        } */

        $home_providers[] = [
            'service' => ServiceResource::collection($home_services),
            // 'providers' => ProviderResource::collection($providers)
        ];

        $popular_services = Service::where('is_active', 1)
            ->withCount('serviceProviders')
            ->orderBy('service_providers_count', 'desc')
            ->get();
        $popular_providers = [];
        foreach ($popular_services as $ps) {
            $user = User::find(request()->user_id);
            if (request()->user_id) {
                $providers = User::where('is_verified', 1)->where('is_blocked', 0)
                ->where('id', '!=', $user->id)
                ->whereIn('type', ['provider', 'company'])
                    ->whereHas('providerServices', function ($query) use ($ps) {
                        $query->where('service_id', $ps->id);
                    })
                    ->whereHas('providerInfo', function ($query) {
                        $query->where('is_approved', 1);
                    })->whereHas('providerWorkZones', function ($query) use ($user) {
                        $query->where('city_id', $user->city_id);
                    })
                    ->get();
            } else {
                $providers = User::where('is_verified', 1)->where('is_blocked', 0)->whereIn('type', ['provider', 'company'])
                    ->whereHas('providerServices', function ($query) use ($ps) {
                        $query->where('service_id', $ps->id);
                    })
                    ->whereHas('providerInfo', function ($query) {
                        $query->where('is_approved', 1);
                    })
                    ->get();
            }
            $popular_providers[] = [
                'service' => new ServiceResource($ps),
                'providers' => ProviderHomeResource::collection($providers),
            ];
        }

        // $popular_providers[] = [
        //     'service' => ServiceResource::collection($popular_services),
        //     'providers' => ProviderResource::collection($providers),
        // ];

        return response()->json(['code' => 200, 'success' => true, 'data' => [
            'sliders' => $sliders,
            'services' => $services,
            'offers' => $offers,
            'home_services' => $home_providers,
            'popular_services' => $popular_providers,
        ]]);
    }

    public function contactInfo()
    {
        $configurations = Configuration::where('section', 'contact')->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => null,
            'data' => $configurations,
        ], 200);
    }
}

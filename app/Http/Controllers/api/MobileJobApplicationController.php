<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Http\Requests\api\jobs\MobileJobDailyScheduleRequest;
use App\Http\Requests\api\jobs\MobileJobRequest;
use App\Http\Resources\MobileJobApplicationResource;
use App\Models\MobileJobApplication;
use App\Models\Order;
use App\Models\ProviderSchedule;
use App\Models\Service;
use App\Models\User;
use App\Models\UserLocation;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MobileJobApplicationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the user's mobile job applications
        $applications = MobileJobApplication::where('user_id', $user->id)
            ->with(['service'])
            ->latest()
            ->get();

        // Add action permissions for each application
        $applicationsWithActions = $applications->map(function ($application) {
            $application->can_edit = $application->status === 'pending';
            $application->can_delete = $application->status === 'pending';

            return $application;
        });

        return response()->json([
            'status' => 'success',
            'data' => MobileJobApplicationResource::collection($applicationsWithActions),
        ]);
    }

    /**
     * Store a new mobile job application.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(MobileJobRequest $request)
    {
        // Request is already validated by MobileJobRequest

        // Get the authenticated user
        $user = Auth::user();

        // Check if service exists
        $service = Service::find($request->service_id);
        if (!$service) {
            return response()->json([
                'status' => 'error',
                'message' => 'Service not found',
            ], 404);
        }

        // Format the schedule data
        $formattedSchedule = [];
        foreach ($request->schedule as $daySchedule) {
            if (!empty($daySchedule['hours'])) {
                $formattedSchedule[] = [
                    'day' => $daySchedule['day'],
                    'hours' => $daySchedule['hours'],
                ];
            }
        }

        // Get location from user's saved locations
        $location = UserLocation::where('id', $request->location_id)
            ->where('user_id', $user->id)
            ->first();

        if (!$location) {
            return response()->json([
                'status' => 'error',
                'message' => 'Location not found',
            ], 404);
        }

        // Create the mobile job application
        $application = new MobileJobApplication();
        $application->user_id = $user->id;
        $application->service_id = $request->service_id;
        $application->location_id = $location->id;
        $application->location_name = $location->name;
        $application->latitude = $location->latitude;
        $application->longitude = $location->longitude;

        $application->schedule = $formattedSchedule;
        $application->status = 'pending'; // Status is automatically set to pending
        $application->notes = $request->notes;
        $application->save();

        return response()->json([
            'status' => 'success',
            'message' => __('message.mobile_job_application_created'),
            'data' => new MobileJobApplicationResource($application),
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the mobile job application
        $application = MobileJobApplication::where('id', $id)
            ->where('user_id', $user->id)
            ->with(['service'])
            ->first();

        if (!$application) {
            return response()->json([
                'status' => 'error',
                'message' => 'Mobile job application not found',
            ], 404);
        }

        // Add action permissions
        $application->can_edit = $application->status === 'pending';
        $application->can_delete = $application->status === 'pending';

        return response()->json([
            'status' => 'success',
            'data' => new MobileJobApplicationResource($application),
        ]);
    }

    /**
     * Update the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function update(MobileJobRequest $request, $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the mobile job application
        $application = MobileJobApplication::where('id', $id)
            ->where('user_id', $user->id)
            ->first();

        if (!$application) {
            return response()->json([
                'status' => 'error',
                'message' => 'Mobile job application not found',
            ], 404);
        }

        // Check if the application can be updated (only pending applications can be updated)
        if ($application->status !== 'pending') {
            return response()->json([
                'status' => 'error',
                'message' => 'Only pending applications can be updated',
            ], 403);
        }

        // Request is already validated by MobileJobRequest

        // Update the mobile job application
        if ($request->has('service_id')) {
            $application->service_id = $request->service_id;
        }

        if ($request->has('location_id')) {
            // Get location from user's saved locations
            $location = UserLocation::where('id', $request->location_id)
                ->where('user_id', $user->id)
                ->first();

            if (!$location) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Location not found',
                ], 404);
            }

            // Update location details
            $application->location_name = $location->name;
            $application->latitude = $location->latitude;
            $application->longitude = $location->longitude;
        }

        if ($request->has('schedule')) {
            // Format the schedule data
            $formattedSchedule = [];
            foreach ($request->schedule as $daySchedule) {
                if (!empty($daySchedule['hours'])) {
                    $formattedSchedule[] = [
                        'day' => $daySchedule['day'],
                        'hours' => $daySchedule['hours'],
                    ];
                }
            }
            $application->schedule = $formattedSchedule;
        }

        if ($request->has('notes')) {
            $application->notes = $request->notes;
        }

        $application->save();

        return response()->json([
            'status' => 'success',
            'message' => __('message.mobile_job_application_updated'),
            'data' => new MobileJobApplicationResource($application),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the mobile job application
        $application = MobileJobApplication::where('id', $id)
            ->where('user_id', $user->id)
            ->first();

        if (!$application) {
            return response()->json([
                'status' => 'error',
                'message' => 'Mobile job application not found',
            ], 404);
        }

        // Check if the application can be deleted (only pending applications can be deleted)
        if ($application->status !== 'pending') {
            return response()->json([
                'status' => 'error',
                'message' => 'Only pending applications can be deleted',
            ], 403);
        }

        // Delete the mobile job application
        $application->delete();

        return response()->json([
            'status' => 'success',
            'message' => __('message.mobile_job_application_deleted'),
        ]);
    }

    /**
     * Delete the specified resource from storage using POST method with ID in request body.
     *
     * @return \Illuminate\Http\Response
     */
    public function deleteWithId(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:mobile_job_applications,id',
            'location_id' => 'nullable|exists:user_locations,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Get the authenticated user
        $user = Auth::user();

        // Get the mobile job application
        $application = MobileJobApplication::where('id', $request->id)
            ->where('user_id', $user->id)
            ->first();

        if (!$application) {
            return response()->json([
                'status' => 'error',
                'message' => 'Mobile job application not found',
            ], 404);
        }

        // Check if the application can be deleted (only pending applications can be deleted)
        if ($application->status !== 'pending') {
            return response()->json([
                'status' => 'error',
                'message' => 'Only pending applications can be deleted',
            ], 403);
        }

        // Delete the mobile job application
        $application->delete();

        return response()->json([
            'status' => 'success',
            'message' => __('message.mobile_job_application_deleted'),
            'location_id' => $request->location_id ?? null,
            'id' => $request->id,
        ]);
    }

    /**
     * Get available services for mobile job applications.
     *
     * @return \Illuminate\Http\Response
     */
    public function getServices()
    {
        // Get services with units 'meter' or 'hour'
        $services = Service::all();

        return response()->json([
            'status' => 'success',
            'data' => $services,
        ]);
    }

    /**
     * Get available weekdays.
     *
     * @return \Illuminate\Http\Response
     */
    public function getWeekdays()
    {
        $weekdays = [
            'saturday' => __('message.saturday'),
            'sunday' => __('message.sunday'),
            'monday' => __('message.monday'),
            'tuesday' => __('message.tuesday'),
            'wednesday' => __('message.wednesday'),
            'thursday' => __('message.thursday'),
            'friday' => __('message.friday'),
        ];

        return response()->json([
            'status' => 'success',
            'data' => $weekdays,
        ]);
    }

    /**
     * Get user locations.
     *
     * @return \Illuminate\Http\Response
     */
    public function getUserLocations()
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            // Get the user's locations
            $locations = UserLocation::where('user_id', $user->id)->get();

            // Return the locations with proper formatting
            return response()->json([
                'status' => true,
                'message' => __('message.user_locations_retrieved_successfully'),
                'data' => $locations->map(function ($location) {
                    return [
                        'id' => $location->id,
                        'name' => $location->name,
                        'latitude' => $location->latitude,
                        'longitude' => $location->longitude,
                        'user_id' => $location->user_id,
                        'created_at' => $location->created_at,
                        'updated_at' => $location->updated_at,
                    ];
                }),
            ]);
        } catch (\Exception $e) {
            \Log::error('Error retrieving user locations: '.$e->getMessage());

            return response()->json([
                'status' => false,
                'message' => __('message.failed_to_retrieve_user_locations'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Set weekly schedule.
     *
     * @return \Illuminate\Http\Response
     */
    public function setWeeklySchedule(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'schedule' => 'required|array',
            'schedule.saturday' => 'nullable|array',
            'schedule.saturday.*.start_time' => 'required_with:schedule.saturday|date_format:H:i',
            'schedule.saturday.*.duration' => 'required_with:schedule.saturday|integer|min:1|max:12',
            'schedule.sunday' => 'nullable|array',
            'schedule.sunday.*.start_time' => 'required_with:schedule.sunday|date_format:H:i',
            'schedule.sunday.*.duration' => 'required_with:schedule.sunday|integer|min:1|max:12',
            'schedule.monday' => 'nullable|array',
            'schedule.monday.*.start_time' => 'required_with:schedule.monday|date_format:H:i',
            'schedule.monday.*.duration' => 'required_with:schedule.monday|integer|min:1|max:12',
            'schedule.tuesday' => 'nullable|array',
            'schedule.tuesday.*.start_time' => 'required_with:schedule.tuesday|date_format:H:i',
            'schedule.tuesday.*.duration' => 'required_with:schedule.tuesday|integer|min:1|max:12',
            'schedule.wednesday' => 'nullable|array',
            'schedule.wednesday.*.start_time' => 'required_with:schedule.wednesday|date_format:H:i',
            'schedule.wednesday.*.duration' => 'required_with:schedule.wednesday|integer|min:1|max:12',
            'schedule.thursday' => 'nullable|array',
            'schedule.thursday.*.start_time' => 'required_with:schedule.thursday|date_format:H:i',
            'schedule.thursday.*.duration' => 'required_with:schedule.thursday|integer|min:1|max:12',
            'schedule.friday' => 'nullable|array',
            'schedule.friday.*.start_time' => 'required_with:schedule.friday|date_format:H:i',
            'schedule.friday.*.duration' => 'required_with:schedule.friday|integer|min:1|max:12',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Get the authenticated user
        $user = Auth::user();

        // Convert the schedule to the format expected by the application
        $formattedSchedule = [];

        foreach ($request->schedule as $day => $hours) {
            if (!empty($hours)) {
                $formattedSchedule[] = [
                    'day' => $day,
                    'hours' => $hours,
                ];
            }
        }

        // Check if location_id is provided
        if (!$request->has('location_id')) {
            return response()->json([
                'status' => 'error',
                'message' => 'Location ID is required',
            ], 422);
        }

        // Get location from user's saved locations
        $location = UserLocation::where('id', $request->location_id)
            ->where('user_id', $user->id)
            ->first();

        if (!$location) {
            return response()->json([
                'status' => 'error',
                'message' => 'Location not found',
            ], 404);
        }

        // Create or update the mobile job application
        $application = MobileJobApplication::updateOrCreate(
            ['user_id' => $user->id, 'status' => 'pending'],
            [
                'service_id' => $request->service_id ?? null,
                'location_name' => $location->name,
                'latitude' => $location->latitude,
                'longitude' => $location->longitude,
                'schedule' => $formattedSchedule,
                'notes' => $request->notes ?? null,
            ]
        );

        return response()->json([
            'status' => 'success',
            'message' => __('message.weekly_schedule_set'),
            'data' => $application,
        ]);
    }

    /**
     * Store a new mobile job application with daily schedule format.
     *
     * @return \Illuminate\Http\Response
     */
    public function storeDailySchedule(MobileJobDailyScheduleRequest $request)
    {
        // Request is already validated by MobileJobDailyScheduleRequest

        // Get the authenticated user
        $user = Auth::user();

        // Check if service exists
        $service = Service::find($request->service_id);
        if (!$service) {
            return response()->json([
                'status' => 'error',
                'message' => 'Service not found',
            ], 404);
        }

        // Format the schedule data from daily format to the format expected by the application
        $formattedSchedule = [];
        $days = ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'];

        foreach ($days as $day) {
            $scheduleKey = "{$day}_schedule";
            if ($request->has($scheduleKey) && !empty($request->$scheduleKey)) {
                $formattedSchedule[] = [
                    'day' => $day,
                    'hours' => $request->$scheduleKey,
                ];
            }
        }

        // Get location from user's saved locations
        $location = UserLocation::where('id', $request->location_id)
            ->where('user_id', $user->id)
            ->first();

        if (!$location) {
            return response()->json([
                'status' => 'error',
                'message' => 'Location not found',
            ], 404);
        }

        // Create the mobile job application
        $application = new MobileJobApplication();
        $application->user_id = $user->id;
        $application->service_id = $request->service_id;
        $application->location_id = $request->location_id;
        $application->latitude = $location->latitude;
        $application->longitude = $location->longitude;
        $application->schedule = $formattedSchedule;
        $application->status = 'pending'; // Status is automatically set to pending
        $application->notes = $request->notes;
        $application->save();

        return response()->json([
            'status' => 'success',
            'message' => __('message.mobile_job_application_created'),
            'data' => $application,
        ], 201);
    }

    /**
     * Update the specified resource with daily schedule format.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function updateDailySchedule(MobileJobDailyScheduleRequest $request, $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the mobile job application
        $application = MobileJobApplication::where('id', $id)
            ->where('user_id', $user->id)
            ->first();

        if (!$application) {
            return response()->json([
                'status' => 'error',
                'message' => 'Mobile job application not found',
            ], 404);
        }

        // Check if the application can be updated (only pending applications can be updated)
        if ($application->status !== 'pending') {
            return response()->json([
                'status' => 'error',
                'message' => 'Only pending applications can be updated',
            ], 403);
        }

        // Request is already validated by MobileJobDailyScheduleRequest

        // Update the mobile job application
        if ($request->has('service_id')) {
            $application->service_id = $request->service_id;
        }

        if ($request->has('location_id')) {
            // Get location from user's saved locations
            $location = UserLocation::where('id', $request->location_id)
                ->where('user_id', $user->id)
                ->first();

            if (!$location) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Location not found',
                ], 404);
            }

            // Update location details
            $application->location_name = $location->name;
            $application->latitude = $location->latitude;
            $application->longitude = $location->longitude;
        }

        // Format the schedule data from daily format to the format expected by the application
        $formattedSchedule = [];
        $days = ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        $hasScheduleChanges = false;

        foreach ($days as $day) {
            $scheduleKey = "{$day}_schedule";
            if ($request->has($scheduleKey)) {
                $hasScheduleChanges = true;
                if (!empty($request->$scheduleKey)) {
                    $formattedSchedule[] = [
                        'day' => $day,
                        'hours' => $request->$scheduleKey,
                    ];
                }
            }
        }

        if ($hasScheduleChanges) {
            $application->schedule = $formattedSchedule;
        }

        if ($request->has('notes')) {
            $application->notes = $request->notes;
        }

        $application->save();

        return response()->json([
            'status' => 'success',
            'message' => __('message.mobile_job_application_updated'),
            'data' => $application,
        ]);
    }

    /**
     * Set weekly schedule with daily format.
     *
     * @return \Illuminate\Http\Response
     */
    public function setDailySchedule(MobileJobDailyScheduleRequest $request)
    {
        // Request is already validated by MobileJobDailyScheduleRequest

        // Get the authenticated user
        $user = Auth::user();

        // Format the schedule data from daily format to the format expected by the application
        $formattedSchedule = [];
        $days = ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'];

        foreach ($days as $day) {
            $scheduleKey = "{$day}_schedule";
            if ($request->has($scheduleKey) && !empty($request->$scheduleKey)) {
                $formattedSchedule[] = [
                    'day' => $day,
                    'hours' => $request->$scheduleKey,
                ];
            }
        }

        // Get location from user's saved locations
        $location = UserLocation::where('id', $request->location_id)
            ->where('user_id', $user->id)
            ->first();

        if (!$location) {
            return response()->json([
                'status' => 'error',
                'message' => 'Location not found',
            ], 404);
        }

        // Create or update the mobile job application
        $application = MobileJobApplication::updateOrCreate(
            ['user_id' => $user->id, 'status' => 'pending'],
            [
                'service_id' => $request->service_id,
                'location_name' => $location->name,
                'latitude' => $location->latitude,
                'longitude' => $location->longitude,
                'schedule' => $formattedSchedule,
                'notes' => $request->notes ?? null,
            ]
        );

        return response()->json([
            'status' => 'success',
            'message' => __('message.daily_schedule_set'),
            'data' => $application,
        ]);
    }

    /**
     * Check for schedule conflicts.
     *
     * @return \Illuminate\Http\Response
     */
    public function checkScheduleConflicts(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'schedule' => 'required|array',
            'schedule.*.day' => 'required|string|in:sunday,monday,tuesday,wednesday,thursday,friday,saturday',
            'schedule.*.date' => 'required|date_format:Y-m-d',
            'schedule.*.hours' => 'required|array',
            'schedule.*.hours.*.start_time' => 'required|date_format:H:i',
            'schedule.*.hours.*.duration' => 'required|integer|min:1|max:12',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Get the authenticated user
        $user = Auth::user();

        // Check for conflicts
        $conflicts = [];

        foreach ($request->schedule as $scheduleItem) {
            $date = $scheduleItem['date'];
            $day = $scheduleItem['day'];

            foreach ($scheduleItem['hours'] as $hourItem) {
                $startTime = $hourItem['start_time'];
                $duration = $hourItem['duration'];

                // Calculate end time
                $startDateTime = Carbon::parse("$date $startTime");
                $duration = (int) $duration; // Convertir a entero
                $endDateTime = (clone $startDateTime)->addHours($duration);

                // Check for conflicts with provider schedules
                $providerSchedules = ProviderSchedule::where('user_id', $user->id)
                    ->where('date', $date)
                    ->get();

                foreach ($providerSchedules as $providerSchedule) {
                    $scheduleStart = Carbon::parse("$date {$providerSchedule->start_time}");
                    $scheduleEnd = Carbon::parse("$date {$providerSchedule->end_time}");

                    if ($startDateTime < $scheduleEnd && $endDateTime > $scheduleStart) {
                        $conflicts[] = [
                            'type' => 'provider_schedule',
                            'day' => $day,
                            'date' => $date,
                            'start_time' => $providerSchedule->start_time,
                            'end_time' => $providerSchedule->end_time,
                            'description' => 'Provider schedule conflict',
                        ];
                    }
                }

                // Check for conflicts with orders
                $orders = Order::where(function ($query) use ($user) {
                    $query->where('user_id', $user->id)
                        ->orWhere('provider_id', $user->id);
                })
                ->with(['orderSchedules' => function ($query) use ($date) {
                    $query->where('date', $date);
                }])
                ->get();

                foreach ($orders as $order) {
                    foreach ($order->orderSchedules as $orderSchedule) {
                        $scheduleStart = Carbon::parse("$date {$orderSchedule->start_time}");
                        $scheduleEnd = Carbon::parse("$date {$orderSchedule->end_time}");

                        if ($startDateTime < $scheduleEnd && $endDateTime > $scheduleStart) {
                            $conflicts[] = [
                                'type' => 'order',
                                'day' => $day,
                                'date' => $date,
                                'start_time' => $orderSchedule->start_time,
                                'end_time' => $orderSchedule->end_time,
                                'description' => 'Order #'.$order->id.' schedule conflict',
                            ];
                        }
                    }
                }
            }
        }

        return response()->json([
            'status' => 'success',
            'has_conflicts' => count($conflicts) > 0,
            'message' => count($conflicts) > 0 ? __('message.schedule_conflicts_found') : __('message.no_schedule_conflicts'),
            'conflicts' => $conflicts,
        ]);
    }

    /**
     * Check for schedule conflicts with daily format.
     *
     * @return \Illuminate\Http\Response
     */
    public function checkDailyScheduleConflicts(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'date' => 'required|date_format:Y-m-d',
            'saturday_schedule' => 'nullable|array',
            'saturday_schedule.*.start_time' => 'required_with:saturday_schedule|date_format:H:i',
            'saturday_schedule.*.duration' => 'required_with:saturday_schedule|integer|min:1|max:12',
            'sunday_schedule' => 'nullable|array',
            'sunday_schedule.*.start_time' => 'required_with:sunday_schedule|date_format:H:i',
            'sunday_schedule.*.duration' => 'required_with:sunday_schedule|integer|min:1|max:12',
            'monday_schedule' => 'nullable|array',
            'monday_schedule.*.start_time' => 'required_with:monday_schedule|date_format:H:i',
            'monday_schedule.*.duration' => 'required_with:monday_schedule|integer|min:1|max:12',
            'tuesday_schedule' => 'nullable|array',
            'tuesday_schedule.*.start_time' => 'required_with:tuesday_schedule|date_format:H:i',
            'tuesday_schedule.*.duration' => 'required_with:tuesday_schedule|integer|min:1|max:12',
            'wednesday_schedule' => 'nullable|array',
            'wednesday_schedule.*.start_time' => 'required_with:wednesday_schedule|date_format:H:i',
            'wednesday_schedule.*.duration' => 'required_with:wednesday_schedule|integer|min:1|max:12',
            'thursday_schedule' => 'nullable|array',
            'thursday_schedule.*.start_time' => 'required_with:thursday_schedule|date_format:H:i',
            'thursday_schedule.*.duration' => 'required_with:thursday_schedule|integer|min:1|max:12',
            'friday_schedule' => 'nullable|array',
            'friday_schedule.*.start_time' => 'required_with:friday_schedule|date_format:H:i',
            'friday_schedule.*.duration' => 'required_with:friday_schedule|integer|min:1|max:12',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Get the authenticated user
        $user = Auth::user();
        $date = $request->date;
        $conflicts = [];
        $days = ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'];

        foreach ($days as $day) {
            $scheduleKey = "{$day}_schedule";
            if ($request->has($scheduleKey) && !empty($request->$scheduleKey)) {
                foreach ($request->$scheduleKey as $hourItem) {
                    $startTime = $hourItem['start_time'];
                    $duration = $hourItem['duration'];

                    // Calculate end time
                    $startDateTime = Carbon::parse("$date $startTime");
                    $duration = (int) $duration; // Convertir a entero
                    $endDateTime = (clone $startDateTime)->addHours($duration);

                    // Check for conflicts with provider schedules
                    $providerSchedules = ProviderSchedule::where('user_id', $user->id)
                        ->where('date', $date)
                        ->get();

                    foreach ($providerSchedules as $providerSchedule) {
                        $scheduleStart = Carbon::parse("$date {$providerSchedule->start_time}");
                        $scheduleEnd = Carbon::parse("$date {$providerSchedule->end_time}");

                        if ($startDateTime < $scheduleEnd && $endDateTime > $scheduleStart) {
                            $conflicts[] = [
                                'type' => 'provider_schedule',
                                'day' => $day,
                                'date' => $date,
                                'start_time' => $providerSchedule->start_time,
                                'end_time' => $providerSchedule->end_time,
                                'description' => 'Provider schedule conflict',
                            ];
                        }
                    }

                    // Check for conflicts with orders
                    $orders = Order::where(function ($query) use ($user) {
                        $query->where('user_id', $user->id)
                            ->orWhere('provider_id', $user->id);
                    })
                    ->with(['orderSchedules' => function ($query) use ($date) {
                        $query->where('date', $date);
                    }])
                    ->get();

                    foreach ($orders as $order) {
                        foreach ($order->orderSchedules as $orderSchedule) {
                            $scheduleStart = Carbon::parse("$date {$orderSchedule->start_time}");
                            $scheduleEnd = Carbon::parse("$date {$orderSchedule->end_time}");

                            if ($startDateTime < $scheduleEnd && $endDateTime > $scheduleStart) {
                                $conflicts[] = [
                                    'type' => 'order',
                                    'day' => $day,
                                    'date' => $date,
                                    'start_time' => $orderSchedule->start_time,
                                    'end_time' => $orderSchedule->end_time,
                                    'description' => 'Order #'.$order->id.' schedule conflict',
                                ];
                            }
                        }
                    }
                }
            }
        }

        return response()->json([
            'status' => 'success',
            'has_conflicts' => count($conflicts) > 0,
            'message' => count($conflicts) > 0 ? __('message.schedule_conflicts_found') : __('message.no_schedule_conflicts'),
            'conflicts' => $conflicts,
        ]);
    }
}

<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Http\Requests\api\ValidateCouponRequest;
use App\Http\Resources\CouponResource;
use App\Models\Coupon;
use App\Models\Order;
use App\Services\CouponService;
use Illuminate\Support\Facades\Auth;

class CouponController extends ApiController
{
    /**
     * Validate a coupon code
     *
     * @param ValidateCouponRequest $request
     * @param CouponService $couponService
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateCoupon(ValidateCouponRequest $request, CouponService $couponService)
    {
        $code = $request->code;

        // IMPORTANT: For testing purposes, we're hardcoding the user ID to 8
        // In production, this should come from the authenticated user or the request
        $userId = 8; // Hardcoded for testing

        // Set the application locale based on the lang header
        $locale = $request->header('lang') ?? 'en';
        app()->setLocale($locale);

        // For debugging purposes, log the request details
        \Log::info('Validate coupon request', [
            'code' => $code,
            'user_id' => $userId,
            'request_user_id' => $request->user_id,
            'authenticated_user_id' => auth()->id(),
            'all_request_data' => $request->all(),
            'locale' => $locale,
            'app_locale' => app()->getLocale()
        ]);

        // Validate the coupon
        $result = $couponService->validateCoupon($code, $userId);

        if (!$result['valid']) {
            return $this->sendError($result['message']);
        }

        // No special handling needed anymore as we handle user_id parameter

        $coupon = $result['coupon'];
        $totalUsage = $result['usage_count'];

        // Coupon is valid, return coupon details using CouponResource
        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => $result['message'],
            'data' => new CouponResource($coupon, $totalUsage),
        ], 200);
    }
}

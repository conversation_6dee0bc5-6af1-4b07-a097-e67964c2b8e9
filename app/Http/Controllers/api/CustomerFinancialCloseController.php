<?php

namespace App\Http\Controllers\api;

use App\Http\Requests\api\CustomerFinancialCloseRequest;
use App\Http\Resources\api\CustomerFinancialCloseResource;
use App\Http\Resources\api\JobApplicationResource;
use App\Http\Resources\api\TaskResource;
use App\Models\JobApplication;
use App\Models\PaymentTransaction;
use App\Models\Task;
use App\Models\User;
use App\Services\PaymentService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CustomerFinancialCloseController extends ApiController
{
    /**
     * Close customer financial records for a specific month and year.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function closeCustomerFinancial(CustomerFinancialCloseRequest $request)
    {
        try {
            // Set application locale based on lang header
            $locale = $request->header('lang') ?? 'en';
            App::setLocale($locale);

            DB::beginTransaction();

            // Get authenticated user (customer)
            $customer = Auth::user();
            $customerId = $customer->id;

            $month = $request->input('month');
            $year = $request->input('year');
            $payerName = $request->input('payer_name', null);
            $paymentNotes = $request->input('payment_notes', null);

            // Verify that the authenticated user is a customer
            if ($customer->type !== 'user') {
                return $this->sendError(__('message.unauthorized_access'));
            }

            // Check if the month/year exists in tasks table for this customer
            $tasksExist = Task::where('user_id', $customerId)
                ->whereMonth('execution_date', $month)
                ->whereYear('execution_date', $year)
                ->exists();

            if (!$tasksExist) {
                return $this->sendError(__('message.no_tasks_found_for_month_year'));
            }

            // Get all tasks for this customer in the specified month/year with job application info
            $tasks = Task::with('jobApplication')
                ->where('user_id', $customerId)
                ->whereMonth('execution_date', $month)
                ->whereYear('execution_date', $year)
                ->get();
            if ($tasks->first()->mobile_payment_enabled == 0) {
                return $this->sendError(__('message.unauthorized_access'));
            }

            // Check if there are any unclosed tasks
            $unclosedTasks = $tasks->where('is_financially_closed_by_user', 0)->count();
            if ($unclosedTasks == 0) {
                return $this->sendError(__('message.month_already_closed'));
            }

            // Filter only unclosed tasks for calculation and closing
            $tasksToProcess = $tasks->where('is_financially_closed_by_user', 0);

            // Calculate totals only for unclosed tasks
            $calculationResult = $this->calculateCustomerTotals($tasksToProcess, $month, $year);

            // Close only unclosed tasks financially
            $closedTasks = Task::where('user_id', $customerId)
                ->whereMonth('execution_date', $month)
                ->whereYear('execution_date', $year)
                ->where('is_financially_closed_by_user', 0)  // Only unclosed tasks
                ->update([
                    'is_financially_closed_by_user' => 1,
                    'financially_closed_by_user' => Carbon::now(),
                ]);

            DB::commit();

            // Prepare response data
            $responseData = (object) [
                'customer_id' => $customerId,
                'customer_name' => $customer->name,
                'month' => $month,
                'year' => $year,
                'total_tasks' => $calculationResult['total_tasks'],
                'total_hours' => $calculationResult['total_hours'],
                'total_amount' => $calculationResult['total_amount'],
                'total_transportation' => $calculationResult['total_transportation'],
                'grand_total' => $calculationResult['grand_total'],
                'fixed_amount_tasks' => $calculationResult['fixed_amount_tasks'],
                'hourly_tasks' => $calculationResult['hourly_tasks'],
                'closed_tasks' => $closedTasks,
                'closure_date' => Carbon::now()->toDateTimeString(),
                'payer_name' => $payerName,
                'payment_notes' => $paymentNotes,
            ];

            return $this->sendResponse(
                new CustomerFinancialCloseResource($responseData),
                __('message.customer_financial_close_success')
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->sendError(__('message.customer_financial_close_failed').': '.$e->getMessage());
        }
    }

    private function calculateCustomerTotals($tasks, $month, $year)
    {
        $totalTasks = $tasks->count();
        $totalHours = 0;
        $totalAmount = 0;
        $totalTransportation = 0;
        $fixedAmountTasks = 0;
        $hourlyTasks = 0;
        $processedJobApplications = []; // Track processed job applications for fixed amounts
        $processedTransportation = []; // Track processed transportation per job_application_id

        foreach ($tasks as $task) {
            // Get calculation type from job application
            $calculationType = $task->jobApplication->calculation_type ?? 'hourly';

            \Log::info("Processing Task ID: {$task->id}, Job Application ID: {$task->job_application_id}, Calculation Type: '{$calculationType}'");

            // Count task types based on job application calculation type
            if ($calculationType === 'fixed_amount' || $calculationType === 'fixed') {
                ++$fixedAmountTasks;
            } else {
                ++$hourlyTasks;
            }

            // Calculate hours (for all tasks)
            if ($task->duration_minutes > 0) {
                $totalHours += $task->duration_minutes / 60;
            }

            // Calculate amounts based on job application calculation type
            if ($calculationType === 'fixed_amount' || $calculationType === 'fixed') {
                // Fixed amount: calculate once per job_application_id per month (no repetition)
                // Even cancelled tasks are counted for fixed amount
                if (!in_array($task->job_application_id, $processedJobApplications)) {
                    // Check if fixed amount has already been paid for this job application in this month
                    $fixedAmountAlreadyPaid = Task::where('job_application_id', $task->job_application_id)
                        ->whereMonth('execution_date', $month)
                        ->whereYear('execution_date', $year)
                        ->where('is_financially_closed_by_user', 1)
                        ->exists();

                    // Only add fixed amount if it hasn't been paid yet
                    if (!$fixedAmountAlreadyPaid) {
                        $fixedAmountValue = $task->jobApplication->fixed_amount ?? 0;
                        \Log::info("Adding fixed amount: {$fixedAmountValue} for job_application_id: {$task->job_application_id}");
                        $totalAmount += $fixedAmountValue;
                    }
                    $processedJobApplications[] = $task->job_application_id;
                }
            } else {
                // Hourly calculation: multiply hours by system_hourly_cost for each task
                // Cancelled hourly tasks don't count for amount calculation
                if ($task->status !== 'cancelled' && $task->duration_minutes > 0) {
                    $taskHours = $task->duration_minutes / 60;
                    $hourlyRate = $task->jobApplication->system_hourly_cost ?? 0;
                    $hourlyAmount = $taskHours * $hourlyRate;
                    \Log::info("Adding hourly amount: {$hourlyAmount} (hours: {$taskHours} × rate: {$hourlyRate}) for task_id: {$task->id}");
                    $totalAmount += $hourlyAmount;
                }
            }

            // Calculate transportation (once per job_application_id per month)
            // Only if transportation hasn't been paid for this job_application_id in this month
            if (!in_array($task->job_application_id, $processedTransportation)) {
                // Check if transportation has already been paid for this job application in this month
                $transportationAlreadyPaid = Task::where('job_application_id', $task->job_application_id)
                    ->whereMonth('execution_date', $month)
                    ->whereYear('execution_date', $year)
                    ->where('is_financially_closed_by_user', 1)
                    ->exists();

                // Only add transportation cost if it hasn't been paid yet
                if (!$transportationAlreadyPaid) {
                    $transportationCost = $task->jobApplication->system_transportation_cost ?? 0;
                    \Log::info("Adding transportation: {$transportationCost} for job_application_id: {$task->job_application_id}");
                    $totalTransportation += $transportationCost;
                }
                $processedTransportation[] = $task->job_application_id;
            }
        }

        \Log::info('Final Calculation Results:');
        \Log::info("Total Tasks: {$totalTasks}");
        \Log::info("Fixed Amount Tasks: {$fixedAmountTasks}");
        \Log::info("Hourly Tasks: {$hourlyTasks}");
        \Log::info("Total Hours: {$totalHours}");
        \Log::info("Total Amount: {$totalAmount}");
        \Log::info("Total Transportation: {$totalTransportation}");
        \Log::info('Grand Total: '.($totalAmount + $totalTransportation));

        return [
            'total_tasks' => $totalTasks,
            'total_hours' => $totalHours,
            'total_amount' => $totalAmount,
            'total_transportation' => $totalTransportation,
            'grand_total' => $totalAmount + $totalTransportation,
            'fixed_amount_tasks' => $fixedAmountTasks,
            'hourly_tasks' => $hourlyTasks,
        ];
    }

    public function makePayment(Request $request)
    {
        Log::info('Starting makePayment method');
        Log::info('Request data: ' . json_encode($request->all()));
        
        // بدء المعاملة
        DB::beginTransaction();
        Log::info('Transaction started');

        try {
            // التحقق مما إذا كان الطلب للتاسك أو للجوب أبليكيشن
            if ($request->task_id) {
                Log::info('Processing payment for Task ID: ' . $request->task_id);
                
                $task = Task::where('user_id', Auth::user()->id)->where('id', $request->task_id)->first();
                if (!$task) {
                    Log::error('Task not found for ID: ' . $request->task_id);
                    return $this->sendError(__('response.This Task Not Found In Your Tasks'));
                }
                
                Log::info('Task found: ' . json_encode($task->toArray()));

                if (in_array($task->status, ['pending', 'approved', 'completed'])) {
                    Log::info('Task status is valid: ' . $task->status);
                    
                    $transaction = new PaymentTransaction();
                    $transaction->task_id = $task->id;
                    $transaction->user_id = $task->user?->id;
                    $transaction->transaction_for = 'total_amount';
                    $amount = $task->total_payment + ($request->tips ?? 0) - $task->deposit_amount;
                    $transaction->amount = $amount;
                    $transaction->save();
                    
                    Log::info('Transaction created: ' . json_encode($transaction->toArray()));

                    $task->deposit_amount += $amount;
                    $task->save();
                    Log::info('Task deposit amount updated: ' . $task->deposit_amount);

                    if ($amount > 0) {
                        Log::info('Amount is positive, creating payment link');
                        
                        $PaymentService = new PaymentService();
                        $payment_link_data = $PaymentService->createPayment($transaction);
                        Log::info('Payment service response: ' . json_encode($payment_link_data));
                        
                        if (
                            array_key_exists('results', $payment_link_data)
                            && array_key_exists('status', $payment_link_data['results'])
                            && $payment_link_data['results']['status'] == 'success'
                        ) {
                            Log::info('Payment link created successfully');
                            
                            $payment_link = $payment_link_data['data']['payment_page_link'];
                            $transaction->invoice_logid = $payment_link_data['data']['page_request_uid'];
                            $transaction->save();
                            Log::info('Transaction updated with invoice_logid: ' . $transaction->invoice_logid);

                            // تأكيد المعاملة
                            DB::commit();
                            Log::info('Transaction committed');

                            $task = new TaskResource($task);
                            Log::info('Returning success response with payment link');

                            return response()->json([
                                'success' => true,
                                'code' => 1,
                                'message' => null,
                                'data' => ['task_details' => $task, 'payment_link' => $payment_link],
                            ], 200);
                        } elseif (
                            array_key_exists('results', $payment_link_data)
                            && array_key_exists('status', $payment_link_data['results'])
                            && $payment_link_data['results']['status'] == 'error'
                        ) {
                            // التراجع عن المعاملة
                            DB::rollBack();
                            Log::error('Payment service returned error: ' . json_encode($payment_link_data['data']));

                            return $this->sendError(__('response.'.$payment_link_data['data'][0]));
                        } else {
                            // التراجع عن المعاملة
                            DB::rollBack();
                            Log::error('Unknown error in payment transaction');

                            return $this->sendError(__('response.Error In Payment Transaction'));
                        }
                    } else {
                        Log::info('Amount is zero or negative, no payment link needed');
                        
                        $transaction->confirmed = 0;
                        $transaction->save();
                        Log::info('Transaction marked as not confirmed');

                        // تأكيد المعاملة
                        DB::commit();
                        Log::info('Transaction committed');

                        $task = new TaskResource($task);
                        Log::info('Returning success response without payment link');

                        return response()->json([
                            'success' => true,
                            'code' => 1,
                            'message' => null,
                            'data' => ['task_details' => $task, 'payment_link' => null],
                        ], 200);
                    }
                } else {
                    // التراجع عن المعاملة
                    DB::rollBack();
                    Log::error('Invalid task status: ' . $task->status);

                    return $this->sendError(__('response.This Task Is').' '.$task->status);
                }
            } else {
                // الكود الأصلي للجوب أبليكيشن
                Log::info('Processing payment for Job Application ID: ' . $request->job_application_id);
                
                $jobApplication = JobApplication::where('user_id', Auth::user()->id)->where('id', $request->job_application_id)->first();
                if (!$jobApplication) {
                    Log::error('Job Application not found for ID: ' . $request->job_application_id);
                    return $this->sendError(__('response.This Job Application Not Found In Your Applications'));
                }
                
                Log::info('Job Application found: ' . json_encode($jobApplication->toArray()));

                if (in_array($jobApplication->status, ['pending', 'approved', 'completed'])) {
                    Log::info('Job Application status is valid: ' . $jobApplication->status);
                    
                    $transaction = new PaymentTransaction();
                    $transaction->job_application_id = $jobApplication->id;
                    $transaction->user_id = $jobApplication->user?->id;
                    $transaction->transaction_for = 'total_amount';
                    $amount = $jobApplication->total_payment + ($request->tips ?? 0) - $jobApplication->deposit_amount;
                    $transaction->amount = $amount;
                    $transaction->save();
                    
                    Log::info('Transaction created: ' . json_encode($transaction->toArray()));

                    $jobApplication->deposit_amount += $amount;
                    $jobApplication->save();
                    Log::info('Job Application deposit amount updated: ' . $jobApplication->deposit_amount);

                    if ($amount > 0) {
                        Log::info('Amount is positive, creating payment link');
                        
                        $PaymentService = new PaymentService();
                        $payment_link_data = $PaymentService->createPayment($transaction);
                        Log::info('Payment service response: ' . json_encode($payment_link_data));
                        
                        if (
                            array_key_exists('results', $payment_link_data)
                            && array_key_exists('status', $payment_link_data['results'])
                            && $payment_link_data['results']['status'] == 'success'
                        ) {
                            Log::info('Payment link created successfully');
                            
                            $payment_link = $payment_link_data['data']['payment_page_link'];
                            $transaction->invoice_logid = $payment_link_data['data']['page_request_uid'];
                            $transaction->save();
                            Log::info('Transaction updated with invoice_logid: ' . $transaction->invoice_logid);

                            // تأكيد المعاملة
                            DB::commit();
                            Log::info('Transaction committed');

                            $jobApplication = new JobApplicationResource($jobApplication);
                            Log::info('Returning success response with payment link');

                            return response()->json([
                                'success' => true,
                                'code' => 1,
                                'message' => null,
                                'data' => ['job_application_details' => $jobApplication, 'payment_link' => $payment_link],
                            ], 200);
                        } elseif (
                            array_key_exists('results', $payment_link_data)
                            && array_key_exists('status', $payment_link_data['results'])
                            && $payment_link_data['results']['status'] == 'error'
                        ) {
                            // التراجع عن المعاملة
                            DB::rollBack();
                            Log::error('Payment service returned error: ' . json_encode($payment_link_data['data']));

                            return $this->sendError(__('response.'.$payment_link_data['data'][0]));
                        } else {
                            // التراجع عن المعاملة
                            DB::rollBack();
                            Log::error('Unknown error in payment transaction');

                            return $this->sendError(__('response.Error In Payment Transaction'));
                        }
                    } else {
                        Log::info('Amount is zero or negative, no payment link needed');
                        
                        $transaction->confirmed = 0;
                        $transaction->save();
                        Log::info('Transaction marked as not confirmed');

                        // تأكيد المعاملة
                        DB::commit();
                        Log::info('Transaction committed');

                        $jobApplication = new JobApplicationResource($jobApplication);
                        Log::info('Returning success response without payment link');

                        return response()->json([
                            'success' => true,
                            'code' => 1,
                            'message' => null,
                            'data' => ['job_application_details' => $jobApplication, 'payment_link' => null],
                        ], 200);
                    }
                } else {
                    // التراجع عن المعاملة
                    DB::rollBack();
                    Log::error('Invalid job application status: ' . $jobApplication->status);

                    return $this->sendError(__('response.This Job Application Is').' '.$jobApplication->status);
                }
            }
        } catch (\Exception $e) {
            // التراجع عن المعاملة في حالة حدوث خطأ
            DB::rollBack();
            Log::error('Exception in makePayment: ' . $e->getMessage());
            Log::error('Exception trace: ' . $e->getTraceAsString());

            return $e; // $this->sendError();
        }
    }
}

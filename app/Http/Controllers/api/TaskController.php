<?php

namespace App\Http\Controllers\api;

use App\Http\Requests\api\Task\UpdateTaskTimeRequest;
use App\Http\Resources\JobApplicationSimpleResource;
use App\Http\Resources\LocationResource;
use App\Http\Resources\ServiceResource;
use App\Http\Resources\TaskTimeResource;
use App\Models\JobApplication;
use App\Models\JobApplicationContracts;
use App\Models\Order;
use App\Models\ProviderSchedule;
use App\Models\ProviderWorkTime;
use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class TaskController extends ApiController
{
    public function index(Request $request)
    {
        $lang = $request->header('lang', 'en');

        // Get all job applications with provider, provider info, and service information
        $jobApplications = JobApplication::with([
            'user:id,name',
            'provider:id,name',
            'provider.providerInfo',
            'service:id,name',
            'tasks',
        ])->get();

        // Check if there are any job applications
        if ($jobApplications->isEmpty()) {
            return response()->json([
                'success' => true,
                'message' => __('message.no_job_applications_found', [], $lang),
                'data' => [],
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => __('message.job_applications_retrieved', [], $lang),
            'data' => $jobApplications,
        ]);
    }

    /**
     * Get job application details by ID.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    // public function show($id)
    // {
    //     // Get the job application with provider, provider info, and service information
    //     $jobApplication = JobApplication::with([
    //         'provider:id,name',
    //         'provider.providerInfo',
    //         'service:id,name',
    //     ])->find($id);

    //     if (!$jobApplication) {
    //         return response()->json([
    //             'success' => false,
    //             'message' => __('message.job_application_not_found'),
    //         ], 404);
    //     }

    //     return response()->json([
    //         'success' => true,
    //         'message' => __('message.job_application_retrieved'),
    //         'data' => new JobApplicationSimpleResource($jobApplication),
    //     ]);
    // }

    /**
     * Get all calendar tasks for a provider for a given month (job_applications, orders, tasks).
     * Request: ?month=2025-05 (format: YYYY-MM)
     * Requires auth (provider).
     */
    public function getMonthlyCalendarProvider(Request $request)
    {
        $lang = $request->header('lang', 'en');

        // add vaildaton for month
        $user = auth()->user();
        $providerId = $user->id;
        $month = $request->input('month'); // format: YYYY-MM
        if (!$month) {
            return response()->json(['success' => false, 'message' => __('message.month_required', [], $lang)], 422);
        }
        $startDate = Carbon::parse($month.'-01')->startOfMonth();
        $endDate = (clone $startDate)->endOfMonth();

        // 1. جلب كل job_applications المتكررة لهذا المزود
        $jobApps = JobApplication::where('provider_id', $providerId)
            ->where('is_active', true)
            // ->where('is_recurring', true)
            ->get();

        // 2. جلب كل المهام المنفذة لهذا المزود في هذا الشهر
        $tasks = Task::where('provider_id', $providerId)
            ->whereBetween('execution_date', [$startDate->toDateString(), $endDate->toDateString()])
            ->get();

        // 3. جلب كل الحجوزات (orders) لهذا المزود في هذا الشهر
        $orders = Order::where('provider_id', $providerId)
            ->whereBetween('start_date', [$startDate->toDateString(), $endDate->toDateString()])
            ->get();

        $calendar = [];
        // 4. توليد كل المهام المتكررة (job_applications) لكل يوم وفترة زمنية
        foreach ($jobApps as $jobApp) {
            $createdAt = $jobApp->created_at->copy()->startOfDay();
            $schedule = $jobApp->schedule ?? [];
            foreach ($schedule as $item) {
                $dayName = strtolower($item['day']);
                foreach ($item['hours'] as $hour) {
                    // لكل يوم في الشهر يطابق اليوم المطلوب
                    $dId = $hour['d_id'] ?? null;
                    $startTime = $hour['start_time'];
                    $endTime = Carbon::parse($startTime)->addHours((int) $hour['duration'])->format('H:i');
                    $period = \Carbon\CarbonPeriod::create($startDate, $endDate);
                    foreach ($period as $date) {
                        if ($date->lt($createdAt)) {
                            continue;
                        } // لا نولد قبل تاريخ الإنشاء
                        if ($date->format('l') === ucfirst($dayName)) {
                            // تحقق هل هناك task منفذ
                            $task = $tasks->first(function ($t) use ($jobApp, $dId, $date) {
                                return $t->job_application_id == $jobApp->id && $t->d_id == $dId && $t->execution_date == $date->toDateString();
                            });
                            $today = Carbon::today();
                            $status = $task ? ($task->start_time && !$task->end_time ? 'started' : ($task->start_time && $task->end_time ? 'ended' : '')) : ($date->lt($today) ? 'Absent' : 'Not_started');
                            $service = $jobApp->Service;
                            $location = $jobApp->location;
                            $calendar[] = [
                                'type' => 'job_application',
                                'job_application_id' => $jobApp->id,
                                'd_id' => $dId,
                                'date' => $date->toDateString(),
                                'day' => $dayName,
                                'start_time' => $startTime,
                                'end_time' => $endTime,
                                'status' => $status,
                                'service' => ServiceResource::make($service),
                                'location' => LocationResource::make($location),
                                'task' => $task ? [
                                    'id' => $task->id,
                                    'start_time' => $task->start_time,
                                    'end_time' => $task->end_time,
                                ] : null,
                            ];
                        }
                    }
                }
            }
        }
        // 5. أضف الحجوزات من orders
        foreach ($orders as $order) {
            // $oservice = $order->typable_id;
            $calendar[] = [
                'type' => 'order',
                'order_id' => $order->id,
                'date' => $order->start_date,
                'day' => strtolower(Carbon::parse($order->start_date)->format('l')),
                'start_time' => $order->start_time ?? $order->start_date,
                'end_time' => $order->end_time ?? $order->end_date,
                'status' => $order->status,
                // 'service' => ServiceResource::make($order->typable),
                'service' => ServiceResource::make($order->typable),
                'address' => $order->address,
                'lat' => $order->user_lat,
                'long' => $order->user_long,
            ];
        }
        // ترتيب النتائج حسب التاريخ والوقت
        usort($calendar, function ($a, $b) {
            $cmp = strcmp($a['date'], $b['date']);
            if ($cmp === 0) {
                return strcmp($a['start_time'], $b['start_time']);
            }

            return $cmp;
        });

        return response()->json([
            'success' => true,
            'month' => $month,
            'data' => $calendar,
        ]);
    }

    public function getMonthlyCalendarUser(Request $request)
    {
        $lang = $request->header('lang', 'en');

        $user = auth()->user();
        $userId = $user->id;

        // Get the month parameter from the request
        $month = $request->input('month'); // format: YYYY-MM
        if (!$month) {
            return response()->json(['success' => false, 'message' => __('message.month_required', [], $lang)], 422);
        }

        $startDate = Carbon::parse($month.'-01')->startOfMonth();
        $endDate = (clone $startDate)->endOfMonth();

        // 1. Get all job applications for this user
        $jobApps = JobApplication::where('user_id', $userId)
            ->where('is_active', true)
            ->get();

        // 2. Get all tasks executed for this user in this month
        $tasks = Task::whereHas('jobApplication', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })
            ->whereBetween('execution_date', [$startDate->toDateString(), $endDate->toDateString()])
            ->get();

        // 3. Get all bookings (orders) for this user in this month
        $orders = Order::where('user_id', $userId)
            ->whereBetween('start_date', [$startDate->toDateString(), $endDate->toDateString()])
            ->get();

        $calendar = [];

        // 4. Generate all recurring tasks (job_applications) for each day and time period
        foreach ($jobApps as $jobApp) {
            $createdAt = $jobApp->created_at->copy()->startOfDay();
            $schedule = $jobApp->schedule ?? [];
            foreach ($schedule as $item) {
                $dayName = strtolower($item['day']);
                foreach ($item['hours'] as $hour) {
                    // For each day in the month that matches the required day
                    $dId = $hour['d_id'] ?? null;
                    $startTime = $hour['start_time'];
                    $endTime = Carbon::parse($startTime)->addHours((int) $hour['duration'])->format('H:i');
                    $period = \Carbon\CarbonPeriod::create($startDate, $endDate);
                    foreach ($period as $date) {
                        if ($date->lt($createdAt)) {
                            continue;
                        } // Don't generate before creation date
                        if ($date->format('l') === ucfirst($dayName)) {
                            // Check if there is an executed task
                            $task = $tasks->first(function ($t) use ($jobApp, $dId, $date) {
                                return $t->job_application_id == $jobApp->id && $t->d_id == $dId && $t->execution_date == $date->toDateString();
                            });
                            $status = $task ? ($task->start_time && !$task->end_time ? 'started' : ($task->start_time && $task->end_time ? 'ended' : '')) : ($date->isPast() ? 'Absent' : 'Not_started');
                            $service = $jobApp->Service;
                            $location = $jobApp->location;
                            $provider = $jobApp->provider;

                            // استخدام أوقات المهمة الفعلية إذا كانت متوفرة، وإلا استخدام الأوقات المخططة
                            $displayStartTime = ($task && $task->start_time) ? $task->start_time : $startTime;
                            $displayEndTime = ($task && $task->end_time) ? $task->end_time : $endTime;

                            $calendar[] = [
                                'type' => 'job_application',
                                'job_application_id' => $jobApp->id,
                                'd_id' => $dId,
                                'date' => $date->toDateString(),
                                'day' => $dayName,
                                'start_time' => $displayStartTime,
                                'end_time' => $displayEndTime,
                                'status' => $status,
                                'service' => ServiceResource::make($service),
                                'location' => LocationResource::make($location),
                                'provider' => [
                                    'id' => $provider->id,
                                    'name' => $provider->name,
                                    'profile_photo' => $provider->profile_photo_url,
                                ],
                                'task' => $task ? [
                                    'id' => $task->id,
                                    'start_time' => $task->start_time,
                                    'end_time' => $task->end_time,
                                ] : null,
                            ];
                        }
                    }
                }
            }
        }

        // 5. Add bookings from orders
        foreach ($orders as $order) {
            $provider = $order->provider;
            $calendar[] = [
                'type' => 'order',
                'order_id' => $order->id,
                'date' => $order->start_date,
                'day' => strtolower(Carbon::parse($order->start_date)->format('l')),
                'start_time' => $order->start_time ?? $order->start_date,
                'end_time' => $order->end_time ?? $order->end_date,
                'status' => $order->status,
                'service' => ServiceResource::make($order->typable),
                'address' => $order->address,
                'provider' => [
                    'id' => $provider->id,
                    'name' => $provider->name,
                    'profile_photo' => $provider->profile_photo_url,
                ],
            ];
        }

        // Sort results by date and time
        usort($calendar, function ($a, $b) {
            $cmp = strcmp($a['date'], $b['date']);
            if ($cmp === 0) {
                return strcmp($a['start_time'], $b['start_time']);
            }

            return $cmp;
        });

        return response()->json([
            'success' => true,
            'month' => $month,
            'data' => $calendar,
        ]);
        // $user = $request->provider;provider_id
        $providerId = $user->id;
        // $providerId = $request->provider_id;
        $month = $request->input('month'); // format: YYYY-MM
        if (!$month) {
            return response()->json(['success' => false, 'message' => __('message.month_required', [], $lang)], 422);
        }
        $startDate = Carbon::parse($month.'-01')->startOfMonth();
        $endDate = (clone $startDate)->endOfMonth();

        // 1. جلب كل job_applications المتكررة لهذا المزود
        $jobApps = JobApplication::where('provider_id', $providerId)
            ->where('is_active', true)
            // ->where('is_recurring', true)
            ->get();

        // 2. جلب كل المهام المنفذة لهذا المزود في هذا الشهر
        $tasks = Task::where('provider_id', $providerId)
            ->whereBetween('execution_date', [$startDate->toDateString(), $endDate->toDateString()])
            ->get();

        // 3. جلب كل الحجوزات (orders) لهذا المزود في هذا الشهر
        $orders = Order::where('provider_id', $providerId)
            ->whereBetween('start_date', [$startDate->toDateString(), $endDate->toDateString()])
            ->get();

        $calendar = [];
        // 4. توليد كل المهام المتكررة (job_applications) لكل يوم وفترة زمنية
        foreach ($jobApps as $jobApp) {
            $createdAt = $jobApp->created_at->copy()->startOfDay();
            $schedule = $jobApp->schedule ?? [];
            foreach ($schedule as $item) {
                $dayName = strtolower($item['day']);
                foreach ($item['hours'] as $hour) {
                    // لكل يوم في الشهر يطابق اليوم المطلوب
                    $dId = $hour['d_id'] ?? null;
                    $startTime = $hour['start_time'];
                    $endTime = Carbon::parse($startTime)->addHours((int) $hour['duration'])->format('H:i');
                    $period = \Carbon\CarbonPeriod::create($startDate, $endDate);
                    foreach ($period as $date) {
                        if ($date->lt($createdAt)) {
                            continue;
                        } // لا نولد قبل تاريخ الإنشاء
                        if ($date->format('l') === ucfirst($dayName)) {
                            // تحقق هل هناك task منفذ
                            $task = $tasks->first(function ($t) use ($jobApp, $dId, $date) {
                                return $t->job_application_id == $jobApp->id && $t->d_id == $dId && $t->execution_date == $date->toDateString();
                            });
                            $status = $task ? __('message.task_executed', [], $lang) : __('message.task_not_executed', [], $lang);
                            $calendar[] = [
                                'type' => 'job_application',
                                'job_application_id' => $jobApp->id,
                                'd_id' => $dId,
                                'date' => $date->toDateString(),
                                'day' => $dayName,
                                'start_time' => $startTime,
                                'end_time' => $endTime,
                                'status' => $status,
                                'task' => $task ? [
                                    'id' => $task->id,
                                    'start_time' => $task->start_time,
                                    'end_time' => $task->end_time,
                                ] : null,
                            ];
                        }
                    }
                }
            }
        }
        // 5. أضف الحجوزات من orders
        foreach ($orders as $order) {
            $calendar[] = [
                'type' => 'order',
                'order_id' => $order->id,
                'date' => $order->start_date,
                'day' => strtolower(Carbon::parse($order->start_date)->format('l')),
                'start_time' => $order->start_time ?? $order->start_date,
                'end_time' => $order->end_time ?? $order->end_date,
                'status' => __('message.order_booking', [], $lang),
            ];
        }
        // ترتيب النتائج حسب التاريخ والوقت
        usort($calendar, function ($a, $b) {
            $cmp = strcmp($a['date'], $b['date']);
            if ($cmp === 0) {
                return strcmp($a['start_time'], $b['start_time']);
            }

            return $cmp;
        });

        return response()->json([
            'success' => true,
            'month' => $month,
            'data' => $calendar,
        ]);
    }
    /**
     * Get available work days and time slots for a provider for an entire month.
     * Returns data organized by work days with available time slots for each day.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    // public function index(Request $request)
    // {
    //     // Validate request
    //     $validator = Validator::make($request->all(), [
    //         'provider_id' => 'required|exists:users,id',
    //         'month' => 'required|integer|min:1|max:12',
    //         'year' => 'required|integer|min:2000|max:2040',
    //         'duration' => 'nullable|integer|min:1|max:12',
    //         'job_id' => 'nullable|exists:job_applications,id',
    //     ]);

    //     if ($validator->fails()) {
    //         return response()->json([
    //             'success' => false,
    //             'message' => $validator->errors()->first(),
    //         ], 422);
    //     }

    //     // Get request parameters
    //     $providerId = $request->provider_id;
    //     $month = $request->month;
    //     $year = $request->year;
    //     $duration = $request->duration ?? 1; // Default duration is 1 hour if not specified
    //     $jobId = $request->job_id;

    //     // Create a Carbon instance for the first day of the month
    //     $firstDayOfMonth = Carbon::createFromDate($year, $month, 1);
    //     $lastDayOfMonth = $firstDayOfMonth->copy()->endOfMonth();

    //     // Initialize result array
    //     $workDays = [];
    //     $workDaysWithJobs = [];

    //     // Loop through each day of the month
    //     $currentDay = $firstDayOfMonth->copy();
    //     while ($currentDay->lte($lastDayOfMonth)) {
    //         $date = $currentDay->format('Y-m-d');
    //         $dayName = strtolower($currentDay->format('l')); // Get day name (e.g., 'monday')

    //         // Get provider work times for this day
    //         $providerWorkTime = ProviderWorkTime::where('user_id', $providerId)
    //             ->where('day', $dayName)
    //             ->first();

    //         // If provider doesn't work on this day, skip to next day
    //         if (!$providerWorkTime) {
    //             $currentDay->addDay();
    //             continue;
    //         }

    //         // Get provider schedules for this day
    //         $providerSchedules = ProviderSchedule::where('user_id', $providerId)
    //             ->where('day', $dayName)
    //             ->get();

    //         if ($providerSchedules->isEmpty()) {
    //             $currentDay->addDay();
    //             continue;
    //         }

    //         // Get existing orders for the provider on the given date
    //         $existingOrders = Order::where('provider_id', $providerId)
    //             ->whereDate('start_date', $date)
    //             ->whereIn('status', ['approved', 'confirmed', 'completed'])
    //             ->get();

    //         // Get existing job applications for the provider on the given date
    //         $existingJobApplications = JobApplication::where('provider_id', $providerId)
    //             ->where('status', '!=', 'rejected')
    //             ->where('status', '!=', 'cancelled')
    //             ->when($jobId, fn ($query) => $query->where('id', '!=', $jobId))
    //             ->where(function ($query) use ($date) {
    //                 $query->whereJsonContains('schedule', ['date' => $date])
    //                     ->orWhereJsonContains('schedule->date', $date);
    //             })
    //             ->get();

    //         // Get current job application schedule if job_id is provided
    //         $currentJobSlots = [];
    //         if ($jobId) {
    //             $currentJobApp = JobApplication::find($jobId);
    //             if ($currentJobApp) {
    //                 $scheduleData = $currentJobApp->schedule;

    //                 if (is_string($scheduleData) && !empty($scheduleData)) {
    //                     $decoded = @json_decode($scheduleData, true);
    //                     $scheduleData = (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) ? $decoded : [];
    //                 }

    //                 if (is_array($scheduleData)) {
    //                     foreach ($scheduleData as $item) {
    //                         if (isset($item['date']) && $item['date'] == $date
    //                             && isset($item['start_time']) && isset($item['end_time'])) {
    //                             $currentJobSlots[] = [
    //                                 'start_time' => $item['start_time'],
    //                                 'end_time' => $item['end_time'],
    //                                 'is_current_job' => true,
    //                             ];
    //                         }
    //                     }
    //                 }
    //             }
    //         }

    //         // Collect all booked time slots
    //         $bookedTimeSlots = [];
    //         $jobsForDay = [];

    //         // Collect all booked time slots from orders
    //         foreach ($existingOrders as $order) {
    //             $orderStartTime = strtotime($order->start_time);
    //             $orderEndTime = strtotime($order->end_time);
    //             $bookedTimeSlots[] = [
    //                 'start' => $orderStartTime,
    //                 'end' => $orderEndTime,
    //                 'type' => 'order',
    //                 'id' => $order->id,
    //             ];

    //             // Add order to jobs for this day
    //             $jobsForDay[] = [
    //                 'id' => $order->id,
    //                 'type' => 'order',
    //                 'start_time' => $order->start_time,
    //                 'end_time' => $order->end_time,
    //                 'status' => $order->status,
    //             ];
    //         }

    //         // Collect all booked time slots from job applications
    //         foreach ($existingJobApplications as $jobApp) {
    //             $scheduleData = $jobApp->schedule;

    //             if (is_string($scheduleData) && !empty($scheduleData)) {
    //                 $decoded = @json_decode($scheduleData, true);
    //                 $scheduleData = (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) ? $decoded : [];
    //             }

    //             if (is_array($scheduleData)) {
    //                 foreach ($scheduleData as $item) {
    //                     if (isset($item['date']) && $item['date'] == $date
    //                         && isset($item['start_time']) && isset($item['end_time'])) {
    //                         $appStartTime = strtotime($item['start_time']);
    //                         $appEndTime = strtotime($item['end_time']);
    //                         $bookedTimeSlots[] = [
    //                             'start' => $appStartTime,
    //                             'end' => $appEndTime,
    //                             'type' => 'job',
    //                             'id' => $jobApp->id,
    //                         ];

    //                         // Add job application to jobs for this day
    //                         $jobsForDay[] = [
    //                             'id' => $jobApp->id,
    //                             'type' => 'job',
    //                             'start_time' => $item['start_time'],
    //                             'end_time' => $item['end_time'],
    //                             'status' => $jobApp->status,
    //                         ];
    //                     }
    //                 }
    //             }
    //         }

    //         // Add current job slots to jobs for this day if any
    //         foreach ($currentJobSlots as $currentSlot) {
    //             $jobsForDay[] = [
    //                 'id' => $jobId,
    //                 'type' => 'current_job',
    //                 'start_time' => $currentSlot['start_time'],
    //                 'end_time' => $currentSlot['end_time'],
    //                 'is_current_job' => true,
    //             ];
    //         }

    //         // Calculate available time slots
    //         $availableSlots = [];

    //         // Generate available time slots for each provider schedule
    //         foreach ($providerSchedules as $schedule) {
    //             // Convert work time to timestamps
    //             $startTime = strtotime($providerWorkTime->start_at);
    //             $endTime = strtotime($providerWorkTime->end_at);

    //             // تخزين الساعات المستخدمة لمنع التكرار (Store used hours to prevent duplication)
    //             $usedHours = [];

    //             // Create slots with the specified duration
    //             for ($time = $startTime; $time < $endTime; $time += $duration * 3600) {
    //                 $slotStart = $time;
    //                 $slotEnd = $time + $duration * 3600;

    //                 // Skip if end time exceeds provider's work end time
    //                 if ($slotEnd > $endTime) {
    //                     continue;
    //                 }

    //                 $currentHour = date('H', $slotStart);
    //                 $isDuplicateHour = in_array($currentHour, $usedHours);

    //                 // Check if this slot conflicts with any booked slots
    //                 $isAvailable = true;
    //                 $isCurrentJobSlot = false;
    //                 $conflictInfo = null;

    //                 foreach ($bookedTimeSlots as $bookedSlot) {
    //                     if ($slotStart < $bookedSlot['end'] && $slotEnd > $bookedSlot['start']) {
    //                         $isAvailable = false;
    //                         $conflictInfo = [
    //                             'type' => $bookedSlot['type'],
    //                             'id' => $bookedSlot['id'],
    //                         ];
    //                         break;
    //                     }
    //                 }

    //                 // Check if this slot is from the current job application
    //                 foreach ($currentJobSlots as $currentSlot) {
    //                     $currentSlotStart = strtotime($currentSlot['start_time']);
    //                     $currentSlotEnd = strtotime($currentSlot['end_time']);

    //                     if ($slotStart == $currentSlotStart && $slotEnd == $currentSlotEnd) {
    //                         $isCurrentJobSlot = true;
    //                         break;
    //                     }
    //                 }

    //                 // Add slot if it's available or from the current job
    //                 if (($isAvailable && !$isDuplicateHour) || $isCurrentJobSlot) {
    //                     $slot = [
    //                         'start_time' => date('H:i', $slotStart),
    //                         'end_time' => date('H:i', $slotEnd),
    //                         'display' => date('H:i', $slotStart).' - '.date('H:i', $slotEnd),
    //                         'value' => date('H:i', $slotStart),
    //                         'is_available' => $isAvailable,
    //                         'is_current_job' => $isCurrentJobSlot,
    //                     ];

    //                     if (!$isAvailable && $conflictInfo) {
    //                         $slot['conflict_type'] = $conflictInfo['type'];
    //                         $slot['conflict_id'] = $conflictInfo['id'];
    //                     }

    //                     $availableSlots[] = $slot;

    //                     // تسجيل الساعة كمستخدمة لمنع تكرارها (Register the hour as used to prevent duplication)
    //                     $usedHours[] = $currentHour;
    //                 }
    //             }
    //         }

    //         // Add day data to work days array
    //         $workDay = [
    //             'date' => $date,
    //             'day_name' => __('message.'.$dayName), // Translate day name
    //             'day_number' => $currentDay->day,
    //             'work_hours' => [
    //                 'start' => $providerWorkTime->start_at,
    //                 'end' => $providerWorkTime->end_at,
    //             ],
    //             'available_slots' => $availableSlots,
    //         ];

    //         $workDays[] = $workDay;

    //         // If there are jobs for this day, add to workDaysWithJobs array
    //         if (!empty($jobsForDay)) {
    //             $workDayWithJobs = $workDay;
    //             $workDayWithJobs['jobs'] = $jobsForDay;
    //             $workDaysWithJobs[] = $workDayWithJobs;
    //         }

    //         // Move to next day
    //         $currentDay->addDay();
    //     }

    //     return response()->json([
    //         'success' => true,
    //         'message' => __('message.available_slots_retrieved'),
    //         'data' => [
    //             'month' => $month,
    //             'year' => $year,
    //             'month_name' => $firstDayOfMonth->translatedFormat('F'),
    //             'work_days' => $workDays,
    //             'work_days_with_jobs' => $workDaysWithJobs,
    //         ],
    //     ]);
    // }

    /**
     * Get job application details by ID.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Get the job application with provider, provider info, and service information
        $jobApplication = JobApplication::with([
            'provider:id,name',
            'provider.providerInfo',
            'service:id,name',
        ])->find($id);

        if (!$jobApplication) {
            return response()->json([
                'success' => false,
                'message' => __('message.job_application_not_found'),
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => __('message.job_application_retrieved'),
            'data' => new JobApplicationSimpleResource($jobApplication),
        ]);
    }

    /**
     * Update or create a task with start and end times.
     * For hourly calculation_type jobs, save data to the task table upon saving time.
     * Allow users to omit end time.
     * If the task record already exists, update only time fields.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateTaskTime(UpdateTaskTimeRequest $request)
    {
        // Get the validated data
        $data = $request->validated();
        $lang = $request->header('lang', 'en');

        // Get the job application
        $jobApplication = JobApplication::find($data['job_application_id']);

        if (!$jobApplication) {
            return response()->json([
                'success' => false,
                'message' => __('message.job_application_not_found', [], $lang),
            ], 404);
        }

        // We now allow both hourly and fixed calculation types

        // Get d_id and execution_date from request (both are now required)
        $dId = $data['d_id'];
        $executionDate = $data['execution_date'];
        $startTime = $data['time']; // Required start time
        $endTime = $data['end_time'] ?? null; // Optional end time

        // All validation is now handled in the Request class

        // Log the final values
        Log::info('Using d_id and execution_date', [
            'job_application_id' => $jobApplication->id,
            'd_id' => $dId,
            'execution_date' => $executionDate,
        ]);

        // Check if a task already exists for this job application, d_id, and execution date
        $task = Task::where('job_application_id', $data['job_application_id'])
            ->where('d_id', $dId)
            ->where('execution_date', $executionDate)
            ->first();

        // If task exists, update only time fields
        if ($task) {
            try {
                // Update start_time (always provided since it's required)
                $task->start_time = Carbon::createFromFormat('g:i A', $startTime)->format('H:i:s');

                // Update end_time if provided, otherwise set to null
                if ($endTime) {
                    $task->end_time = Carbon::createFromFormat('g:i A', $endTime)->format('H:i:s');
                } else {
                    $task->end_time = null;
                }

                // Keep the original execution_date from the request
                $task->execution_date = $executionDate;
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => __('message.invalid_time_format', [], $lang),
                    'error' => $e->getMessage(),
                ], 422);
            }

            // Update status and duration based on available times
            if ($task->start_time && $task->end_time) {
                // Both times available - task is completed
                $startTimeCarbon = Carbon::parse($task->start_time);
                $endTimeCarbon = Carbon::parse($task->end_time);

                // Handle overnight shifts
                if ($endTimeCarbon->lt($startTimeCarbon)) {
                    $endTimeCarbon->addDay();
                }

                $task->duration_minutes = $startTimeCarbon->diffInMinutes($endTimeCarbon);
                $task->status = 'ended';
            } elseif ($task->start_time) {
                // Only start time available - task is started
                $task->status = 'started';
                $task->duration_minutes = null;
            } else {
                // No start time - task is pending
                $task->status = 'pending';
                $task->duration_minutes = null;
            }

            // If this is a fixed calculation type, update the contract data from job_application_contracts table
            if ($jobApplication->calculation_type == 'fixed') {
                // Get the execution date month and year for comparison
                $executionDate = Carbon::parse($task->execution_date);
                $executionMonth = $executionDate->format('m');
                $executionYear = $executionDate->format('Y');
                $executionYearMonth = $executionDate->format('Y-m');

                // Get the latest contract for this job application from job_application_contracts table
                $latestContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                    ->orderBy('month', 'desc')
                    ->first();

                if ($latestContract) {
                    // Get current month and year
                    $currentDate = Carbon::now();
                    $currentYearMonth = $currentDate->format('Y-m');

                    // Get the month and year from the latest contract
                    $contractDate = Carbon::parse($latestContract->month);
                    $contractYearMonth = $contractDate->format('Y-m');

                    // Check if latest contract month is not greater than current month
                    if ($contractYearMonth <= $currentYearMonth) {
                        // Latest contract is valid (not in future) - use it
                        $task->fixed_amount = $latestContract->fixed_amount;
                        $task->provider_fixed_cost = $latestContract->provider_fixed_cost;
                        $task->provider_hourly_cost = $latestContract->provider_hourly_cost;
                    } else {
                        // Latest contract is in future - find appropriate contract
                        // Get contract for current month or latest available before current month
                        $appropriateContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                            ->where('month', '<=', $currentDate->format('Y-m-d'))
                            ->orderBy('month', 'desc')
                            ->first();

                        if ($appropriateContract) {
                            $task->fixed_amount = $appropriateContract->fixed_amount;
                            $task->provider_fixed_cost = $appropriateContract->provider_fixed_cost;
                            $task->provider_hourly_cost = $appropriateContract->provider_hourly_cost;
                        } else {
                            // If no appropriate contract found, use the job application data
                            $task->fixed_amount = $jobApplication->fixed_amount;
                            $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                            $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;
                        }
                    }
                } else {
                    // If no contract exists, use the job application data
                    $task->fixed_amount = $jobApplication->fixed_amount;
                    $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                    $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;
                }
            }

            $task->save();

            // Log the successful update
            Log::info('Task time updated successfully via API', [
                'task_id' => $task->id,
                'job_application_id' => $task->job_application_id,
                'd_id' => $task->d_id,
                'old_execution_date' => $executionDate,
                'new_execution_date' => $task->execution_date,
                'start_time' => $task->start_time,
                'end_time' => $task->end_time,
                'status' => $task->status,
                'provider_id' => $task->provider_id,
            ]);

            return response()->json([
                'success' => true,
                'message' => __('message.time_updated_successfully', [], $lang),
                'data' => new TaskTimeResource($task),
            ]);
        }

        // If task doesn't exist, create a new task
        $task = new Task();
        $task->job_application_id = $data['job_application_id'];
        $task->d_id = $dId;
        $task->execution_date = $executionDate;

        try {
            // Set start_time (always provided since it's required)
            $task->start_time = Carbon::createFromFormat('g:i A', $startTime)->format('H:i:s');

            // Set end_time if provided
            if ($endTime) {
                $task->end_time = Carbon::createFromFormat('g:i A', $endTime)->format('H:i:s');
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('message.invalid_time_format', [], $lang),
                'error' => $e->getMessage(),
            ], 422);
        }
        $task->user_id = $jobApplication->user_id;
        $task->provider_id = $jobApplication->provider_id;

        // Set default status to 'pending' (will be updated later based on start/end times)
        $task->status = 'pending';

        // Copy common fields from job application
        $task->calculation_type = $jobApplication->calculation_type;
        $task->provider_payment_method = $jobApplication->provider_payment_method;
        $task->include_transportation = $jobApplication->include_transportation;
        $task->system_transportation_cost = $jobApplication->system_transportation_cost;
        $task->provider_transportation_cost = $jobApplication->provider_transportation_cost;
        $task->service_id = $jobApplication->service_id;
        $task->location_id = $jobApplication->location_id;

        // Handle different calculation types
        if ($jobApplication->calculation_type == 'hourly') {
            // Copy hourly-specific data from job application (job_applications table)
            $task->fixed_amount = $jobApplication->fixed_amount;
            $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;
            $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
            $task->system_hourly_cost = $jobApplication->system_hourly_cost;
        } else {
            // Fixed calculation type logic - get data from job_application_contracts table
            // Get the latest contract for this job application from job_application_contracts table
            $latestContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                ->orderBy('month', 'desc')
                ->first();

            if ($latestContract) {
                // Get current month and year
                $currentDate = Carbon::now();
                $currentYearMonth = $currentDate->format('Y-m');

                // Get the month and year from the latest contract
                $contractDate = Carbon::parse($latestContract->month);
                $contractYearMonth = $contractDate->format('Y-m');

                // Check if latest contract month is not greater than current month
                if ($contractYearMonth <= $currentYearMonth) {
                    // Latest contract is valid (not in future) - use it
                    $task->fixed_amount = $latestContract->fixed_amount;
                    $task->provider_fixed_cost = $latestContract->provider_fixed_cost;
                    $task->provider_hourly_cost = $latestContract->provider_hourly_cost;
                } else {
                    // Latest contract is in future - find appropriate contract
                    // Get contract for current month or latest available before current month
                    $appropriateContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                        ->where('month', '<=', $currentDate->format('Y-m-d'))
                        ->orderBy('month', 'desc')
                        ->first();

                    if ($appropriateContract) {
                        $task->fixed_amount = $appropriateContract->fixed_amount;
                        $task->provider_fixed_cost = $appropriateContract->provider_fixed_cost;
                        $task->provider_hourly_cost = $appropriateContract->provider_hourly_cost;
                    } else {
                        // If no appropriate contract found, use the job application data
                        $task->fixed_amount = $jobApplication->fixed_amount;
                        $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                        $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;
                    }
                }
            } else {
                // If no contract exists, use the job application data
                $task->fixed_amount = $jobApplication->fixed_amount;
                $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;
            }
        }

        // Copy service_id and location_id from job application
        $task->service_id = $jobApplication->service_id;
        $task->location_id = $jobApplication->location_id;

        // Set status and duration based on available times
        if ($task->start_time && $task->end_time) {
            // Both times available - task is completed
            $startTimeCarbon = Carbon::parse($task->start_time);
            $endTimeCarbon = Carbon::parse($task->end_time);

            // Handle overnight shifts
            if ($endTimeCarbon->lt($startTimeCarbon)) {
                $endTimeCarbon->addDay();
            }

            $task->duration_minutes = $startTimeCarbon->diffInMinutes($endTimeCarbon);
            $task->status = 'ended';
        } elseif ($task->start_time) {
            // Only start time available - task is started
            $task->status = 'started';
            $task->duration_minutes = null;
        } else {
            // No start time - task is pending
            $task->status = 'pending';
            $task->duration_minutes = null;
        }

        $task->save();

        return response()->json([
            'success' => true,
            'message' => __('message.task_added_successfully', [], $lang),
            'data' => new TaskTimeResource($task),
        ]);
    }

    /**
     * Calculate duration in minutes between two times, handling overnight shifts.
     *
     * @param Carbon $startTime
     * @param Carbon $endTime
     *
     * @return int Duration in minutes (always positive)
     */
    private function calculateDurationMinutes($startTime, $endTime)
    {
        // Clone the times to avoid modifying the original objects
        $start = $startTime->copy();
        $end = $endTime->copy();

        // If end time is earlier than start time, assume it's the next day
        if ($end->lt($start)) {
            $end->addDay();
        }

        // Calculate the difference in minutes
        $duration = $start->diffInMinutes($end);

        // Additional safety checks
        if ($duration < 0) {
            // This shouldn't happen with our logic above, but just in case
            Log::warning('Negative duration calculated, using absolute value', [
                'start_time' => $start->format('H:i:s'),
                'end_time' => $end->format('H:i:s'),
                'calculated_duration' => $duration,
            ]);
            $duration = abs($duration);
        }

        // Ensure reasonable duration (max 24 hours = 1440 minutes)
        if ($duration > 1440) {
            Log::warning('Duration exceeds 24 hours, capping at 1440 minutes', [
                'start_time' => $start->format('H:i:s'),
                'end_time' => $end->format('H:i:s'),
                'calculated_duration' => $duration,
            ]);
            $duration = 1440;
        }

        return $duration;
    }
}

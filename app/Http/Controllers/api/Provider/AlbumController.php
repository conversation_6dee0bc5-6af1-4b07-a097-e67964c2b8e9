<?php

namespace App\Http\Controllers\api\Provider;

use App\Http\Controllers\api\ApiController;
use App\Models\Album;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\AlbumResource;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\api\Album\NewAlbumRequest;
use App\Http\Requests\api\Album\UpdateAlbumRequest;

class AlbumController extends ApiController
{
    //
    public function index()
    {
        $provider =  Auth::user();
        $albums = $provider->albums;

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => AlbumResource::collection($albums),
        ], 200);
    }

    //
    public function store(NewAlbumRequest $request)
    {
        $data = $request->validated();
        $providerId = Auth::user()->id;
        $cover = $data['cover'];

        $album = new Album();
        $album->provider_id = $providerId;
        $album->save();

        $extestion = $cover->getClientOriginalExtension();
        $filename = $album->id . '.' . $extestion;
        $path = $cover->storeAs('albums', $filename, 'public');

        $data['cover'] = $path;

        $album->fill($data);
        $album->save();

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => new AlbumResource($album),
        ], 200);
    }


    //
    public function update(UpdateAlbumRequest $request, $id)
    {
        $data = $request->validated();
        $providerId = Auth::user()->id;

        $album =  Album::findOrFail($id);
        if ($request->hasFile('cover') && $request->cover != null) {
            $cover = $data['cover'];

            $existingCover = $album->cover;

            if ($existingCover) {
                // Delete the existing cover file if it exists
                Storage::delete($existingCover);
            }

            $cover = $request->file('cover');
            $extension = $cover->getClientOriginalExtension();
            $filename = $album->id . '.' . $extension;
            $path = $cover->storeAs('albums', $filename, 'public');
            $data['cover'] = $path;
        }
        if ($album->provider_id != $providerId) {
            return $this->sendError(__('response.You can not update this Album'));
        }
        $album->update($data);

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => new AlbumResource($album),
        ], 200);
    }


    //
    public function destroy($id)
    {
        $providerId =  Auth::user()->id;

        $album =  Album::findOrFail($id);
        if ($album->provider_id != $providerId) {
            return $this->sendError(__('response.You can not delete this Album'));
        }
        $album->delete();

        return $this->sendSuccess(__('response.Album deleted successfully'));
    }
}

<?php

namespace App\Http\Controllers\api\Provider;

use App\Http\Controllers\api\ApiController;
use App\Http\Requests\api\Wallet\UploadInvoiceRequest;
use App\Http\Resources\WalletResource;
use App\Models\ProviderInvoice;
use App\Models\Wallet;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class WalletController extends ApiController
{
    public function index()
    {
        $total_balance = Wallet::where('user_id', Auth::user()->id)->where('amount_type', 'debt')->sum('amount') - Wallet::where('user_id', Auth::user()->id)->where('amount_type', 'credit')->sum('amount');
        $deposits = Wallet::where('user_id', Auth::user()->id)->where('process_type', 'deposit')->get();
        $dones = Wallet::where('user_id', Auth::user()->id)->where('process_type', 'total_amount')->get();
        $tips = Wallet::where('user_id', Auth::user()->id)->where('process_type', 'tips')->get();
        $data = [
            'total_balance' => $total_balance,
            'deposits' => [
                'transactions' => WalletResource::collection($deposits),
                'total_amount' => $deposits->sum('amount'),
            ],
            'done' => [
                'transactions' => WalletResource::collection($dones),
                'total_amount' => $dones->sum('amount'),
            ],
            'tips' => [
                'transactions' => WalletResource::collection($tips),
                'total_amount' => $tips->sum('amount'),
            ],
        ];

        return response()->json(['code' => 200, 'success' => true, 'data' => $data]);
    }

    public function store(UploadInvoiceRequest $request)
    {
        $invoice = new ProviderInvoice();
        $invoice->provider_id = Auth::user()->id;
        $invoice->invoice = Storage::disk('public')->putFile('invoices/'.Auth::user()->id.'/'.date('Y-m-d'), $request->file('invoice'));
        $invoice->status = 'pending';
        $invoice->save();

        return $this->sendSuccess(__('response.Invoice Uploaded Successfully'));
    }
}

<?php

namespace App\Http\Controllers\api\Provider;

use App\Models\Offer;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\OfferResource;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\api\ApiController;
use App\Http\Requests\api\Offer\StoreOfferRequest;
use App\Http\Requests\api\Offer\UpdateOfferRequest;

class OfferController extends ApiController
{
    public function index()
    {
        $offers = Offer::where('user_id', Auth::user()->id)
            ->whereDate('end_date', '>', date('Y-m-d'))
            ->get();
        $offers = OfferResource::collection($offers);

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $offers,
        ], 200);
    }

    public function store(StoreOfferRequest $request)
    {
        if ($request->has('is_active') && $request->is_active == 'true') {
            $active_offers = Offer::where('user_id', Auth::user()->id)
                ->where('is_active', 1)
                ->count();
            if ($active_offers == 3) {
                return $this->sendError(__('response.You have only three offers to activate'));
            }
        }
        $data = $request->validated();
        $offer = new Offer;
        $offer->fill($data);
        $offer->image = Storage::disk('public')->putFile('offers', $request->file('image'));
        $offer->user_id = auth()->user()->id;
        $offer->save();

        return $this->sendSuccess(__('response.New Offer Added Successfully'));
    }

    public function update(UpdateOfferRequest $request, $id)
    {
        $data = $request->validated();
        if ($request->has('is_active') && $request->is_active == 'true') {
            $active_offers = Offer::where('user_id', Auth::user()->id)
                ->where('is_active', 1)
                ->where('id', $id)
                ->count();
            if ($active_offers == 3) {
                return $this->sendError(__('response.You have only three offers to activate'));
            }
        }

        $offer = Offer::findorfail($id);
        $offer->update($data);
        if ($request->hasFile('image')) {
            if ($request->hasFile('image') && $offer->image != 'admins/default.png') {
                Storage::disk('public')->delete($offer->image);
            }
            $offer->image = Storage::disk('public')->putFile('offers', $request->file('image'));
        }

        $offer->save();
        return $this->sendSuccess(__('response.Offer Updated Successfully'));
    }

    public function delete($id)
    {
        $offer = Offer::where('user_id', Auth::user()->id)
            ->where('id', $id)->first();
        if (!$offer) {
            return $this->sendError(__('response.Offer Not Exists'));
        }

        $offer->delete();
        return $this->sendSuccess(__('response.Offer Deleted Successfully'));
    }
}

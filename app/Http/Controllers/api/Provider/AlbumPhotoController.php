<?php

namespace App\Http\Controllers\api\Provider;

use App\Http\Controllers\api\ApiController;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\api\Album\NewAlbumPhotoRequest;
use App\Http\Requests\api\Album\UpdateAlbumPhotoRequest;
use App\Http\Resources\AlbumPhotoResource;
use App\Models\Album;
use App\Models\AlbumPhoto;
use Illuminate\Support\Facades\Auth;

class AlbumPhotoController extends ApiController
{
    //
    public function index($id)
    {
        $albumPhoto = AlbumPhoto::where('album_id', $id)->get();
        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => AlbumPhotoResource::collection($albumPhoto),
        ], 200);
    }

    //
    public function store(NewAlbumPhotoRequest $request)
    {
        $data = $request->validated();
        $newlyAddedPhotos = [];

        foreach ($data['photos'] as $index => $photo) {
            $extestion = $photo->getClientOriginalExtension();
            $filename = time() . $index . '.' . $extestion;
            $path = $photo->storeAs('albums/' . $data['album_id'], $filename, 'public');
            $newPhoto = new AlbumPhoto();
            $newPhoto->album_id = $data['album_id'];
            $newPhoto->photo = $path;
            $newPhoto->save();

            $newlyAddedPhotos[] = $newPhoto; // Add the newly added photo to the array
        }

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('response.Data_Stored'),
            'data' => AlbumPhotoResource::collection($newlyAddedPhotos),
        ], 200);
    }


    /*  //
    public function update(UpdateAlbumPhotoRequest $request, $id)
    {
        $data = $request->validated();
        $providerId = Auth::user()->id;

        $photo =  AlbumPhoto::findOrFail($id);
        if ($photo->album->provider_id != $providerId) {
            return response()->json(['code' => 200, 'success' => false, 'message' => \TranslationManager::translate('You can not update this Photo')]);
        }
        $photo->update($data);

        return response()->json(['code' => 200, 'success' => true, 'data' => new AlbumPhotoResource($photo)]);
    } */


    //
    public function destroy($id)
    {
        $providerId = Auth::guard('api')->user()->id;

        $photo =  AlbumPhoto::findOrFail($id);
        if ($photo->album->provider_id != $providerId) {
            return $this->sendError(__('response.You can not delete this photo'));
        }
        $photo->delete();

        return $this->sendSuccess(__('response.Photo deleted successfully'));
    }
}

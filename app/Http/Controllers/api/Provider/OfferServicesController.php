<?php

namespace App\Http\Controllers\api\Provider;

use App\Http\Controllers\api\ApiController;
use App\Models\ProviderService;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OfferServicesController extends ApiController
{
    public function index()
    {
        $services = Service::whereRelation('category', 'id', 5)->get();

        $services->map(function ($service) {
            $subscribeStatus = false;
            $providerServices = ProviderService::where('provider_id', Auth::user()->id)->where('service_id', $service->id)->first();

            if ($providerServices) {
                $subscribeStatus = true;
            }
            // Add the additional key and its value to each item

            $service['subscribe_status'] = $subscribeStatus;
            $service['duration'] = 1;
            $service['tracking'] = 0;

            return $service;
        });
        $services = $services->map(function ($service) {
            return [
                'id' => $service['id'],
                'name' => $service['name'],
                'image' => $service['image'],
                'option_id' => $service['category_id'],
                'subscribe_status' => $service['subscribe_status'],
                'duration' => $service['duration'],
                'tracking' => $service['tracking'],
            ];
        });

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => '',
            'data' => $services,
        ]);
    }

    public function store(Request $request)
    {
        $provider_id = Auth::user()->id;
        $service_ids = $request->input('service_ids', []);

        $provider = User::findOrFail($provider_id);

        ProviderService::where('provider_id', Auth::user()->id)
            ->whereRelation('service', 'category_id', 5)->delete();

        foreach ($service_ids as $service_id) {
            $providerService = new ProviderService();
            $providerService->provider_id = Auth::user()->id;
            $providerService->service_id = $service_id;
            $providerService->save();
        }

        return $this->sendSuccess(__('response.The offers added successfully to your account'));
    }
}

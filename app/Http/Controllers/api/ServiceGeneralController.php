<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
use App\Models\ProviderService;
use App\Http\Controllers\Controller;
use App\Http\Controllers\api\ApiController;
use App\Http\Resources\ServicePriceResource;

class ServiceGeneralController extends ApiController
{
    public function getServicePrice($providerId, $serviceId)
    {
        $service = ProviderService::where('provider_id', $providerId)->where('service_id', $serviceId)->first();
        if (!$service) {
            return $this->sendError(__('response.Provider Do Not Offer This Service'));
        }

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => new ServicePriceResource($service),
        ], 200);
    }
}

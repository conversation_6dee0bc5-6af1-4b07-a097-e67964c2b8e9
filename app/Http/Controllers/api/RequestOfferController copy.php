<?php

namespace App\Http\Controllers\api;

use data;
use App\Models\User;
use App\Models\RequestOffer;
use Illuminate\Http\Request;
use App\Models\PaymentTransaction;
use App\Traits\PaymentIntegration;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\api\RequestOffer\ApprovedRequestOffersRequest;
use App\Http\Requests\api\RequestOffer\SaveBookingDateRequest;
use App\Http\Requests\api\RequestOffer\StoreRquestOfferRequest;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\RequestOfferResource;
use App\Notifications\NewRequestOfferNotification;
use App\Notifications\Order\NewBookingNotification;
use App\Notifications\UnPaidRequestOfferNotification;
use App\Http\Resources\OrderResource;
use App\Models\Order;
use App\Notifications\CancelRequestOfferNotification;

class RequestOfferController extends ApiController
{

    //store new RequestOffer recored
    public function store(StoreRquestOfferRequest $request)
    {
        $data = $request->validated();

        if (Auth::user()->id == $data['provider_id']) {
            $this->sendError(__('response.You can not send offer request to your self'));
        }

        DB::beginTransaction();

        $newRequest = new Order();
        $newRequest->user_id = Auth::user()->id;
        $newRequest->fill($data);
        $newRequest->type = 'service';
        $newRequest->save();

        $provider = User::findOrFail($data['provider_id']);
        //$provider->notify(new NewRequestOfferNotification($newRequest));  //notification system

        DB::commit();

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Offer request sent successfully to provider'),
            'data' => new OrderResource($newRequest),
        ], 200);
    }

    public function getUserPendingOffers()
    {
        $pendingRequest = Order::where('user_id', Auth::user()->id)
            ->whereRelation('service', 'category_id', 5)
            ->where('status', 'pending')
            ->where('booked_at', null)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Offer request retrived successfully'),
            'data' =>  OrderResource::collection($pendingRequest),
        ], 200);
    }

    //get the waiting requested offers
    public function getUserWaitingRequestedOffers()
    {
        $waitingRequest = Order::where('user_id', Auth::user()->id)
            ->whereRelation('service', 'category_id', 5)
            ->where('status', 'pending')
            ->where('booked_at', '!=', null)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Offer request retrived successfully'),
            'data' =>  OrderResource::collection($waitingRequest),
        ], 200);
    }

    //unPaid offer request
    public function getUserUnPaidOffers()
    {
        $pendingRequest = Order::where('user_id', Auth::user()->id)
            ->whereRelation('service', 'category_id', 5)
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Offer request retrived successfully'),
            'data' =>  OrderResource::collection($pendingRequest),
        ], 200);
    }


    /****** provider function ******/

    //pending offer request
    public function getProviderPendingOffers()
    {
        $pendingRequest = Order::where('provider_id', Auth::user()->id)
            ->whereRelation('service', 'category_id', 5)
            ->where('status', 'pending')
            ->where('booked_at', null)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Offer request retrived successfully'),
            'data' =>  OrderResource::collection($pendingRequest),
        ], 200);
    }

    //save booking date
    public function saveBookingDate(SaveBookingDateRequest $request)
    {
        $data = $request->validated();
        $requestedOffer = Order::findOrFail($data['order_id']);
        $requestedOffer->update(['booked_at' => $data['booked_in']]);

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Offer request sent successfully to provider'),
            'data' => new OrderResource($requestedOffer),
        ], 200);
    }


    //get the waiting requested offers
    public function getWaitingRequestedOffers()
    {
        $waitingRequest = Order::where('provider_id', Auth::user()->id)
            ->whereRelation('service', 'category_id', 5)
            ->where('status', 'pending')
            ->where('booked_at', '!=', null)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Offer request retrived successfully'),
            'data' =>  OrderResource::collection($waitingRequest),
        ], 200);
    }

    //get the waiting requested offers
    public function getUnPaidRequestedOffers()
    {
        $waitingRequest = Order::where('provider_id', Auth::user()->id)
            ->whereRelation('service', 'category_id', 5)
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Offer request retrived successfully'),
            'data' =>  OrderResource::collection($waitingRequest),
        ], 200);
    }

    //approve the requested offer
    public function approveOffers(ApprovedRequestOffersRequest $request)
    {
        $data = $request->validated();
        $requestedOffer = Order::findOrFail($data['order_id']);
        if ($requestedOffer->provider_id != Auth::user()->id) {
            $this->sendError(__('response.You can not approve this offer request'));
        }

        DB::beginTransaction();
        $requestedOffer->update($data);
        $requestedOffer->status = 'approved';
        $requestedOffer->deposit_amount = $data['total_payment'];
        $requestedOffer->save();

        $user = User::findOrFail($requestedOffer->user->id);
        //$user->notify(new UnPaidRequestOfferNotification($requestedOffer));   //to do notification
        DB::commit();

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Offer request approved successfully'),
            'data' => new OrderResource($requestedOffer),
        ], 200);
    }


    public function cancelOffers($id)
    {
        $requestedOffer = Order::findOrFail($id);
        if ($requestedOffer->provider_id != Auth::user()->id) {
            $this->sendError(__('response.You can not cancel this offer request'));
        }

        DB::beginTransaction();
        $requestedOffer->status = 'canceled';
        $requestedOffer->save();
        $user = User::findOrFail($requestedOffer->user->id);
        //$user->notify(new CancelRequestOfferNotification($requestedOffer));  //to do notification
        DB::commit();


        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Offer request canceled successfully'),
            'data' => new OrderResource($requestedOffer),
        ], 200);
    }
}

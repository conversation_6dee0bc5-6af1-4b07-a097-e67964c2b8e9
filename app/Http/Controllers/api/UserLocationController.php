<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Http\Resources\LocationResource;
use App\Models\User;
use App\Models\UserLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserLocationController extends Controller
{
    public function index()
    {
        $id = request()->id ?? '';
        try {
            $query = UserLocation::where('user_id', auth()->id());

            if (request()->id) {
                $query = $query->where('id', $id);
            }
            $locations = $query->get();

            return response()->json([
                'status' => true,
                'message' => 'Locations retrieved successfully',
                'data' => LocationResource::collection($locations),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve locations',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $location = new UserLocation();
            $location->user_id = auth()->id();
            $location->name = $request->name;
            $location->latitude = $request->latitude;
            $location->longitude = $request->longitude;
            $location->save();

            return response()->json([
                'status' => true,
                'message' => 'Location saved successfully',
                'data' => new LocationResource($location),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to save location',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get a specific location by ID.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id = null)
    {
        $locationId = $id ?? $request->query('id') ?? $request->input('id');

        if (!$locationId) {
            try {
                $locations = UserLocation::where('user_id', auth()->id())->get();

                return response()->json([
                    'status' => true,
                    'message' => 'All locations retrieved successfully',
                    'data' => LocationResource::collection($locations),
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'status' => false,
                    'message' => 'Failed to retrieve locations',
                    'error' => $e->getMessage(),
                ], 500);
            }
        }

        try {
            $locationExists = UserLocation::where('id', $locationId)->exists();

            if (!$locationExists) {
                return response()->json([
                    'status' => false,
                    'message' => 'Location not found. The location with ID '.$locationId.' does not exist.',
                ], 404);
            }

            $location = UserLocation::where('id', $locationId)
                ->where('user_id', auth()->id())
                ->first();

            if (!$location) {
                return response()->json([
                    'status' => false,
                    'message' => 'You do not have permission to view this location. It belongs to another user.',
                ], 403);
            }

            return response()->json([
                'status' => true,
                'message' => 'Location retrieved successfully',
                'data' => new LocationResource($location),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve location',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function update(Request $request, $id = null)
    {
        // Get ID from query parameter, request body, or URL parameter
        $locationId = $id ?? $request->query('id') ?? $request->input('id');

        if (!$locationId) {
            return response()->json([
                'status' => false,
                'message' => 'Location ID is required for update. You can provide it in three ways:',
                'examples' => [
                    'URL parameter' => 'PUT /api/locations/123',
                    'Query parameter' => 'PUT /api/location?id=123',
                    'Request body' => 'PUT /api/location with {"id": 123} in the body',
                ],
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'latitude' => 'sometimes|required|numeric|between:-90,90',
            'longitude' => 'sometimes|required|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // First check if the location exists at all
            $locationExists = UserLocation::where('id', $locationId)->exists();

            if (!$locationExists) {
                return response()->json([
                    'status' => false,
                    'message' => 'Location not found. The location with ID '.$locationId.' does not exist.',
                ], 404);
            }

            // Then check if it belongs to the authenticated user
            $location = UserLocation::where('id', $locationId)
                ->where('user_id', auth()->id())
                ->first();

            if (!$location) {
                return response()->json([
                    'status' => false,
                    'message' => 'You do not have permission to update this location. It belongs to another user.',
                ], 403);
            }

            if ($request->has('name')) {
                $location->name = $request->name;
            }

            if ($request->has('latitude')) {
                $location->latitude = $request->latitude;
            }

            if ($request->has('longitude')) {
                $location->longitude = $request->longitude;
            }

            $location->save();

            return response()->json([
                'status' => true,
                'message' => 'Location updated successfully',
                'data' => new LocationResource($location),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to update location',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get locations for a specific user.
     *
     * @param int $user_id
     * @return \Illuminate\Http\Response
     */
    public function getUserLocations($user_id)
    {
        try {
            // Check if user exists
            $userExists = User::where('id', $user_id)->exists();

            if (!$userExists) {
                return response()->json([
                    'status' => false,
                    'message' => 'User not found',
                ], 404);
            }

            // Get locations for the user
            $locations = UserLocation::where('user_id', $user_id)->get();

            return response()->json([
                'status' => true,
                'message' => 'User locations retrieved successfully',
                'data' => LocationResource::collection($locations),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve user locations',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(Request $request, $id = null)
    {
        // Get ID from query parameter, request body, or URL parameter
        $locationId = $id ?? $request->query('id') ?? $request->input('id');

        if (!$locationId) {
            return response()->json([
                'status' => false,
                'message' => 'Location ID is required for deletion. You can provide it in three ways:',
                'examples' => [
                    'URL parameter' => 'DELETE /api/locations/123',
                    'Query parameter' => 'DELETE /api/location?id=123',
                    'Request body' => 'DELETE /api/location with {"id": 123} in the body',
                ],
            ], 400);
        }

        try {
            // First check if the location exists at all
            $locationExists = UserLocation::where('id', $locationId)->exists();

            if (!$locationExists) {
                return response()->json([
                    'status' => false,
                    'message' => 'Location not found. The location with ID '.$locationId.' does not exist.',
                ], 404);
            }

            // Then check if it belongs to the authenticated user
            $location = UserLocation::where('id', $locationId)
                ->where('user_id', auth()->id())
                ->first();

            if (!$location) {
                return response()->json([
                    'status' => false,
                    'message' => 'You do not have permission to delete this location. It belongs to another user.',
                ], 403);
            }

            // Store location data before deletion if needed
            $locationData = [
                'id' => $location->id,
                'name' => $location->name,
            ];

            $location->delete();

            return response()->json([
                'status' => true,
                'message' => 'Location deleted successfully',
                'data' => $locationData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete location',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

<?php

namespace App\Http\Controllers\api;

use App\Http\Requests\api\user\EditProfileRequest;
use App\Http\Resources\CityResource;
use App\Http\Resources\UserResource;
use App\Models\ProviderInfo;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class UserController extends ApiController
{
    public function show()
    {
        $user = Auth::user();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => new UserResource($user),
        ], 200);
    }

    public function getUserById($id)
    {
        $user = User::find($id);

        if ($user == null) {
            return $this->sendError(__('response.User Not Found'));
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => new UserResource($user),
        ], 200);
    }

    public function editProfile(EditProfileRequest $request)
    {
        $user = User::find(Auth()->user()->id);

        $data = $request->validated();

        if (isset($data['change_type']) && $data['change_type'] == 1) {
            if (($user->type == 'provider' || $user->type == 'company') && $request->type == 'user') {
                return $this->sendError(__('response.Cannot Change Acount Type To User'));
            } else {
                $user->update(['type' => $request->type]);
            }
        }
        $user->update($request->except([
            'image', 'password', 'provider_name', 'provider_address', 'provider_city_id',
            'provider_district_id', 'provider_phone', 'provider_id_file', 'provider_id_number',
        ]));

        if ($request->has('password')) {
            $user->update(['password' => bcrypt($request->password)]);
        }

        if ($request->hasFile('image')) {
            if ($request->hasFile('image') && $user->image != null && $user->image != 'admins/default.png' && $user->image != '') {
                Storage::disk('public')->delete($user->image);
            }
            $user->update(['image' => Storage::disk('public')->putFile('users', $request->file('image'))]);
        }

        if (($request->has('type') && $request->type != 'user') || $user->type != 'user') {
            if ($request->hasFile('provider_id_file') && $user->providerInfo && $user->providerInfo->id_file) {
                Storage::disk('public')->delete($user->providerInfo->id_file);
            }

            $provider = ProviderInfo::where('user_id', $user->id)->first();
            if ($provider == null) {
                $provider = new ProviderInfo();
                $provider->user_id = $user->id;
            }
            if ($request->has('provider_name')) {
                $provider->name = $request->provider_name;
            }
            if ($request->has('provider_city_id')) {
                $provider->city_id = $request->provider_city_id;
            }
            if ($request->has('provider_phone')) {
                $provider->phone = $request->provider_phone;
            }
            if ($request->hasFile('provider_id_file')) {
                $provider->id_file = Storage::disk('public')->putFile($request->type.'/documents/'.$user->id, $request->file('provider_id_file'));
            }
            if ($request->has('provider_id_number')) {
                $provider->id_number = $request->provider_id_number;
            }

            if ($request->has('skills')) {
                $user->skills()->sync($request->skills);
            }

            if ($request->has('work_zones')) {
                $user->providerWorkZones()->sync($request->work_zones);
            }

            $provider->save();
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Updated'),
            'data' => new UserResource($user),
        ], 200);
    }

    public function getProviderWorkCity($id)
    {
        $provider = ProviderInfo::find($id);
        if ($provider === null) {
            return $this->sendError(__('response.Provider Not Found'));
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => CityResource::collection($provider->user?->providerWorkZones),
        ], 200);
    }

    public function getProviderWorkTime($id)
    {
        $provider = ProviderInfo::find($id);
        if ($provider === null) {
            return $this->sendError(__('response.Provider Not Found'));
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $provider->user?->providerWorkTimes,
        ], 200);
    }

    public function update_fcm_token(Request $request)
    {
        if ($request->fcm_token === null) {
            return $this->sendError(__('response.FCM Is Required '));
        }
        $user = $request->user();
        $user->update(['fcm_token' => $request->fcm_token]);
        $user = new UserResource($user);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Token Updated Successfully'),
            'data' => $user,
        ], 200);
    }
}

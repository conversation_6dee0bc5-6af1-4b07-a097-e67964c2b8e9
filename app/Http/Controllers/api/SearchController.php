<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    /**
     * Search for users
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchUsers(Request $request)
    {
        $query = User::query()
            ->where('type', 'user')
            ->where('is_blocked', 0)
            ->where('is_active', 1);

        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%")
                  ->orWhere('phone', 'like', "%{$searchTerm}%");
            });
        }

        $users = $query->paginate(15);

        return response()->json([
            'success' => true,
            'message' => 'Users retrieved successfully',
            'data' => $users->items(),
            'current_page' => $users->currentPage(),
            'last_page' => $users->lastPage(),
            'per_page' => $users->perPage(),
            'total' => $users->total(),
            'next_page_url' => $users->nextPageUrl(),
            'prev_page_url' => $users->previousPageUrl(),
        ]);
    }

    /**
     * Search for providers
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchProviders(Request $request)
    {
        $query = User::query()
            ->whereIn('type', ['provider', 'company'])
            ->where('is_blocked', 0)
            ->where('is_active', 1)
            ->where('is_verified', 1)
            ->whereHas('providerInfo', function($query) {
                $query->where('is_approved', 1);
            });

        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%")
                  ->orWhere('phone', 'like', "%{$searchTerm}%")
                  ->orWhereHas('providerInfo', function($q) use ($searchTerm) {
                      $q->where('name', 'like', "%{$searchTerm}%")
                        ->orWhere('phone', 'like', "%{$searchTerm}%");
                  });
            });
        }

        $providers = $query->with('providerInfo')->paginate(15);

        return response()->json([
            'success' => true,
            'message' => 'Providers retrieved successfully',
            'data' => $providers->items(),
            'current_page' => $providers->currentPage(),
            'last_page' => $providers->lastPage(),
            'per_page' => $providers->perPage(),
            'total' => $providers->total(),
            'next_page_url' => $providers->nextPageUrl(),
            'prev_page_url' => $providers->previousPageUrl(),
        ]);
    }
}

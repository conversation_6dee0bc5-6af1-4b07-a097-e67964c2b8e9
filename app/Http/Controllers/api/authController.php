<?php

namespace App\Http\Controllers\api;

use App\Http\Resources\authResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class authController extends ApiController
{
    public function Signup(Request $request)
    {
        $existuser = User::where('phone', $request->phone)
            ->whereDate('created_at', '!=', now()->toDateString())
            ->first();

        if (isset($existuser)) {
            if ($existuser->is_active == 0) {
                $existuser->delete();
            }
        }
        $existingUser = User::where('email', $request->email)
            ->where('type', $request->type)
            ->first();

        if ($existingUser) {
            return response()->json(['errors' => ['email' => 'This Email already exists.']]);
        }
        DB::beginTransaction();
        $messages = [
            'name.required' => 'The name field is required.',
            'email.required' => 'The email field is required.',
            'email.unique' => 'This Email Exist Before.',
            'phone.required' => 'The phone field is required.',
            'phone.unique' => 'This phone Exist Before.',
            'password.required' => 'The password field is required.',
        ];
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required',
            'email' => 'required|email',
            'phone_code' => 'required',
            'phone' => [
                'required',
                'max:15',
                Rule::unique('users', 'phone')->where('type', $request->type),
            ],
            'password' => 'required',
        ], $messages);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }
        $request['name'] = $request->name;
        $request['email'] = $request->email;
        $request['phone_code'] = $request->phone_code;
        $request['address'] = $request->address;
        $request['password'] = Hash::make($request['password']);
        $request['is_verified'] = 0;
        $request['is_blocked'] = 0;
        $request['remember_token'] = 'null';
        $request['otp'] = $this->generate_otp();
        $request['type'] = $request->type;
        $request['gender'] = $request->gender;
        $request['birth_date'] = $request->birth_date;
        $user['device_token'] = $request->device_token;
        $user = User::create($request->all());
        $user['access_token'] = $user->createToken('auth_token')->plainTextToken;
        DB::commit();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.register_success'),
            'data' => new authResource($user),
        ], 200);
    }

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_phone' => 'required',
            'password' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()]);
        }
        $user = User::where('email', $request->email_phone)->orWhere('phone', $request->email_phone)->first();
        $status = false;
        if ($user === null) {
            $message = 'Wrong Login Credentials';
        } elseif (!Hash::check($request->password, $user->password)) {
            $message = 'Wrong Password Credentials';
        } elseif ($user->is_verified == 0) {
            $user->otp = $this->generate_otp();
            $user->save();
            $status = true;
            $user = new authResource($user);
            $message = 'Your Account Is Not Verified';
        } elseif ($user->is_blocked) {
            $message = 'Your Account Has Been Blocked';
        } else {
            $user['access_token'] = $user->createToken('auth_token')->plainTextToken;
            $user['device_token'] = $request->device_token;
            $user->save();
            $user = new authResource($user);
            $status = true;
            $message = 'Logged In Successfully';
        }

        return response()->json([
            'success' => $status,
            'code' => $status == true ? 1 : 0,
            'message' => $message,
            'data' => $status == true ? $user : null,
        ], 200);
    }

    public function VerfiyCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'otp' => 'required',
            'email_phone' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['code' => -1, 'errors' => $validator->errors()]);
        }
        if (!filter_var($request->email_phone, FILTER_VALIDATE_EMAIL)) {
            $user = User::where('phone', $request['email_phone'])->where('otp', $request['otp'])->first();
        } else {
            $user = User::where('email', $request['email_phone'])->where('otp', $request['otp'])->first();
        }
        if (is_null($user)) {
            return response()->json(['success' => false, 'code' => 0, 'message' => 'code incorrect'], 404);
        } else {
            $data = ['is_verified' => 1];
            if (!filter_var($request->email_phone, FILTER_VALIDATE_EMAIL)) {
                $active = User::where('phone', $request['email_phone'])->where('otp', $request['otp'])->update($data);
            } else {
                $active = User::where('email', $request['email_phone'])->where('otp', $request['otp'])->update($data);
            }
            if ($active > 0) {
                if (!filter_var($request->email_phone, FILTER_VALIDATE_EMAIL)) {
                    $user = User::where('phone', $request['email_phone'])->where('otp', $request['otp'])->first();
                } else {
                    $user = User::where('email', $request['email_phone'])->where('otp', $request['otp'])->first();
                }
                $user['access_token'] = $user->createToken('auth_token')->plainTextToken;

                return response()->json(['success' => true, 'code' => 1, 'message' => 'success', 'data' => new authResource($user)]);
            } else {
                return response()->json(['success' => false, 'code' => 0, 'message' => 'fail']);
            }
        }
    }

    public function forgetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_phone' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['code' => 0, 'errors' => $validator->errors()]);
        }
        $user = User::where('email', $request->email_phone)->orWhere('phone', $request->email_phone)->first();

        if ($user === null) {
            return response()->json(['success' => false, 'code' => 0, 'message' => 'Wrong Login Credentials']);
        } elseif ($user->is_verified == 0) {
            return response()->json(['success' => false, 'code' => 0, 'message' => 'Your Account Is Not Verified']);
        } elseif ($user->is_blocked) {
            return response()->json(['success' => false, 'code' => 0, 'message' => 'Your Account Has Been Blocked']);
        } else {
            $user['access_token'] = $user->createToken('auth_token')->plainTextToken;
            $user->otp = $this->generate_otp();
            $user->save();
            $user = new authResource($user);

            return response()->json(['success' => true, 'code' => 1, 'message' => 'Forget Password OTP Sent Successfully', 'data' => $user]);
        }
    }

    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_phone' => 'required',
            'password' => 'required|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json(['code' => 0, 'errors' => $validator->errors()]);
        }
        $user = User::where('email', $request->email_phone)->orWhere('phone', $request->email_phone)->first();

        if ($user === null) {
            return response()->json(['success' => false, 'code' => 0, 'message' => 'Wrong Login Credentials']);
        } elseif ($user->is_verified == 0) {
            return response()->json(['success' => false, 'code' => 0, 'message' => 'Your Account Is Not Verified']);
        } elseif ($user->is_blocked) {
            return response()->json(['success' => false, 'code' => 0, 'message' => 'Your Account Has Been Blocked']);
        } else {
            $user['access_token'] = $user->createToken('auth_token')->plainTextToken;
            $request['password'] = Hash::make($request['password']);
            $user->save();
            $user = new authResource($user);

            return response()->json(['success' => true, 'code' => 1, 'message' => 'Password Has been Changed Successfully', 'data' => $user]);
        }
    }

    public function generate_otp()
    {
        $generator = '**********';
        $result = '';
        for ($i = 0; $i < 4; ++$i) {
            $result .= substr($generator, rand() % strlen($generator), 1);
        }
        if (User::where('otp', $result)->count() > 0) {
            $result = $this->generate_otp();
        }

        return $result;
    }
}

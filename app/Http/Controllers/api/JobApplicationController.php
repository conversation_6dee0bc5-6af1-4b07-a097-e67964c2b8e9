<?php

namespace App\Http\Controllers\api;

use App\Http\Resources\JobApplicationResource;
use App\Http\Resources\JobApplicationSimpleResource;
use App\Models\JobApplication;
use Illuminate\Http\Request;

class JobApplicationController extends ApiController
{
    /**
     * Get a list of all job applications.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Get all job applications with provider, provider info, and service information
        $jobApplications = JobApplication::with([
            'user',
            'provider',
            'provider.providerInfo',
            'service',
            'location',
        ])
            ->latest()
            ->get();

        // Check if there are any job applications
        if ($jobApplications->isEmpty()) {
            return response()->json([
                'success' => true,
                'message' => __('message.no_job_applications_found'),
                'data' => [],
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => __('message.job_applications_retrieved'),
            'data' => JobApplicationResource::collection($jobApplications),
        ]);
    }

    /**
     * Get job application details by ID.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Get the job application with provider, provider info, and service information
        $jobApplication = JobApplication::with([
            'provider:id,name',
            'provider.providerInfo',
            'service:id,name',
        ])
            ->find($id);

        if (!$jobApplication) {
            return response()->json([
                'success' => false,
                'message' => __('message.job_application_not_found'),
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => __('message.job_application_retrieved'),
            'data' => new JobApplicationSimpleResource($jobApplication),
        ]);
    }
}

<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\Task;
use Illuminate\Http\Request;

class TaskApiController extends Controller
{
    /**
     * Get all tasks for a specific job application.
     *
     * @param int $jobApplicationId
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTasksByJobApplication(Request $request, $jobApplicationId)
    {
        // Get month and year from request or use current month/year as default
        $selectedMonth = $request->input('month', date('n'));
        $selectedYear = $request->input('year', date('Y'));

        // Get all tasks with the same job_application_id for the selected month and year
        $tasks = Task::where('job_application_id', $jobApplicationId)
            ->whereMonth('execution_date', $selectedMonth)
            ->whereYear('execution_date', $selectedYear)
            ->with(['user:id,name', 'provider:id,name', 'jobApplication.service:id,name'])
            ->orderBy('execution_date')
            ->orderBy('start_time')
            ->get();

        // Format the tasks for the response
        $formattedTasks = $tasks->map(function ($task) {
            return [
                'id' => $task->id,
                'execution_date' => $task->execution_date,
                'start_time' => $task->start_time,
                'end_time' => $task->end_time,
                'duration_minutes' => $task->duration_minutes,
                'status' => $task->status,
                'notes' => $task->notes,
                'calculation_type' => $task->calculation_type,
                'provider_payment_method' => $task->provider_payment_method,
                'fixed_amount' => $task->fixed_amount,
                'provider_fixed_cost' => $task->provider_fixed_cost,
                'provider_hourly_cost' => $task->provider_hourly_cost,
                'provider_transportation_cost' => $task->provider_transportation_cost,
                'system_hourly_cost' => $task->system_hourly_cost,
                'user' => [
                    'id' => $task->user ? $task->user->id : null,
                    'name' => $task->user ? $task->user->name : 'N/A',
                ],
                'provider' => [
                    'id' => $task->provider ? $task->provider->id : null,
                    'name' => $task->provider ? $task->provider->name : 'N/A',
                ],
                'service' => [
                    'id' => $task->jobApplication && $task->jobApplication->service ? $task->jobApplication->service->id : null,
                    'name' => $task->jobApplication && $task->jobApplication->service ? $task->jobApplication->service->name : 'N/A',
                ],
                'job_application_id' => $task->job_application_id,
            ];
        });

        return response()->json([
            'success' => true,
            'job_application_id' => $jobApplicationId,
            'tasks' => $formattedTasks,
        ]);
    }
}

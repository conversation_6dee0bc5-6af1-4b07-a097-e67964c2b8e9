<?php

namespace App\Http\Controllers\api;

use App\Helper\ResponseUtil;
use Illuminate\Support\Facades\Response;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class ApiController extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function sendResponse($result, $message)
    {
        return Response::json(ResponseUtil::makeResponse($message, $result));
    }

    public function sendError($error)
    {
        return Response::json(ResponseUtil::makeError($error));
    }

    public function sendSuccess($message)
    {
        return Response::json([
            'success' =>  true,
            'code' => 1,
            'message' => $message,
        ], 200);
    }
}

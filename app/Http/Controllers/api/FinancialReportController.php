<?php

namespace App\Http\Controllers\api;

use App\Http\Requests\api\FinancialReportRequest;
use App\Http\Resources\FinancialReportResource;
use App\Models\PaymentTransaction;
use App\Models\Task;
use App\Services\PaymentService;
use Illuminate\Support\Facades\Auth;

class FinancialReportController extends ApiController
{
    /**
     * Get financial report for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReport(FinancialReportRequest $request)
    {
        try {
            $user = Auth::user();
            $month = $request->input('month');
            $year = $request->input('year');

            // Check if mobile payment is enabled for this user in this month/year
            $showpayment = false;
            if ($user->type === 'user') {
                // For customers, check their own tasks
                $showpayment = Task::where('user_id', $user->id)
                    ->whereMonth('execution_date', $month)
                    ->whereYear('execution_date', $year)
                    ->where('mobile_payment_enabled', true)
                    ->exists();
            } elseif (in_array($user->type, ['provider', 'company'])) {
                // For providers, check tasks where they are the provider
                $showpayment = Task::where('provider_id', $user->id)
                    ->whereMonth('execution_date', $month)
                    ->whereYear('execution_date', $year)
                    ->where('mobile_payment_enabled', true)
                    ->exists();
            }

            // Build the query based on user type
            $query = Task::with([
                'user',
                'provider',
                'jobApplication.service',
                'jobApplication.location',
            ]);

            // Filter by month and year
            $query->whereMonth('execution_date', $month)
                  ->whereYear('execution_date', $year);

            // Filter based on user type
            if ($user->type === 'user') {
                // Customer: show tasks where they are the customer
                $query->where('user_id', $user->id);
            } elseif (in_array($user->type, ['provider', 'company'])) {
                // Provider: show tasks where they are the provider
                $query->where('provider_id', $user->id);
            } else {
                return $this->sendError(__('message.unauthorized_access'));
            }

            // Order by execution date and start time
            $query->orderBy('execution_date', 'desc')
                  ->orderBy('start_time', 'desc');

            $tasks = $query->get();

            // Group tasks for fixed amount calculation types
            $groupedTasks = $this->groupTasksForDisplay($tasks, $user, $month, $year);

            // Calculate summary statistics
            $totalTasks = $tasks->count();
            $totalHours = 0;
            $totalAmount = 0;
            $totalTransportation = 0;
            $paidTasks = 0;
            $unpaidTasks = 0;
            $fixedAmountTasks = 0;
            $hourlyTasks = 0;
            $processedJobApplications = []; // Track processed job applications for amounts
            $processedTransportation = []; // Track processed transportation
            $paidJobApplications = []; // Track job applications that have been paid (to avoid recalculating fixed amounts and transportation)
            $previousPayments = []; // Track previous payments

            foreach ($tasks as $task) {
                if ($task->duration_minutes > 0) {
                    $totalHours += round($task->duration_minutes / 60, 2);
                }

                // Count paid/unpaid tasks
                $isTaskPaid = $user->type === 'user' ?
                    $task->is_financially_closed_by_user :
                    $task->is_financially_closed_by_provider;

                if ($isTaskPaid) {
                    ++$paidTasks;

                    // Mark this job application as paid to avoid recalculating fixed amounts and transportation
                    $paidJobApplications[] = $task->job_application_id;

                    // Collect payment information for paid tasks (once per job application)
                    $paymentKey = $task->job_application_id.'_'.($user->type === 'user' ? 'customer' : 'provider');
                    if (!isset($previousPayments[$paymentKey])) {
                        $paymentInfo = [
                            'job_application_id' => $task->job_application_id,
                            'payment_date' => $task->updated_at ? $task->updated_at->format('Y-m-d') : null,
                        ];

                        if ($user->type === 'user') {
                            if ($task->calculation_type === 'hourly') {
                                // For hourly: sum all hours for this job application
                                $totalHoursForJob = Task::where('job_application_id', $task->job_application_id)
                                    ->whereMonth('execution_date', $month)
                                    ->whereYear('execution_date', $year)
                                    ->where('is_financially_closed_by_user', true)
                                    ->sum('duration_minutes') / 60;
                                $rate = $task->system_hourly_cost ?? 0;
                                $paymentInfo['amount'] = round($totalHoursForJob * $rate, 2);
                            } else {
                                // For fixed: amount is fixed regardless of tasks count
                                $paymentInfo['amount'] = $task->fixed_amount ?? 0;
                            }
                            $paymentInfo['transportation'] = $task->system_transportation_cost ?? 0;
                        } else {
                            if ($task->provider_payment_method === 'hourly') {
                                // For hourly: sum all hours for this job application
                                $totalHoursForJob = Task::where('job_application_id', $task->job_application_id)
                                    ->whereMonth('execution_date', $month)
                                    ->whereYear('execution_date', $year)
                                    ->where('is_financially_closed_by_provider', true)
                                    ->sum('duration_minutes') / 60;
                                $rate = $task->provider_hourly_cost ?? 0;
                                $paymentInfo['amount'] = round($totalHoursForJob * $rate, 2);
                            } else {
                                // For fixed: amount is fixed regardless of tasks count
                                $paymentInfo['amount'] = $task->provider_fixed_cost ?? 0;
                            }
                            $paymentInfo['transportation'] = $task->provider_transportation_cost ?? 0;
                        }

                        $paymentInfo['total'] = $paymentInfo['amount'] + $paymentInfo['transportation'];
                        $previousPayments[$paymentKey] = $paymentInfo;
                    }
                } else {
                    ++$unpaidTasks;
                }

                // Count tasks by calculation type based on user type
                if ($user->type === 'user') {
                    // For customers: count based on customer calculation type
                    if ($task->calculation_type === 'fixed') {
                        ++$fixedAmountTasks;
                    } else {
                        ++$hourlyTasks;
                    }
                } else {
                    // For providers: count based on provider calculation type
                    if ($task->provider_payment_method === 'fixed') {
                        ++$fixedAmountTasks;
                    } else {
                        ++$hourlyTasks;
                    }
                }

                // Calculate amounts for unpaid tasks only
                // Skip calculation if this job application has been paid
                $jobAppKey = $task->job_application_id.'_'.($user->type === 'user' ? 'customer' : 'provider');
                $isJobAppPaid = in_array($task->job_application_id, $paidJobApplications);

                if (!$isTaskPaid && !$isJobAppPaid) {
                    // Check if task is cancelled
                    $isCancelled = $task->status === 'cancelled';

                    if ($user->type === 'user') {
                        // Customer calculations
                        if ($task->calculation_type === 'hourly') {
                            // Hourly: only count if not cancelled
                            if (!$isCancelled) {
                                $totalAmount += ($task->duration_minutes / 60) * ($task->system_hourly_cost ?? 0);
                            }
                            // Transportation: once per job_application_id
                            if (!in_array($task->job_application_id, $processedTransportation)) {
                                $totalTransportation += $task->system_transportation_cost ?? 0;
                                $processedTransportation[] = $task->job_application_id;
                            }
                        } else {
                            // Fixed amount: count even if cancelled, once per job_application_id
                            if (!in_array($task->job_application_id, $processedJobApplications)) {
                                $totalAmount += $task->fixed_amount ?? 0;
                                $totalTransportation += $task->system_transportation_cost ?? 0;
                                $processedJobApplications[] = $task->job_application_id;
                            }
                        }
                    } else {
                        // Provider calculations
                        if ($task->provider_payment_method === 'hourly') {
                            // Hourly: only count if not cancelled
                            if (!$isCancelled) {
                                $totalAmount += ($task->duration_minutes / 60) * ($task->provider_hourly_cost ?? 0);
                            }
                            // Transportation: once per job_application_id
                            if (!in_array($task->job_application_id, $processedTransportation)) {
                                $totalTransportation += $task->provider_transportation_cost ?? 0;
                                $processedTransportation[] = $task->job_application_id;
                            }
                        } else {
                            // Fixed amount: count even if cancelled, once per job_application_id
                            if (!in_array($task->job_application_id, $processedJobApplications)) {
                                $totalAmount += $task->provider_fixed_cost ?? 0;
                                $totalTransportation += $task->provider_transportation_cost ?? 0;
                                $processedJobApplications[] = $task->job_application_id;
                            }
                        }
                    }
                }
            }

            // Calculate previous payments totals
            $previousPaymentsList = array_values($previousPayments);
            $totalPreviousAmount = array_sum(array_column($previousPaymentsList, 'total'));

            $summary = [
                'total_tasks' => $totalTasks,
                'paid_tasks' => $paidTasks,
                'unpaid_tasks' => $unpaidTasks,
                'fixed_amount_tasks' => $fixedAmountTasks,
                'hourly_tasks' => $hourlyTasks,
                'fixed_amount_tasks_label' => __('message.fixed_amount_tasks'),
                'hourly_tasks_label' => __('message.hourly_tasks'),
                'total_job_applications' => count($processedJobApplications),
                'total_hours' => round($totalHours, 2),
                'total_amount' => round($totalAmount, 2),
                'total_transportation' => round($totalTransportation, 2),
                'grand_total' => round($totalAmount + $totalTransportation, 2),
                'previous_payments' => $previousPaymentsList,
                'total_previous_payments' => count($previousPaymentsList),
                'total_previous_amount' => round($totalPreviousAmount, 2),
                'month' => $month,
                'year' => $year,
                'user_type' => $user->type,
                'calculation_note' => __('message.transportation_calculation_note'),
                'showpayment' => $showpayment,
            ];
            if ($user->type == 'user') {
                if ($showpayment == true) {
                    foreach ($tasks as $task) {
                        PaymentTransaction::where('task_id', $task->id)->where('transaction_for', 'deposit')->where('confirmed', 0)->delete();
                        $transaction = new PaymentTransaction();
                        $transaction->task_id = $task->id;
                        $transaction->user_id = $task->user?->id;
                        $transaction->transaction_for = 'deposit';
                        $amount = round($totalAmount + $totalTransportation, 2);
                        $transaction->amount = $amount;
                        $transaction->confirmed = 0;
                        $transaction->save();
                    }
                    if ($amount > 0) {
                        $PaymentService = new PaymentService();
                        $paymentLinkData = $PaymentService->createPayment($transaction, 'job');
                        if (
                            array_key_exists('results', $paymentLinkData)
                            && array_key_exists('status', $paymentLinkData['results'])
                            && $paymentLinkData['results']['status'] == 'success'
                        ) {
                            $payment_link = $paymentLinkData['data']['payment_page_link'];

                            // new code for multi payment
                            if ($transaction) {
                                foreach ($tasks as $task) {
                                    $multiTransaction = PaymentTransaction::where('task_id', $task->id)->where('transaction_for', 'deposit')->where('confirmed', 0)->get();
                                    foreach ($multiTransaction as $index => $singleTransaction) {
                                        $singleTransaction->invoice_logid = $paymentLinkData['data']['page_request_uid'];
                                        $singleTransaction->save();
                                    }
                                }
                            }
                            // end new code

                            $transaction->invoice_logid = $paymentLinkData['data']['page_request_uid'];
                            $transaction->save();
                            $summary['payment_link'] = $payment_link;
                        } elseif (array_key_exists('results', $paymentLinkData)
                            && array_key_exists('status', $paymentLinkData['results'])
                            && $paymentLinkData['results']['status'] == 'error'
                        ) {
                            return $this->sendError($paymentLinkData['data'][0]);
                        } else {
                            return $this->sendError(__('response.Error In Payment Transaction'));
                        }
                    }
                }
            }
            $data = [
                'tasks' => FinancialReportResource::collection($groupedTasks),
                'summary' => $summary,
            ];

            return $this->sendResponse($data, __('message.financial_report_retrieved_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('message.something_went_wrong').': '.$e->getMessage());
        }
    }

    /**
     * Group tasks for display - combine fixed amount tasks by job_application_id.
     */
    private function groupTasksForDisplay($tasks, $user, $month, $year)
    {
        $groupedTasks = collect();
        $fixedTaskGroups = [];

        foreach ($tasks as $task) {
            // Determine if current user's calculation type is fixed
            $shouldGroup = false;

            if ($user->type === 'user') {
                // For customers: group if customer calculation type is fixed
                $shouldGroup = ($task->calculation_type === 'fixed');
            } else {
                // For providers: group if provider calculation type is fixed
                $shouldGroup = ($task->provider_payment_method === 'fixed');
            }

            if ($shouldGroup) {
                // Group fixed amount tasks by job_application_id
                $jobAppId = $task->job_application_id;

                if (!isset($fixedTaskGroups[$jobAppId])) {
                    $fixedTaskGroups[$jobAppId] = [
                        'main_task' => $task,
                        'schedules' => [],
                    ];
                }

                // Add schedule info
                $fixedTaskGroups[$jobAppId]['schedules'][] = [
                    'execution_date' => $task->execution_date,
                    'start_time' => $task->start_time,
                    'end_time' => $task->end_time,
                    'duration_minutes' => $task->duration_minutes,
                    'status' => $task->status,
                    'notes' => $task->notes,
                ];
            } else {
                // Hourly tasks are displayed individually
                $groupedTasks->push($task);
            }
        }

        // Add grouped fixed tasks
        foreach ($fixedTaskGroups as $group) {
            $mainTask = $group['main_task'];
            $mainTask->schedules = $group['schedules'];
            $groupedTasks->push($mainTask);
        }

        // Add month and year to each task for hours calculation
        foreach ($groupedTasks as $task) {
            $task->report_month = $month;
            $task->report_year = $year;
        }

        return $groupedTasks;
    }
}

<?php

namespace App\Http\Controllers\api\User;

use App\Http\Controllers\api\ApiController;
use App\Http\Requests\api\Booking\BookingUrgentRequest;
use App\Http\Resources\OrderResource;
use App\Models\Order;
use App\Models\OrderSubCategory;
use App\Models\ProviderService;
use App\Models\Service;
use App\Models\UrgentOrderProvider;
use App\Notifications\Order\NewUrgentBookingNotification;
use Illuminate\Support\Carbon;

class UrgentBookingController extends ApiController
{
    public function requestUrgentBooking(BookingUrgentRequest $request)
    {
        $multiDates = $request->date;
        $multiTimes = $request->time;

        // التحقق من توفر مقدمي الخدمات قبل إنشاء الطلب
        $startDateTime = date('Y-m-d H:i:s', strtotime($multiDates[0].' '.date('H:i:s', strtotime($multiTimes[0]))));
        $endDateTime = date('Y-m-d H:i:s', strtotime($multiDates[0].' '.date('H:i:s', strtotime($multiTimes[0]))));

        $orderTemp = new Order();
        $orderTemp->start_date = $startDateTime;
        $orderTemp->end_date = $endDateTime;

        $providersAvailable = $this->getProviders($orderTemp, $request->service_id, $request->date, $request->time);

        if (!$providersAvailable) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => __('response.No Available Providers Found'),
            ], 400);
        }

        // إنشاء الطلب فقط بعد التحقق من توفر المزودين
        $order = new Order();
        $order->user_id = auth()->user()->id;
        $order->provider_id = null;
        $order->group = null;
        $order->typable_type = Service::class;
        $order->typable_id = $request->service_id;
        $order->city_id = $request->city_id;
        $order->quantity = 0;
        $order->status = 'pending';
        $order->sub_payment = 0;
        $order->total_payment = 0;
        $order->deposit_amount = 0;
        $order->payment_left = 0;
        $order->material_price = 0;
        $order->tax_amount = 0;
        $order->tax_percentage = 0;
        $order->commission_amount = 0;
        $order->commission_percentage = 0;
        $order->discount_amount = 0;
        $order->discount_percentage = 0;
        $order->booked_at = date('Y-m-d H:i:s');
        $order->user_note = ($request->has('note')) ? $request->note : null;
        $order->user_lat = $request->user_lat;
        $order->user_long = $request->user_long;
        $order->address = $request->address;
        $order->is_urgent = true;
        $order->start_date = $startDateTime;
        $order->end_date = $endDateTime;
        $order->save();

        $subCategories = $request->sub_category;
        if ($subCategories && $order->typable->category?->has_subs == 1) {
            foreach ($subCategories as $subIndex => $subCategory) {
                $orderSubs = new OrderSubCategory();
                $orderSubs->order_id = $order->id;
                $orderSubs->sub_category_id = $subCategory;
                $orderSubs->quantity = $request->quantity[0];
                $orderSubs->save();
            }
        }

        // تسجيل المزودين المتاحين في UrgentOrderProvider
        foreach ($providersAvailable as $provider) {
            UrgentOrderProvider::create(['order_id' => $order->id, 'provider_id' => $provider->id]);
            $provider->notify(new NewUrgentBookingNotification($order));
        }

        $order = new OrderResource($order);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Order Sent Successfully To Providers'),
            'data' => $order,
        ], 200);
    }

    /**
     * Undocumented function.
     *
     * @param [type] $order
     * @param [type] $serviceId
     * @param [type] $date
     * @param [type] $time
     *
     * @return void
     */
    public function getProviders($order, $serviceId, $dates, $times)
    {
        // الحصول على جميع مقدمي الخدمات الذين يقدمون هذه الخدمة
        $services = ProviderService::where('service_id', $serviceId)->get();
        if ($services->isEmpty()) {
            return false; // لا يوجد مقدمي خدمات
        }

        $providers = [];
        foreach ($services as $service) {
            $provider = $service->provider;

            foreach ($dates as $index => $date) {
                $startDateTime = Carbon::parse($date.' '.$times[$index]);
                $endDateTime = $startDateTime->copy()->addHours($service->duration); // افترض أن مدة الخدمة بالساعات
                $dayName = strtolower($startDateTime->format('l'));

                // التحقق من الإجازات
                $hasHoliday = $provider->providerHolidays()
                    ->where('starts_at', '<=', $startDateTime)
                    ->where('ends_at', '>=', $endDateTime)
                    ->exists();

                if ($hasHoliday) {
                    continue; // تخطي هذا المزود إذا كان لديه إجازة في هذا الوقت
                }

                // التحقق من أوقات العمل
                $isAvailable = $provider->providerWorkTimes()
                    ->where('day', $dayName)
                    ->where('start_at', '<=', $startDateTime->format('H:i:s'))
                    ->where('end_at', '>=', $endDateTime->format('H:i:s'))
                    ->exists();

                if ($isAvailable) {
                    // التحقق من تضارب المواعيد مع المواعيد الأخرى للمزود
                    $hasConflict = $provider->providerSchedule()
                        ->where(function ($query) use ($startDateTime, $endDateTime) {
                            $query->whereBetween('start_at', [$startDateTime, $endDateTime])
                                ->orWhereBetween('end_at', [$startDateTime, $endDateTime])
                                ->orWhere(function ($subQuery) use ($startDateTime, $endDateTime) {
                                    $subQuery->where('start_at', '<=', $startDateTime)
                                             ->where('end_at', '>=', $endDateTime);
                                });
                        })
                        ->exists();

                    if (!$hasConflict) {
                        $providers[] = $provider;
                        break; // إذا كان المزود متاحًا لهذا التاريخ، لا حاجة للتحقق من التواريخ الأخرى
                    }
                }
            }
        }

        return !empty($providers) ? $providers : false;
    }
}

<?php

namespace App\Http\Controllers\api\User;

use App\Http\Controllers\api\ApiController;
use App\Http\Resources\ProviderResource;
use App\Models\FavoritesProvider;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FavoritesController extends ApiController
{
    // add to Favorites
    public function addToFavorites(Request $request)
    {
        $userId = Auth::user()->id;
        $providerId = $request->provider_id;

        // check if already in favorites
        $isFavorite = FavoritesProvider::where('user_id', $userId)->where('provider_id', $providerId)->first();
        if ($isFavorite) {
            $isFavorite->delete();

            return $this->sendSuccess(__('response.Provider removed from your favorites'));
        } else {
            $newFavorite = new FavoritesProvider();
            $newFavorite->user_id = $userId;
            $newFavorite->provider_id = $providerId;
            $newFavorite->save();

            return $this->sendSuccess(__('response.Provider added to your favorites'));
        }
    }

    // show all favorites
    public function allFavorites()
    {
        // الحصول على المستخدم الحالي
        $user_id = Auth::user()->id;
        $user = User::findorfail($user_id);
        // التأكد من أن المستخدم مصادق عليه
        if (!$user) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => __('response.User_Not_Authenticated'),
            ], 401);
        }

        // الحصول على المفضلات
        $allFavorites = ProviderResource::collection($user->favorites);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $allFavorites,
        ], 200);
    }
}

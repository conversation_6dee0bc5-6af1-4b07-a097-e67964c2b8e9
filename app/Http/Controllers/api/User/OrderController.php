<?php

namespace App\Http\Controllers\api\User;

use App\Http\Controllers\api\ApiController;
use App\Http\Requests\api\Orders\OrderReviewRequest;
use App\Http\Requests\api\Orders\OrderTipRequest;
use App\Http\Resources\OrderResource;
use App\Models\Order;
use App\Models\OrderEstimation;
use App\Models\OrderTip;
use App\Models\PaymentTransaction;
use App\Models\Review;
use App\Models\User;
use App\Notifications\Order\UserAcceptProviderNotification;
use App\Notifications\Order\UserApprovedOrderChangeNotification;
use App\Notifications\Order\UserCancelBookingNotification;
use App\Notifications\Order\UserRejectedOrderChangeNotification;
use App\Notifications\Order\UserRejectProviderNotification;
use App\Services\CalculatePriceService;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OrderController extends ApiController
{
    public function index()
    {
        $orders = Order::where('user_id', Auth::user()->id)->latest()
            ->groupBy(DB::raw('COALESCE(`group`, id)'))
            ->get();
        $orders = OrderResource::collection($orders);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $orders,
        ], 200);
    }

    public function show($id)
    {
        $order = Order::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Order Not Found In Your Orders'));
        }
        $order = new OrderResource($order);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $order,
        ], 200);
    }

    public function cancelOrder($id)
    {
        $order = Order::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Order Not Found In Your Orders'));
        }
        if ($order->status == 'pending' || $order->status == 'approved' || $order->status == 'confirmed') {
            $order->status = 'canceled';
            $order->canceled_by = 'user';
            $order->save();
            $order->provider->notify(new UserCancelBookingNotification($order));
            $order = new OrderResource($order);

            return response()->json([
                'success' => true,
                'code' => 1,
                'message' => __('response.Your Booking Has Been Canceled'),
                'data' => $order,
            ], 200);
        } else {
            return $this->sendError(__('response.This Booking Cannot Be Canceled'));
        }
    }

    public function confirmOrder($id)
    {
        $order = Order::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Order Not Found In Your Orders'));
        }
        $orders = null;
        if ($order->group != null) {
            $orders = Order::where('user_id', Auth::user()->id)
                ->where('group', $order->group)
                ->get();
        }

        if ($order->status == 'approved') {
            if ($orders) {
                foreach ($orders as $singleOrder) {
                    PaymentTransaction::where('order_id', $singleOrder->id)->where('transaction_for', 'deposit')->where('confirmed', 0)->delete();
                    $transaction = new PaymentTransaction();
                    $transaction->order_id = $singleOrder->id;
                    $transaction->user_id = $singleOrder->user?->id;
                    $transaction->transaction_for = 'deposit';
                    $amount = $singleOrder->deposit_amount;
                    $transaction->amount = $amount;
                    $transaction->confirmed = 0;
                    $transaction->save();
                }
            } else {
                PaymentTransaction::where('order_id', $order->id)->where('transaction_for', 'deposit')->where('confirmed', 0)->delete();
                $transaction = new PaymentTransaction();
                $transaction->order_id = $order->id;
                $transaction->user_id = $order->user?->id;
                $transaction->transaction_for = 'deposit';
                $amount = $order->deposit_amount;
                $transaction->amount = $amount;
                $transaction->confirmed = 0;
                $transaction->save();
            }

            if ($amount > 0) {
                $PaymentService = new PaymentService();
                $paymentLinkData = $PaymentService->createPayment($transaction);
                if (
                    array_key_exists('results', $paymentLinkData)
                    && array_key_exists('status', $paymentLinkData['results'])
                    && $paymentLinkData['results']['status'] == 'success'
                ) {
                    $payment_link = $paymentLinkData['data']['payment_page_link'];

                    // new code for multi payment
                    if ($orders) {
                        foreach ($orders as $singleOrder) {
                            $multiTransaction = PaymentTransaction::where('order_id', $singleOrder->id)->where('transaction_for', 'deposit')->where('confirmed', 0)->get();
                            foreach ($multiTransaction as $index => $singleTransaction) {
                                $singleTransaction->invoice_logid = $paymentLinkData['data']['page_request_uid'];
                                $singleTransaction->save();
                            }
                        }
                    }
                    // end new code

                    $transaction->invoice_logid = $paymentLinkData['data']['page_request_uid'];
                    $transaction->save();

                    $order = new OrderResource($order);

                    return response()->json([
                        'success' => true,
                        'code' => 1,
                        'message' => __('response.Data_Retrived'),
                        'data' => ['order_details' => $order, 'payment_link' => $payment_link],
                    ], 200);
                } elseif (
                    array_key_exists('results', $paymentLinkData)
                    && array_key_exists('status', $paymentLinkData['results'])
                    && $paymentLinkData['results']['status'] == 'error'
                ) {
                    return $this->sendError($paymentLinkData['data'][0]);
                } else {
                    return $this->sendError(__('response.Error In Payment Transaction'));
                }
            } else {
                // $transaction->confirmed = 1;
                $transaction->save();
                $order->status = 'confirmed';
                $order->deposit_amount = $transaction->amount;
                $order->save();
                // event(new OrderConfirmed($order));
                $order = new OrderResource($order);

                return response()->json([
                    'success' => true,
                    'code' => 1,
                    'message' => __('response.Data_Retrived'),
                    'data' => ['order_details' => $order, 'payment_link' => null],
                ], 200);
            }
        } else {
            return $this->sendError(__('response.This Booking Is').' '.$order->status);
        }
    }

    // approved updated cloth order
    public function approveOrderChange($id)
    {
        $order = Order::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Order Not Found In Your Orders'));
        }
        if ($order->typable?->category->id != 6) {
            return $this->sendError(__('response.This Order Is Not For Cloth Service'));
        }
        $order->status = 'approved';
        $order->save();

        $order->provider->notify(new UserApprovedOrderChangeNotification($order));   // to do make notify
        $order = new OrderResource($order);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $order,
        ], 200);
    }

    // cancel updated cloth order
    public function cancelOrderChange($id)
    {
        $order = Order::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Order Not Found In Your Orders'));
        }
        if ($order->typable?->category->id != 6) {
            return $this->sendError(__('response.This Order Is Not For Cloth Service'));
        }
        if ($order->status == 'canceled_not_delivered') {
            $order->status = 'canceled';
        } else {
            $order->status = 'canceled_not_delivered';
        }
        $order->save();

        $order->provider->notify(new UserRejectedOrderChangeNotification($order));
        $order = new OrderResource($order);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $order,
        ], 200);
    }

    // get order by status
    public function getOrderBySatus($status)
    {
        $orders = Order::where('user_id', Auth::user()->id)->where('status', $status)->get();
        $orders = OrderResource::collection($orders);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $orders,
        ], 200);
    }

    public function getOrderGroup($group)
    {
        $orders = Order::where('user_id', Auth::user()->id)->where('group', $group)->get();
        $orders = OrderResource::collection($orders);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $orders,
        ], 200);
    }

    public function orderAction()
    {
        $orders = Order::where('user_id', Auth::user()->id)
            ->whereIn('status', ['pending', 'approved', 'waiting_dropdawn', 'waiting_approval'])
            ->get();
        // ->whereDate('start_date', '>=', now())
        $orders = OrderResource::collection($orders);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $orders,
        ], 200);
    }

    public function toDo()
    {
        $orders = Order::where('user_id', Auth::user()->id)
            ->where('status', 'confirmed')
            ->whereDate('start_date', '=', now())->get();
        $orders = OrderResource::collection($orders);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $orders,
        ], 200);
    }

    public function rateOrder(OrderReviewRequest $request, $id)
    {
        $order = Order::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Order Not Found In Your Orders'));
        }
        if ($order->status == 'completed') { // confirmed
            $review = Review::where('order_id', $id)->first();
            if ($review === null) {
                $review = new Review();
                $review->user_id = Auth::user()->id;
                $review->provider_id = $order->provider_id;
                $review->order_id = $id;
            }
            $review->rating = $request->rating;
            $review->note = $request->comment;
            $review->save();

            if (Review::where('provider_id', $order->provider_id)->where('is_active', 1)->count() > 0) {
                $provider_ratings = Review::where('provider_id', $order->provider_id)->where('is_active', 1)->sum('rating') / Review::where('provider_id', $order->provider_id)->where('is_active', 1)->count();
                $provider = User::find($order->provider_id);
                $provider->provider_rating = $provider_ratings;
                $provider->save();
            } else {
                $provider = User::find($order->provider_id);
                $provider->provider_rating = 0;
                $provider->save();
            }

            $order = new OrderResource($order);

            return response()->json([
                'success' => true,
                'code' => 1,
                'message' => __('response.Your Review has Been Published'),
                'data' => $order,
            ], 200);
        } else {
            return $this->sendError(__('response.You Can Not Rate This Order Yet'));
        }
    }

    // public function bookingRestPayment($id)
    // {
    //     $order = Order::where('user_id', Auth::user()->id)->where('id', $id)->first();
    //     if (!$order) {
    //         return $this->sendError(__('response.This Order Not Found In Your Orders'));
    //     }

    //     if ($order->status == 'confirmed' || $order->status == 'in_progress' || $order->status == 'completed' || $order->status == 'delivered') {
    //         $tips = OrderTip::where('order_id', $order->id)->sum('amount');

    //         $transaction = new PaymentTransaction();
    //         $transaction->order_id = $order->id;
    //         $transaction->user_id = $order->user?->id;
    //         $transaction->transaction_for = 'total_amount';
    //         $amount = $order->total_payment + $tips - $order->deposit_amount;
    //         $transaction->amount = $amount;
    //         $transaction->save();

    //         $order->deposit_amount += $amount;
    //         $order->save();

    //         if ($amount > 0) {
    //             $payment_link_data = $this->createPayment($transaction);
    //             if (
    //                 array_key_exists('results', $payment_link_data)
    //                 && array_key_exists('status', $payment_link_data['results'])
    //                 && $payment_link_data['results']['status'] == 'success'
    //             ) {
    //                 $payment_link = $payment_link_data['data']['payment_page_link'];
    //                 $transaction->invoice_logid = $payment_link_data['data']['page_request_uid'];
    //                 $transaction->save();

    //                 $order = new OrderResource($order);

    //                 return response()->json([
    //                     'success' => true,
    //                     'code' => 1,
    //                     'message' => null,
    //                     'data' => ['order_details' => $order, 'payment_link' => $payment_link],
    //                 ], 200);
    //             } elseif (
    //                 array_key_exists('results', $payment_link_data)
    //                 && array_key_exists('status', $payment_link_data['results'])
    //                 && $payment_link_data['results']['status'] == 'error'
    //             ) {
    //                 return $this->sendError(__('response.'.$payment_link_data['data'][0]));
    //             } else {
    //                 return $this->sendError(__('response.Error In Payment Transaction'));
    //             }
    //         } else {
    //             $transaction->confirmed = 1;
    //             $transaction->save();
    //             // event(new OrderTotalPricePaid($order));
    //             $order = new OrderResource($order);

    //             return response()->json([
    //                 'success' => true,
    //                 'code' => 1,
    //                 'message' => null,
    //                 'data' => ['order_details' => $order, 'payment_link' => null],
    //             ], 200);
    //         }
    //     } else {
    //         return $this->sendError(__('response.This Booking Is').' '.$order->status);
    //     }
    // }
    public function bookingRestPayment($id)
    {
        // بدء المعاملة
        DB::beginTransaction();

        try {
            $order = Order::where('user_id', Auth::user()->id)->where('id', $id)->first();
            if (!$order) {
                return $this->sendError(__('response.This Order Not Found In Your Orders'));
            }

            if (in_array($order->status, ['confirmed', 'in_progress', 'completed', 'delivered'])) {
                $tips = OrderTip::where('order_id', $order->id)->sum('amount');

                $transaction = new PaymentTransaction();
                $transaction->order_id = $order->id;
                $transaction->user_id = $order->user?->id;
                $transaction->transaction_for = 'total_amount';
                $amount = $order->total_payment + $tips - $order->deposit_amount;
                $transaction->amount = $amount;
                $transaction->save();

                $order->deposit_amount += $amount;
                $order->save();

                if ($amount > 0) {
                    $PaymentService1 = new PaymentService();
                    $payment_link_data = $PaymentService1->createPayment($transaction);
                    // $payment_link_data = $this->createPayment($transaction);
                    if (
                        array_key_exists('results', $payment_link_data)
                        && array_key_exists('status', $payment_link_data['results'])
                        && $payment_link_data['results']['status'] == 'success'
                    ) {
                        $payment_link1 = $payment_link_data['data']['payment_page_link'];
                        $transaction->invoice_logid = $payment_link_data['data']['page_request_uid'];
                        $transaction->save();

                        // تأكيد المعاملة
                        DB::commit();

                        $order = new OrderResource($order);

                        return response()->json([
                            'success' => true,
                            'code' => 1,
                            'message' => null,
                            'data' => ['order_details' => $order, 'payment_link' => $payment_link1],
                        ], 200);
                    } elseif (
                        array_key_exists('results', $payment_link_data)
                        && array_key_exists('status', $payment_link_data['results'])
                        && $payment_link_data['results']['status'] == 'error'
                    ) {
                        // التراجع عن المعاملة
                        DB::rollBack();

                        return $this->sendError(__('response.'.$payment_link_data['data'][0]));
                    } else {
                        // التراجع عن المعاملة
                        DB::rollBack();

                        return $this->sendError(__('response.Error In Payment Transaction'));
                    }
                } else {
                    $transaction->confirmed = 0;
                    $transaction->save();

                    // تأكيد المعاملة
                    DB::commit();

                    $order = new OrderResource($order);

                    return response()->json([
                        'success' => true,
                        'code' => 1,
                        'message' => null,
                        'data' => ['order_details' => $order, 'payment_link' => null],
                    ], 200);
                }
            } else {
                // التراجع عن المعاملة
                DB::rollBack();

                return $this->sendError(__('response.This Booking Is').' '.$order->status);
            }
        } catch (\Exception $e) {
            // التراجع عن المعاملة في حالة حدوث خطأ
            DB::rollBack();

            return $e; // $this->sendError();
        }
    }

    public function confirmEstimationRequest($id, Request $request)
    {
        $provdier_request = OrderEstimation::find($id);
        if ($provdier_request === null || $provdier_request->status != 'pending') {
            return $this->sendError(__('response.Request Not Found'));
        } else {
            $provdier_request->status = 'accepted';
            $provdier_request->save();
            $order = $provdier_request->order;
            $order->quantity += $provdier_request->quantity;

            if ($order->typable?->category_id == 1) {
                $order->quantity = $order->quantity;
                foreach ($order->schedule as $schedule) {
                    $schedule->end_at = date('Y-m-d H:i:s', strtotime($schedule->end_at) + (3600 * $provdier_request->quantity));
                    $schedule->save();
                }
            }
            $order->save();

            $calculatePric = new CalculatePriceService();
            $response = $calculatePric->updateOrderPricing($order);

            $order->provider->notify(new UserAcceptProviderNotification($order));  // todo
            $order = new OrderResource($order);

            return response()->json([
                'success' => true,
                'code' => 1,
                'message' => __('response.Your Accept Sent To Provider'),
                'data' => $order,
            ], 200);
        }
    }

    public function rejectEstimationrequest($id, Request $request)
    {
        $provdier_request = OrderEstimation::find($id);
        if ($provdier_request === null || $provdier_request->status != 'pending') {
            return $this->sendError(__('response.Request Not Found'));
        } else {
            $provdier_request->status = 'rejected';
            $provdier_request->save();
            $order = $provdier_request->order;

            $order->provider->notify(new UserRejectProviderNotification($order));
            $order = new OrderResource($order);

            return response()->json([
                'success' => true,
                'code' => 1,
                'message' => __('response.Your Rejection Sent To Provider'),
                'data' => $order,
            ], 200);
        }
    }

    public function orderTip($id, OrderTipRequest $request)
    {
        $order = Order::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Order Not Found In Your Orders'));
        }

        if ($order->status == 'confirmed' || $order->status == 'in_progress' || $order->status == 'completed') {
            $tip = new OrderTip();
            $tip->order_id = $order->id;
            $tip->amount = $request->amount;
            $tip->save();

            $order = new OrderResource($order);

            return response()->json([
                'success' => true,
                'code' => 1,
                'message' => __('response.Your Tip Sent To Provider'),
                'data' => $order,
            ], 200);
        } else {
            return $this->sendError(__('response.This Booking Is').' '.$order->status);
        }
    }
}

<?php

namespace App\Http\Controllers\api\User;

use App\Models\Order;
use Illuminate\Http\Request;
use App\Models\PaymentTransaction;


use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\OrderResource;
use App\Http\Resources\BillingResource;
use App\Http\Controllers\api\ApiController;

class BillingController extends ApiController
{
    public function index()
    {
        $billings = PaymentTransaction::where('user_id', Auth::user()->id)->with(['order'])->latest()->get();
        $billings = BillingResource::collection($billings);
        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => $billings,
        ], 200);
    }

    public function requestInvoice($id, Request $request)
    {
        $order = Order::where('user_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }
        if ($order->status == 'completed') {
            $order->invoice_request = 1;
            $order->save();
            $order = new OrderResource($order);
            return response()->json([
                'success' =>  true,
                'code' => 1,
                'message' => __('response.Invoice Request Has Been Sent'),
                'data' => $order,
            ], 200);
        } else {
            return $this->sendError(__('response.This Booking Cannot Be Canceled'));
        }
    }
}

<?php

namespace App\Http\Controllers\api;

use App\Models\Consultation;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class ConsultationController extends ApiController
{
    public function store($type, Request $request)
    {
        if ($type != 'financial' && $type != 'legal') {
            return $this->sendError(__('response.please choose vaild type'));
        }
        $user = Auth::user();
        Consultation::create(['user_id' => $user->id, 'type' => $request->type]);
        return $this->sendSuccess(__('response.Your request sent successfully'));
    }
}

<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Http\Resources\LocationResource;
use App\Models\User;
use App\Models\UserLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LocationController extends Controller
{
    /**
     * Get locations for a specific user.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function getUserLocations($id)
    {
        try {
            Log::info('Fetching locations for user ID: ' . $id);

            // Check if user exists
            $userExists = User::where('id', $id)->exists();
            Log::info('User exists: ' . ($userExists ? 'Yes' : 'No'));

            if (!$userExists) {
                Log::warning('User not found with ID: ' . $id);
                return response()->json([
                    'status' => false,
                    'message' => __('message.user_not_found'),
                ], 404);
            }

            // Get locations for the user
            $locations = UserLocation::where('user_id', $id)->get();
            Log::info('Found ' . $locations->count() . ' locations for user ID: ' . $id);

            // Format the response properly
            return response()->json([
                'status' => true,
                'message' => __('message.user_locations_retrieved_successfully'),
                'data' => $locations->map(function($location) {
                    return [
                        'id' => $location->id,
                        'name' => $location->name,
                        'latitude' => $location->latitude,
                        'longitude' => $location->longitude,
                        'user_id' => $location->user_id,
                        'created_at' => $location->created_at,
                        'updated_at' => $location->updated_at
                    ];
                })
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrieving user locations: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return response()->json([
                'status' => false,
                'message' => __('message.failed_to_retrieve_user_locations'),
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

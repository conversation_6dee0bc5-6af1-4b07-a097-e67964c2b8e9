<?php

namespace App\Http\Controllers\api;

use DateTime;
use App\Models\Offer;

use TranslationManager;

use Illuminate\Http\Request;

use App\Models\ProviderHoliday;
use App\Http\Requests\api\Offer\BookingRequest;
use Illuminate\Support\Facades\Auth;

use App\Http\Resources\OfferResource;
use App\Http\Resources\OrderResource;
use App\Notifications\Order\NewBookingNotification;

use App\Http\Resources\OfferAvailableAppointmentResource;
use App\Models\Configuration;
use App\Models\Order;
use App\Models\ProviderWorkTime;

class OfferController extends ApiController
{
    public function index(Request $request)
    {
        $offers = Offer::where('is_active', 1)
            ->whereDate('start_date', '<=', date('Y-m-d'))
            ->whereDate('end_date', '>=', date('Y-m-d'))
            ->whereHas('active_provider', function ($query) {
                $query->where('is_verified', 1)->where('is_blocked', 0)->whereHas('providerInfo', function ($provider_query) {
                    $provider_query->where('is_approved', 1);
                    $provider_query->where('is_suspended', 0);
                });
            });
        if ($request->has('search') && $request->search != NULL) {
            $offers = $offers->whereAny(['name', 'description'], 'LIKE', '%' . $request->search . '%');
        }
        if ($request->has('address_id') && $request->address_id != NULL) {
            $offers = $offers->where('district_id', $request->address_id);
        }
        if ($request->has('provider_id') && $request->provider_id != NULL) {
            $offers = $offers->where('user_id', $request->provider_id);
        }
        $offers = $offers->get();
        $offers = OfferResource::collection($offers);

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => $offers,
        ], 200);
    }

    public function show($id)
    {
        $offer = Offer::where('is_active', 1)
            ->whereDate('start_date', '<=', date('Y-m-d'))
            ->whereDate('end_date', '>', date('Y-m-d'))
            ->whereHas('active_provider', function ($query) {
                $query->where('is_verified', 1)->where('is_blocked', 0)->whereHas('providerInfo', function ($provider_query) {
                    $provider_query->where('is_approved', 1);
                    $provider_query->where('is_suspended', 0);
                });
            })
            ->find($id);
        if (!$offer) {
            return $this->sendError(__('response.Offer Not Exists'));
        }
        $offer = new OfferResource($offer);
        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => $offer,
        ], 200);
    }

    public function show_appointments($id)
    {
        $offer = Offer::where('is_active', 1)
            ->whereDate('start_date', '<=', date('Y-m-d'))
            ->whereDate('end_date', '>', date('Y-m-d'))
            ->whereHas('active_provider', function ($query) {
                $query->where('is_verified', 1)->where('is_blocked', 0)->whereHas('providerInfo', function ($provider_query) {
                    $provider_query->where('is_approved', 1);
                    $provider_query->where('is_suspended', 0);
                });
            })
            ->find($id);
        if (!$offer) {
            return $this->sendError(__('response.Offer Not Exists'));
        }
        $offer = new OfferResource($offer);

        $appointments = new OfferAvailableAppointmentResource($offer);
        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => [
                'offer' => $offer,
                'appointments' => $appointments
            ],
        ], 200);
    }

    public function book_appointment(BookingRequest $request, $id)
    {
        $offer = Offer::where('is_active', 1)
            ->whereDate('start_date', '<=', date('Y-m-d'))
            ->whereDate('end_date', '>', date('Y-m-d'))
            ->whereHas('active_provider', function ($query) {
                $query->where('is_verified', 1)->where('is_blocked', 0)->whereHas('providerInfo', function ($provider_query) {
                    $provider_query->where('is_approved', 1);
                    $provider_query->where('is_suspended', 0);
                });
            })
            ->find($id);
        if (!$offer) {
            return $this->sendError(__('response.Offer Not Exists'));
        }
        if (Auth::user()->id == $offer->user_id) {
            return $this->sendError(__('response.Cannot Book Your Offer'));
        }
        $offer = new OfferResource($offer);

        $date_fullname = strtolower(date('l', strtotime($request->date)));
        $date = date('Y-m-d', strtotime($request->date));
        $working_time = ProviderWorkTime::where('user_id', $offer->user_id)
            ->where('day', $date_fullname)
            ->first();
        $holiday = ProviderHoliday::where('user_id', $offer->user_id)
            ->where('starts_at', '<=', $request->date)->where('ends_at', '>=', $request->date)
            ->first();

        if ($working_time !== NULL && $holiday === NULL) {
            $booking_times = array();
            $work_starts_at =  strtotime($date . ' ' . $working_time->start_at);
            $work_ends_at = strtotime($date . ' ' . $working_time->end_at);
            for ($x = $work_starts_at; $x < $work_ends_at; $x) {
                $booking_times[] = date('h:i A', $x);
                $x = $x + ($offer->duration * 3600);
            }


            $end_time = date('H:i', strtotime(date('Y-m-d') . ' ' . $request->date) + ($offer->duration * 3600));

            if (in_array($request->time, $booking_times)) {
                $deposit = Configuration::where('key', 'deposit')->first();
                $order = Order::create([
                    'user_id' => Auth::user()->id,
                    'provider_id' => $offer->user_id,
                    'typable_type' => Offer::class,
                    'typable_id' => $offer->id,
                    'city_id' => $offer->city_id,
                    'address' => $request->address,
                    'deposit_amount' => $offer->price * $deposit->value / 100,
                    'total_payment' => $offer->price,
                    'commission_amount' => 0,
                    'commission_percentage' => 0,
                    'tax_amount' => 0,
                    'booked_at' => date('Y-m-d H:i:s'),
                    'success' =>  'pending',
                    'start_date' => $request->date . ' ' . date('H:i', strtotime($request->time)),
                    'end_date' => $request->date . ' ' . date('H:i', strtotime($end_time)),
                ]);

                $order->provider->notify(new NewBookingNotification($order));
                $order_data = new OrderResource($order);
                return response()->json([
                    'success' =>  true,
                    'code' => 1,
                    'message' => __('message.Data_Retrieved'),
                    'data' => $order_data,
                ], 200);
            } else {
                return $this->sendError(__('response.Offer Not Available At Selected Date'));
            }
        } else {
            return $this->sendError(__('response.Offer Not Available At Selected Date'));
        }
    }
}

<?php

namespace App\Http\Controllers\api;

use App\Http\Requests\api\Notification\NotificationReadRequest;
use App\Http\Resources\NotificationResource;
use Illuminate\Http\Request;

class NotificationController extends ApiController
{
    public function index(Request $request)
    {
        $user = $request->user(); // استخدام الطلب لجلب المستخدم المصادق عليه

        if (!$user) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => __('message.User_Not_Authenticated'),
            ], 401);
        }

        $notifications = $user->notifications;
        $notifications = NotificationResource::collection($notifications);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => $notifications,
        ], 200);
    }

    public function readNotification(NotificationReadRequest $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => __('message.User_Not_Authenticated'),
            ], 401);
        }

        $notification = $user->notifications->find($request->notification_id);
        if ($notification) {
            $notification->markAsRead();

            return $this->sendSuccess(__('response.Notification Read Successfully'));
        }

        return $this->sendError(__('message.Notification_Not_Found'), [], 404);
    }
}

<?php

namespace App\Http\Controllers\api;

use App\Models\City;
use App\Models\District;
use App\Models\Language;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\CityResource;
use App\Http\Resources\DistrictResource;
use App\Http\Resources\LanguageResource;
use App\Http\Controllers\api\ApiController;

class SettingController extends ApiController
{
    public function getLanguages()
    {
        $languages = Language::where('is_active', 1)->get();
        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => LanguageResource::collection($languages),
        ], 200);
    }
    public function getDistricts()
    {
        $languages = District::where('active', 1)->get();
        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => DistrictResource::collection($languages),
        ], 200);
    }
    public function getCities()
    {
        $data = City::where('active', 1);
        if (request()->district) {
            $districtId = request()->district;
            $data = $data->where("district_id", $districtId);
        }
        $data = $data->get();
        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => CityResource::collection($data),
        ], 200);
    }
}

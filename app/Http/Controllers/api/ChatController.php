<?php

namespace App\Http\Controllers\api;

use App\Models\Chat;
use App\Models\Order;
use App\Models\ChatMessage;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\ChatResource;
use Illuminate\Support\Facades\Auth;

class ChatController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (!$request->has('order_id')) {
            return $this->sendError(__('response.Please Choose Order'));
        } else {
            $order = Order::find($request->order_id);
            if (!$order) {
                return $this->sendError(__('response.Please Choose Valid Order'));
            } elseif (!Auth::check()) {
                return $this->sendError(__('response.Please Choose One Of Your Order'));
            } elseif ($order->user_id != Auth::user()->id && $order->provider_id != Auth::user()->id) {
                return $this->sendError(__('response.Please Choose One Of Your Orders'));
            }
            $chat = Chat::updateOrCreate([
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'provider_id' => $order->provider_id,
            ], []);
            if ($order->user_id == Auth::user()->id) {
                $chat->user_read_last = 1;
                $chat->save();
            } else {
                $chat->provider_read_last = 1;
                $chat->save();
            }
            $chat = new ChatResource($chat);

            return response()->json([
                'success' =>  true,
                'code' => 1,
                'message' => __('message.Data_Retrieved'),
                'data' => $chat,
            ], 200);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (!$request->has('order_id')) {
            return $this->sendError(__('response.Please Choose Order'));
        } elseif (!$request->has('message') || $request->message == '') {
            return $this->sendError(__('response.Please Enter Your Message'));
        } else {
            $order = Order::find($request->order_id);
            if (!$order) {
                return $this->sendError(__('response.Please Choose Valid Order'));
            } elseif (!Auth::check()) {
                return $this->sendError(__('response.Please Choose One Of Your Order'));
            } elseif ($order->user_id != Auth::user()->id && $order->provider_id != Auth::user()->id) {
                return $this->sendError(__('response.Please Choose One Of Your Order'));
            }
        }

        $chat = Chat::updateOrCreate([
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'provider_id' => $order->provider_id,
        ], []);

        $chat_message = new ChatMessage();
        $chat_message->chat_id = $chat->id;
        $chat_message->sender_id = Auth::user()->id;
        $chat_message->message = $request->message;
        if ($order->user_id == Auth::user()->id) {
            $chat->user_read_last = 1;
            $chat_message->sender_role = 'user';
            $chat->provider_read_last = 0;
            $chat->save();

            ChatMessage::where('chat_id', $chat->id)->where('sender_role', 'provider')->update(['is_read' => 1]);
        } else {
            $chat->provider_read_last = 1;
            $chat_message->sender_role = 'provider';
            $chat->user_read_last = 0;
            $chat->save();

            ChatMessage::where('chat_id', $chat->id)->where('sender_role', 'user')->update(['is_read' => 1]);
        }
        $chat_message->save();

        $chat->last_message_at = date('Y-m-d H:i:s');
        $chat->save();

        $chat = new ChatResource($chat);

        return response()->json([
            'success' =>  true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => $chat,
        ], 200);
    }
}

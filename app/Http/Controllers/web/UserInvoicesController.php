<?php

namespace App\Http\Controllers\web;

use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\web\InvoiceRequest\UpdateInvoiceRequest;

class UserInvoicesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        $users = User::orderBy('name')->get();
        $providers = User::whereIn('type', ['provider', 'company'])->whereHas('providerInfo')->with(['providerInfo'])->orderBy('name')->get();
        $query = Order::query();
        if ($request->has('user') && $request->user != NULL) {
            $query->where('user_id', $request->user);
        }

        if ($request->has('provider') && $request->provider != NULL) {
            $query->where('provider_id', $request->provider);
        }

        $orders = $query->where('status', 'completed')->where('invoice_request', 1)->whereNull('invoice')->latest()->get();

        return view('pages.accountings.users-invoices.index', compact(['users', 'providers', 'orders']));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $order = Order::where('status', 'completed')->where('invoice_request', 1)->whereNull('invoice')->findorfail($id);
        if ($order === NULL) {
            abort('404');
        }
        return view('pages.accountings.users-invoices.edit', compact(['order']));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateInvoiceRequest $request, $id)
    {
        $order = Order::where('status', 'completed')->where('invoice_request', 1)->whereNull('invoice')->findorfail($id);
        if ($order === NULL) {
            abort('404');
        }
        $order->invoice = Storage::disk('public')->putFile('invoices/' . $id, $request->file('image'));
        $order->save();

        //$order->user->notify(new InvoiceUploadedNotification($order));   //ToDo when api is ready

        return redirect()->route('invoices.users.index')->with(['message' => 'successfull']);
    }
}

<?php

namespace App\Http\Controllers\web;

use App\Http\Controllers\Controller;
use App\Models\Task;
use Illuminate\Http\Request;

class TaskDetailsController extends Controller
{
    /**
     * Display the task details for provider report.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        $task = Task::with([
            'provider',
            'user',
            'jobApplication' => function($query) {
                $query->with(['service', 'location']);
            }
        ])->findOrFail($id);

        // Get month and year from request or use current month/year as default
        $selectedMonth = $request->input('month', date('n'));
        $selectedYear = $request->input('year', date('Y'));

        return view('pages.task-details.show', compact('task', 'selectedMonth', 'selectedYear'));
    }

    /**
     * Display the task details for client report.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function showClient(Request $request, $id)
    {
        $task = Task::with([
            'provider',
            'user',
            'jobApplication' => function($query) {
                $query->with(['service', 'location']);
            }
        ])->findOrFail($id);

        // Get month and year from request or use current month/year as default
        $selectedMonth = $request->input('month', date('n'));
        $selectedYear = $request->input('year', date('Y'));

        return view('pages.task-details.show-client', compact('task', 'selectedMonth', 'selectedYear'));
    }
}

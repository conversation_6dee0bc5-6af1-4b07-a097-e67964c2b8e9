<?php

namespace App\Http\Controllers\web;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProviderFinancialReportRequest;
use App\Http\Requests\ClientFinancialReportRequest;
use Illuminate\Http\Request;
use App\Models\Task;
use App\Models\User;
use App\Models\Service;
use App\Models\JobApplication;
use App\Models\UserLocation;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class FinancialReportController extends Controller
{
    /**
     * Display the provider financial report.
     *
     * @param  \App\Http\Requests\ProviderFinancialReportRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function providerReport(ProviderFinancialReportRequest $request)
    {
        // Get only providers who are employees
        $providers = User::whereIn('type', ['provider', 'company'])
            ->whereHas('providerInfo', function($query) {
                $query->where('is_employee', true);
            })
            ->orderBy('name')
            ->get();

        // Generate years from current year to 2040
        $currentYear = date('Y');
        $years = range($currentYear, 2040);

        // Generate months
        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $months[$i] = date('F', mktime(0, 0, 0, $i, 1));
        }

        // Default to current month and year if not specified
        $selectedMonth = $request->input('month', date('n'));
        $selectedYear = $request->input('year', $currentYear);
        $selectedProvider = $request->input('provider_id');

        // Check if tasks are already closed for this provider in this month/year
        $showFinancialCloseButton = false;
        if ($selectedProvider && $selectedMonth && $selectedYear) {
            // Contar las tareas que NO están cerradas financieramente
            $unclosedTasksCount = Task::where('provider_id', $selectedProvider)
                ->whereMonth('execution_date', $selectedMonth)
                ->whereYear('execution_date', $selectedYear)
                ->where(function($query) {
                    $query->where('is_financially_closed_by_provider', 0)
                          ->orWhereNull('financially_closed_by_provider');
                })
                ->count();

            // Mostrar el botón solo si hay tareas sin cerrar
            if ($unclosedTasksCount > 0) {
                $showFinancialCloseButton = true;
            }
        }

        // We don't need services anymore as per client request

        $selectedCalculationType = $request->input('calculation_type', 'all');
        // Service filter removed as per client request

        // Build query with eager loading of all necessary relationships
        $query = Task::query()
            ->with([
                'provider',
                'user',
                'jobApplication' => function($query) {
                    $query->with(['service', 'location']);
                }
            ])
            ->whereYear('execution_date', $selectedYear)
            ->whereMonth('execution_date', $selectedMonth);

        if ($selectedProvider) {
            $query->where('provider_id', $selectedProvider);
        }

        // Filter by calculation type if specified
        if ($selectedCalculationType !== 'all') {
            if ($selectedCalculationType == 'hourly') {
                $query->where('provider_payment_method', 'hourly');
            } else {
                $query->where('provider_payment_method', '!=', 'hourly');
            }
        }

        // Service filter removed as per client request

        // Get tasks
        $allTasks = $query->orderBy('execution_date')->get();

        // Process tasks based on provider_payment_method
        $fixedPaymentTasks = [];
        $hourlyPaymentTasks = [];
        $fixedPaymentTasksGroups = [];

        foreach ($allTasks as $task) {
            if ($task->provider_payment_method == 'hourly') {
                // Keep hourly payment tasks as is
                $hourlyPaymentTasks[] = $task;
            } else {
                // Group fixed payment tasks by job_application_id
                if (!isset($fixedPaymentTasks[$task->job_application_id])) {
                    $fixedPaymentTasks[$task->job_application_id] = $task;

                    // Store all tasks with the same job_application_id for grouping button
                    if (!isset($fixedPaymentTasksGroups[$task->job_application_id])) {
                        $fixedPaymentTasksGroups[$task->job_application_id] = [];
                    }
                }

                // Add all fixed payment tasks to their respective groups
                if (!isset($fixedPaymentTasksGroups[$task->job_application_id])) {
                    $fixedPaymentTasksGroups[$task->job_application_id] = [];
                }
                $fixedPaymentTasksGroups[$task->job_application_id][] = $task;
            }
        }

        // Combine the processed tasks
        $tasks = collect(array_values($fixedPaymentTasks))->merge(collect($hourlyPaymentTasks));

        // Add the groups to the view data
        $fixedPaymentTasksGroups = collect($fixedPaymentTasksGroups);

        // Calculate totals - only for unclosed tasks
        $totalHours = $tasks->filter(function($task) {
            return $task->is_financially_closed_by_provider == 0 || is_null($task->financially_closed_by_provider);
        })->sum(function($task) {
            return $task->duration_minutes / 60;
        });

        $totalAmount = $tasks->filter(function($task) {
            return $task->is_financially_closed_by_provider == 0 || is_null($task->financially_closed_by_provider);
        })->sum(function($task) {
            if ($task->provider_payment_method == 'hourly') {
                // Para tipo de pago por hora, verificar si está cancelada
                if ($task->status == 'cancelled') {
                    return 0; // Si está cancelada, no se cuenta
                }
                return ($task->duration_minutes / 60) * $task->provider_hourly_cost;
            } else {
                // Para tipo de pago fijo, mostrar siempre el monto (incluso si está cancelada)
                return $task->provider_fixed_cost;
            }
        });

        // Calculate monthly transportation cost once per job_application_id - only for jobs that haven't had transportation paid
        $transportationTasks = $tasks
            ->filter(function($task) {
                return $task->is_financially_closed_by_provider == 0 || is_null($task->financially_closed_by_provider);
            })
            ->groupBy(function($task) {
                // Group by job_application_id to ensure each job is counted only once
                return $task->job_application_id;
            })
            ->filter(function($groupedTasks, $jobApplicationId) use ($selectedMonth, $selectedYear) {
                // Check if transportation has already been paid for this job application in this month
                $paidTransportationTask = Task::where('job_application_id', $jobApplicationId)
                    ->whereMonth('execution_date', $selectedMonth)
                    ->whereYear('execution_date', $selectedYear)
                    ->where('is_financially_closed_by_provider', 1)
                    ->first();

                // Only include if transportation hasn't been paid yet
                return !$paidTransportationTask;
            })
            ->map(function($groupedTasks) {
                // Get the first task for this job application
                $task = $groupedTasks->first();

                // Return transportation cost for this job application (once per job_application_id)
                return $task->provider_transportation_cost;
            });

        // Set default value if no transportation tasks found
        $monthlyTransportation = $transportationTasks->isEmpty() ? 0 : $transportationTasks->sum();

        // Add monthly transportation to total amount
        $grandTotal = $totalAmount + $monthlyTransportation;

        // Calculate total hourly rate - show all hourly rates regardless of status
        $totalHourlyRate = $tasks->where('provider_payment_method', 'hourly')
            ->sum('provider_hourly_cost');

        // Create calculation types array for the filter
        $calculationTypes = [
            'all' => __('message.all'),
            'hourly' => __('message.hourly'),
            'fixed' => __('message.fixed_amount')
        ];

        // Get clients for financial close
        $clients = User::where('type', 'user')
            ->orderBy('name')
            ->get();

        return view('pages.financial-reports.provider-report', compact(
            'providers',
            'clients',
            'years',
            'months',
            'selectedMonth',
            'selectedYear',
            'selectedProvider',
            'selectedCalculationType',
            'calculationTypes',
            'tasks',
            'totalHours',
            'totalAmount',
            'monthlyTransportation',
            'grandTotal',
            'totalHourlyRate',
            'showFinancialCloseButton',
            'fixedPaymentTasksGroups'
        ));
    }

    /**
     * Display the client financial report.
     *
     * @param  \App\Http\Requests\ClientFinancialReportRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function clientReport(ClientFinancialReportRequest $request)
    {
        // Get only users with type 'user' (clients)
        $clients = User::where('type', 'user')
            ->orderBy('name')
            ->get();

        // Generate years from current year to 2040
        $currentYear = date('Y');
        $years = range($currentYear, 2040);

        // Generate months
        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $months[$i] = date('F', mktime(0, 0, 0, $i, 1));
        }

        // Default to current month and year if not specified
        $selectedMonth = $request->input('month', date('n'));
        $selectedYear = $request->input('year', $currentYear);
        $selectedClient = $request->input('user_id');

        // Check if tasks are already closed for this client in this month/year
        $showFinancialCloseButton = false;
        if ($selectedClient && $selectedMonth && $selectedYear) {
            // Contar las tareas que NO están cerradas financieramente
            $unclosedTasksCount = Task::where('user_id', $selectedClient)
                ->whereMonth('execution_date', $selectedMonth)
                ->whereYear('execution_date', $selectedYear)
                ->where(function($query) {
                    $query->where('is_financially_closed_by_user', 0)
                          ->orWhereNull('financially_closed_by_user');
                })
                ->count();

            // Mostrar el botón solo si hay tareas sin cerrar
            if ($unclosedTasksCount > 0) {
                $showFinancialCloseButton = true;
            }
        }

        // We don't need services anymore as per client request

        $selectedCalculationType = $request->input('calculation_type', 'all');
        // Service filter removed as per client request

        // Build query with eager loading of all necessary relationships
        $query = Task::query()
            ->with([
                'user',
                'provider',
                'jobApplication' => function($query) {
                    $query->with(['service', 'location']);
                }
            ])
            ->whereYear('execution_date', $selectedYear)
            ->whereMonth('execution_date', $selectedMonth);

        if ($selectedClient) {
            $query->where('user_id', $selectedClient);
        }

        // Filter by calculation type if specified
        if ($selectedCalculationType !== 'all') {
            if ($selectedCalculationType == 'hourly') {
                $query->where('calculation_type', 'hourly');
            } else {
                $query->where('calculation_type', '!=', 'hourly');
            }
        }

        // Service filter removed as per client request

        // Get tasks
        $allTasks = $query->orderBy('execution_date')->get();

        // Process tasks based on calculation_type
        $fixedPaymentTasks = [];
        $hourlyPaymentTasks = [];
        $fixedPaymentTasksGroups = [];

        foreach ($allTasks as $task) {
            if ($task->calculation_type == 'hourly') {
                // Keep hourly payment tasks as is
                $hourlyPaymentTasks[] = $task;
            } else {
                // Group fixed payment tasks by job_application_id
                if (!isset($fixedPaymentTasks[$task->job_application_id])) {
                    $fixedPaymentTasks[$task->job_application_id] = $task;

                    // Store all tasks with the same job_application_id for grouping button
                    if (!isset($fixedPaymentTasksGroups[$task->job_application_id])) {
                        $fixedPaymentTasksGroups[$task->job_application_id] = [];
                    }
                }

                // Add all fixed payment tasks to their respective groups
                if (!isset($fixedPaymentTasksGroups[$task->job_application_id])) {
                    $fixedPaymentTasksGroups[$task->job_application_id] = [];
                }
                $fixedPaymentTasksGroups[$task->job_application_id][] = $task;
            }
        }

        // Combine the processed tasks
        $tasks = collect(array_values($fixedPaymentTasks))->merge(collect($hourlyPaymentTasks));

        // Add the groups to the view data
        $fixedPaymentTasksGroups = collect($fixedPaymentTasksGroups);

        // Calculate totals - only for unclosed tasks
        $totalHours = $tasks->filter(function($task) {
            return $task->is_financially_closed_by_user == 0 || is_null($task->financially_closed_by_user);
        })->sum(function($task) {
            return $task->duration_minutes / 60;
        });

        $totalAmount = $tasks->filter(function($task) {
            return $task->is_financially_closed_by_user == 0 || is_null($task->financially_closed_by_user);
        })->sum(function($task) {
            if ($task->calculation_type == 'hourly') {
                // Para tipo de pago por hora, mostrar siempre el monto
                // For hourly calculation type, check if task is cancelled
                if ($task->status == 'cancelled') {
                    return 0;
                }
                return ($task->duration_minutes / 60) * ($task->system_hourly_cost ?? 0);
            } else {
                // Para tipo de pago fijo, contar siempre (even if cancelled)
                return $task->fixed_amount;
            }
        });

        // Calculate monthly transportation cost once per job_application_id - only for jobs that haven't had transportation paid
        $transportationTasks = $tasks
            ->filter(function($task) {
                return $task->is_financially_closed_by_user == 0 || is_null($task->financially_closed_by_user);
            })
            ->groupBy(function($task) {
                // Group by job_application_id to ensure each job is counted only once
                return $task->job_application_id;
            })
            ->filter(function($groupedTasks, $jobApplicationId) use ($selectedMonth, $selectedYear) {
                // Check if transportation has already been paid for this job application in this month
                $paidTransportationTask = Task::where('job_application_id', $jobApplicationId)
                    ->whereMonth('execution_date', $selectedMonth)
                    ->whereYear('execution_date', $selectedYear)
                    ->where('is_financially_closed_by_user', 1)
                    ->first();

                // Only include if transportation hasn't been paid yet
                return !$paidTransportationTask;
            })
            ->map(function($groupedTasks) {
                // Get the first task for this job application
                $task = $groupedTasks->first();

                // Return transportation cost for this job application
                return $task->system_transportation_cost;
            });

        // Set default value if no transportation tasks found
        $monthlyTransportation = $transportationTasks->isEmpty() ? 0 : $transportationTasks->sum();

        // Add monthly transportation to total amount
        $grandTotal = $totalAmount + $monthlyTransportation;

        // Calculate total hourly rate - show all hourly rates regardless of status
        $totalHourlyRate = $tasks->where('calculation_type', 'hourly')
            ->sum('system_hourly_cost');

        // Create calculation types array for the filter
        $calculationTypes = [
            'all' => __('message.all'),
            'hourly' => __('message.hourly'),
            'fixed' => __('message.fixed_amount')
        ];

        // Get providers for financial close
        $providers = User::whereIn('type', ['provider', 'company'])
            ->whereHas('providerInfo', function($query) {
                $query->where('is_employee', true);
            })
            ->orderBy('name')
            ->get();

        return view('pages.financial-reports.client-report', compact(
            'clients',
            'providers',
            'years',
            'months',
            'selectedMonth',
            'selectedYear',
            'selectedClient',
            'selectedCalculationType',
            'calculationTypes',
            'tasks',
            'totalHours',
            'totalAmount',
            'monthlyTransportation',
            'grandTotal',
            'totalHourlyRate',
            'showFinancialCloseButton',
            'fixedPaymentTasksGroups'
        ));
    }

    /**
     * Update mobile payment status for tasks
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateMobilePaymentStatus(Request $request)
    {
        try {
            $request->validate([
                'user_id' => 'required|exists:users,id',
                'month' => 'required|integer|min:1|max:12',
                'year' => 'required|integer|min:2000|max:2040',
                'mobile_payment_enabled' => 'required|boolean',
            ]);

            $userId = $request->input('user_id');
            $month = $request->input('month');
            $year = $request->input('year');
            $mobilePaymentEnabled = $request->input('mobile_payment_enabled');

            // Update all tasks for this user in the specified month/year
            $updatedCount = Task::where('user_id', $userId)
                ->whereMonth('execution_date', $month)
                ->whereYear('execution_date', $year)
                ->update(['mobile_payment_enabled' => $mobilePaymentEnabled]);

            return response()->json([
                'success' => true,
                'message' => 'Mobile payment status updated successfully',
                'updated_tasks' => $updatedCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating mobile payment status: ' . $e->getMessage()
            ], 500);
        }
    }
}

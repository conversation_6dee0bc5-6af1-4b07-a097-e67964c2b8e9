<?php

namespace App\Http\Controllers\web;

use App\Models\City;
use App\Models\User;
use App\Models\District;
use App\Models\Language;
use Illuminate\Support\Str;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use App\Notifications\AdministrationNotification;

class NotificationController extends Controller
{
    public function index(Request $request)
    {
        $en_language = Language::orderBy('id')->first();
        // $allUsers = User::orderBy('name')->get();
        if ($request->has('users')) {
            $users = User::where('type', 'user')->orderBy('name')->get();
        } else {
            $users = [];
        }
        if ($request->has('providers')) {
            $providers = User::whereIn('type', ['provider', 'company'])->whereHas('providerInfo')->with(['providerInfo'])->orderBy('name')->get();
        } else {
            $providers = [];
        }

        $providers = User::whereIn('type', ['provider', 'company'])->whereHas('providerInfo')->with(['providerInfo'])->orderBy('name')->get();
        $districts = District::get();
        if ($request->has('district')) {
            $cities = City::where('district_id', $request->district)->get();
        } else {
            $cities = [];
        }

        $query = User::query();
        if ($request->has('type') && $request->type != NULL && $request->type != 'all') {
            if ($request->type == 'provider') {
                $query->whereIn('type', ['provider', 'company']);
            } elseif ($request->type == 'user') {
                $query->where('type', 'user');
            }
        }
        if ($request->has('city') && $request->city != NULL) {
            $query->where('city_id', $request->city);
        } else {
            if ($request->has('district') && $request->district != NULL) {
                $query->whereHas('city', function ($cityQuery) use ($request) {
                    $cityQuery->whereHas('district', function ($districtQuery) use ($request) {
                        $districtQuery->where('id', $request->district);
                    });
                });
            }
        }
        $usersTable = $query->get();

        return view('pages.notifications.index', compact(['users', 'providers', 'districts', 'cities', 'usersTable']));
    }


    public function generateUniqueCode()
    {
        $numbers = Str::random(9);
        $char = Str::random(1);
        $code = $numbers . $char;
        while (Notification::where('data->group', $code)->exists()) {
            $numbers = Str::random(9);
            $char = Str::random(1);
            $code = $numbers . $char;
        }

        return $code;
    }
    public function send(Request $request)
    {
        // Validate the request
        $request->validate([
            'title' => 'required',
            'content' => 'required',
        ]);

        // Get request parameters
        $ids = $request->input('ids');
        $title = $request->input('title');
        $content = $request->input('content');
        $tab = $request->input('tab');
        $all_status = $request->input('all_status');
        $userType = $request->input('userType');
        $area = $request->input('area');
        $city = $request->input('city');

        try {
            if ($tab == 'bulk-tab') {
                if ($all_status == 'true') {
                    // Generate a unique code for this notification group
                    $UniqueCode = $this->generateUniqueCode();
                    
                    // Create notification for all users
                    $notification = new AdministrationNotification($request->title, $request->content, 'ToAll', 'ToAll', 'ToAll', 'ToAll', true, $UniqueCode);
                    $response = $notification->toFirebase('');
                    
                    // Process the response
                    if (is_object($response) && method_exists($response, 'getContent')) {
                        $responseContent = $response->getContent();
                        $responseData = json_decode($responseContent, true);
                        
                        // Check if response was successful
                        $isSuccessful = method_exists($response, 'isSuccessful') ? $response->isSuccessful() : (isset($responseData['success']) && $responseData['success']);
                        
                        if ($isSuccessful) {
                            // Generate a message ID for the notification
                            $messageId = isset($responseData['response']['message_id']) ? $responseData['response']['message_id'] : 'notification-'.time();

                            // Prepare notification data
                            $data = [
                                'id' => $messageId,
                                'type' => 'Administration Notice',
                                'sender' => 'Administration',
                                'group' => $UniqueCode,
                                'user_id' => 'ToAll',
                                'user_type' => 'ToAll',
                                'area_id' => 'ToAll',
                                'city_id' => 'ToAll',
                                'title' => $request->title,
                                'body' => $request->content,
                            ];
                            
                            // Create notification record in the database
                            Notification::create([
                                'id' => $messageId,
                                'type' => 'Administration Notice',
                                'notifiable_id' => 'ToAll',
                                'notifiable_type' => 'ToAll',
                                'data' => json_encode($data)
                            ]);
                            
                            return response()->json(['success' => true, 'status' => 'تم إرسال الإشعارات بنجاح']);
                        } else {
                            Log::error('Firebase Notification Error: ' . ($responseContent ?? 'Unknown error'));
                            return response()->json(['success' => false, 'error' => 'فشل في إرسال الإشعارات. يرجى المحاولة مرة أخرى.'], 422);
                        }
                    } else {
                        Log::error('Invalid response from Firebase notification');
                        return response()->json(['success' => false, 'error' => 'استجابة غير صالحة من خدمة الإشعارات'], 422);
                    }
                } else {
                    // Send notifications to filtered users
                    $query = User::query();
                    
                    // Filter by user type
                    if ($userType == 'user') {
                        $query->where('type', 'user');
                        $user_type = 'user';
                    } elseif ($userType == 'provider') {
                        $query->whereIn('type', ['provider', 'company']);
                        $user_type = 'provider';
                    } else {
                        $user_type = 'ToAll';
                    }
                    
                    // Filter by area/city
                    if ($request->has('city') && $request->city != NULL) {
                        $query->where('district_id', $request->city);
                        $area_id = $request->city;
                    } else {
                        $area_id = 'ToAll';
                    }
                    
                    if ($request->has('area') && $request->area != NULL) {
                        $query->where('city_id', $request->area);
                        $city_id = $request->area;
                    } else {
                        $city_id = 'ToAll';
                    }
                    
                    // Generate a unique code for this notification group
                    $UniqueCode = $this->generateUniqueCode();
                    
                    // Get users and send notifications
                    $users = $query->get();
                    
                    if (!$users->isEmpty()) {
                        $successCount = 0;
                        $errorCount = 0;
                        
                        foreach ($users as $user1) {
                            try {
                                $user = User::findorfail($user1->id);
                                $user->notify(new AdministrationNotification($request->title, $request->content, $user_type, $user1->id, $area_id, $city_id, false, $UniqueCode));
                                $successCount++;
                            } catch (\Exception $e) {
                                Log::error('Error sending notification to user '.$user1->id.': '.$e->getMessage());
                                $errorCount++;
                            }
                        }
                        
                        $message = 'تم إرسال الإشعارات بنجاح';
                        if ($errorCount > 0) {
                            $message .= ' مع '.$errorCount.' خطأ';
                        }
                        
                        return response()->json(['success' => true, 'status' => $message]);
                    } else {
                        return response()->json(['success' => false, 'error' => 'لا يوجد أي مستخدم في هذه المنطقة'], 422);
                    }
                }
            } elseif ($tab == 'Customize-tab') {
                // Send notifications to selected users
                $user_type = 0;
                $area_id = 0;
                $city_id = 0;
                
                // Set user type
                if ($userType == 'user') {
                    $user_type = 'user';
                } elseif ($userType == 'provider') {
                    $user_type = 'provider';
                }
                
                // Set area/city
                if ($request->has('city') && $request->city != NULL) {
                    $area_id = $request->city;
                } else if ($request->has('area') && $request->area != NULL) {
                    $city_id = $request->area;
                }

                // Check if user IDs are provided
                if (!empty($request->ids)) {
                    // Generate a unique code for this notification group
                    $UniqueCode = $this->generateUniqueCode();
                    $successCount = 0;
                    $errorCount = 0;
                    
                    foreach ($request->ids as $id) {
                        try {
                            $user = User::findorfail($id);
                            $user->notify(new AdministrationNotification($request->title, $request->content, $user_type, $user->id, $area_id, $city_id, false, $UniqueCode));
                            $successCount++;
                        } catch (\Exception $e) {
                            Log::error('Error sending notification to user '.$id.': '.$e->getMessage());
                            $errorCount++;
                        }
                    }
                    
                    $message = 'تم إرسال الإشعارات بنجاح';
                    if ($errorCount > 0) {
                        $message .= ' مع '.$errorCount.' خطأ';
                    }
                    
                    return response()->json(['success' => true, 'status' => $message]);
                } else {
                    return response()->json(['success' => false, 'error' => 'لم يتم تحديد أي مستخدمين'], 422);
                }
            } else {
                return response()->json(['success' => false, 'error' => 'تب غير معروف'], 422);
            }
        } catch (\Exception $e) {
            Log::error('Notification Error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => 'حدث خطأ أثناء إرسال الإشعارات: ' . $e->getMessage()], 500);
        }
    }
    public function history()
    {
        $notifications = Notification::select(
            'type',
            "data->type as dataType",
            "data->group as group",
            "data->user_id as user_id",
            "data->user_type as user_type",
            "data->area_id as area_id",
            "data->city_id as city_id",
            "data->title as title",
            "data->body as body",
            'created_at'
        )->whereIn('type', ['App\Notifications\AdministrationNotification', 'Administration Notice', 'Administration'])
            ->groupBy('data->group')
            ->orderBy('created_at', 'desc')
            ->get(['data', 'created_at']);
        // dd($notifications);
        return view('pages.notifications.show', compact('notifications'));
    }


    public function details($group)
    {
        $notifications = Notification::where('data->group', $group)->select(
            'type',
            "data->user_id as user_id",
            "data->user_type as user_type",
            "data->area_id as area_id",
            "data->city_id as city_id",
            "data->title as title",
            "data->body as body",
            'created_at'
        )->get();
        return view('pages.notifications.details', compact('notifications'));
    }
}

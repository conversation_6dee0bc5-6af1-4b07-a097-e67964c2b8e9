<?php

namespace App\Http\Controllers\web;

use App\Http\Controllers\Controller;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FinancialCloseController extends Controller
{
    /**
     * Check if provider tasks are already closed.
     *
     * @param  int  $providerId
     * @param  int  $month
     * @param  int  $year
     * @return bool
     */
    public function checkProviderTasksClosed($providerId, $month, $year)
    {
        $closedTasks = Task::where('provider_id', $providerId)
            ->whereMonth('execution_date', $month)
            ->whereYear('execution_date', $year)
            ->where('is_financially_closed_by_provider', 1)
            ->orderBy('financially_closed_by_provider', 'desc')
            ->first();

        if ($closedTasks) {
            // Verificamos si financially_closed_by_provider es un timestamp válido
            if ($closedTasks->financially_closed_by_provider && !is_bool($closedTasks->financially_closed_by_provider)) {
                $paymentDate = date('Y-m-d H:i', strtotime($closedTasks->financially_closed_by_provider));
            } else {
                // Si no es un timestamp válido, usamos la fecha de actualización
                $paymentDate = date('Y-m-d H:i', strtotime($closedTasks->updated_at));
            }

            session()->put('payment_warning', __('message.tasks_already_closed_with_date', ['date' => $paymentDate]));
            session()->put('payment_type', 'provider');
            return response()->json([
                'status' => 'closed',
                'message' => __('message.tasks_already_closed_with_date', ['date' => $paymentDate]),
                'payment_date' => $paymentDate
            ]);
        }

        return response()->json([
            'status' => 'open',
            'message' => 'Tasks are not closed yet'
        ]);
    }

    /**
     * Check if user tasks are already closed.
     *
     * @param  int  $userId
     * @param  int  $month
     * @param  int  $year
     * @return bool
     */
    public function checkUserTasksClosed($userId, $month, $year)
    {
        $closedTasks = Task::where('user_id', $userId)
            ->whereMonth('execution_date', $month)
            ->whereYear('execution_date', $year)
            ->where('is_financially_closed_by_user', 1)
            ->orderBy('financially_closed_by_user', 'desc')
            ->first();

        if ($closedTasks) {
            // Verificamos si financially_closed_by_user es un timestamp válido
            if ($closedTasks->financially_closed_by_user && !is_bool($closedTasks->financially_closed_by_user)) {
                $paymentDate = date('Y-m-d H:i', strtotime($closedTasks->financially_closed_by_user));
            } else {
                // Si no es un timestamp válido, usamos la fecha de actualización
                $paymentDate = date('Y-m-d H:i', strtotime($closedTasks->updated_at));
            }

            session()->put('payment_warning', __('message.tasks_already_closed_with_date', ['date' => $paymentDate]));
            session()->put('payment_type', 'user');
            return response()->json([
                'status' => 'closed',
                'message' => __('message.tasks_already_closed_with_date', ['date' => $paymentDate]),
                'payment_date' => $paymentDate
            ]);
        }

        return response()->json([
            'status' => 'open',
            'message' => 'Tasks are not closed yet'
        ]);
    }
    /**
     * Close financial tasks for provider.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function closeProviderTasks(Request $request)
    {
        // Validate request
        $request->validate([
            'provider_id' => 'required|exists:users,id',
            'month' => 'required|integer|min:1|max:12',
            'year' => 'required|integer|min:2000|max:2100',
            'task_ids' => 'sometimes|array',
            'task_ids.*' => 'exists:tasks,id',
            'job_application_ids' => 'sometimes|array',
            'job_application_ids.*' => 'exists:job_applications,id',
        ]);

        try {
            // Begin transaction
            DB::beginTransaction();

            // Check if there are any tasks already closed
            $closedTasks = Task::where('provider_id', $request->provider_id)
                ->whereMonth('execution_date', $request->month)
                ->whereYear('execution_date', $request->year)
                ->where('is_financially_closed_by_provider', 1)
                ->orderBy('financially_closed_by_provider', 'desc')
                ->first();

            // Get tasks based on selection or all unclosed tasks
            $query = Task::where('provider_id', $request->provider_id)
                ->whereMonth('execution_date', $request->month)
                ->whereYear('execution_date', $request->year)
                ->where('is_financially_closed_by_provider', 0);

            // If specific task IDs are provided, only close those tasks
            if ($request->has('task_ids') && !empty($request->task_ids)) {
                $query->whereIn('id', $request->task_ids);
            }

            // If specific job application IDs are provided, close all tasks for those job applications
            if ($request->has('job_application_ids') && !empty($request->job_application_ids)) {
                $query->whereIn('job_application_id', $request->job_application_ids);
            }

            $tasks = $query->get();

            // Check if there are any tasks to close
            if ($tasks->isEmpty()) {
                if ($closedTasks) {
                    // Verificamos si financially_closed_by_provider es un timestamp válido
                    if ($closedTasks->financially_closed_by_provider && !is_bool($closedTasks->financially_closed_by_provider)) {
                        $paymentDate = date('Y-m-d H:i', strtotime($closedTasks->financially_closed_by_provider));
                    } else {
                        // Si no es un timestamp válido, usamos la fecha de actualización
                        $paymentDate = date('Y-m-d H:i', strtotime($closedTasks->updated_at));
                    }

                    session()->put('payment_warning', __('message.tasks_already_closed_with_date', ['date' => $paymentDate]));
                    session()->put('payment_type', 'provider');
                    return redirect()->back();
                }
                return redirect()->back()->with('warning', __('message.no_tasks_to_close'));
            }

            // Update all tasks for the specified month and year only
            foreach ($tasks as $task) {
                // Double check that the task is for the specified month and year
                if (date('n', strtotime($task->execution_date)) == $request->month &&
                    date('Y', strtotime($task->execution_date)) == $request->year) {
                    $task->is_financially_closed_by_provider = 1;
                    $task->financially_closed_by_provider = now();
                    $task->save();

                    // Verificamos que se haya guardado correctamente
                    if (!$task->financially_closed_by_provider) {
                        \Log::warning('Failed to save financially_closed_by_provider date for task ID: ' . $task->id);
                    }
                }
            }

            // Commit transaction
            DB::commit();

            return redirect()->back()->with('success', __('message.tasks_closed_successfully'));
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();

            return redirect()->back()->with('error', __('message.error_closing_tasks') . ': ' . $e->getMessage());
        }
    }

    /**
     * Close financial tasks for user (client).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function closeUserTasks(Request $request)
    {
        // Validate request
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'month' => 'required|integer|min:1|max:12',
            'year' => 'required|integer|min:2000|max:2100',
            'task_ids' => 'sometimes|array',
            'task_ids.*' => 'exists:tasks,id',
            'job_application_ids' => 'sometimes|array',
            'job_application_ids.*' => 'exists:job_applications,id',
        ]);

        try {
            // Begin transaction
            DB::beginTransaction();

            // Check if there are any tasks already closed
            $closedTasks = Task::where('user_id', $request->user_id)
                ->whereMonth('execution_date', $request->month)
                ->whereYear('execution_date', $request->year)
                ->where('is_financially_closed_by_user', 1)
                ->orderBy('financially_closed_by_user', 'desc')
                ->first();

            // Get tasks based on selection or all unclosed tasks
            $query = Task::where('user_id', $request->user_id)
                ->whereMonth('execution_date', $request->month)
                ->whereYear('execution_date', $request->year)
                ->where('is_financially_closed_by_user', 0);

            // If specific task IDs are provided, only close those tasks
            if ($request->has('task_ids') && !empty($request->task_ids)) {
                $query->whereIn('id', $request->task_ids);
            }

            // If specific job application IDs are provided, close all tasks for those job applications
            if ($request->has('job_application_ids') && !empty($request->job_application_ids)) {
                $query->whereIn('job_application_id', $request->job_application_ids);
            }

            $tasks = $query->get();

            // Check if there are any tasks to close
            if ($tasks->isEmpty()) {
                if ($closedTasks) {
                    // Verificamos si financially_closed_by_user es un timestamp válido
                    if ($closedTasks->financially_closed_by_user && !is_bool($closedTasks->financially_closed_by_user)) {
                        $paymentDate = date('Y-m-d H:i', strtotime($closedTasks->financially_closed_by_user));
                    } else {
                        // Si no es un timestamp válido, usamos la fecha de actualización
                        $paymentDate = date('Y-m-d H:i', strtotime($closedTasks->updated_at));
                    }

                    session()->put('payment_warning', __('message.tasks_already_closed_with_date', ['date' => $paymentDate]));
                    session()->put('payment_type', 'user');
                    return redirect()->back();
                }
                return redirect()->back()->with('warning', __('message.no_tasks_to_close'));
            }

            // Update all tasks for the specified month and year only
            foreach ($tasks as $task) {
                // Double check that the task is for the specified month and year
                if (date('n', strtotime($task->execution_date)) == $request->month &&
                    date('Y', strtotime($task->execution_date)) == $request->year) {
                    $task->is_financially_closed_by_user = 1;
                    $task->financially_closed_by_user = now();
                    $task->save();

                    // Verificamos que se haya guardado correctamente
                    if (!$task->financially_closed_by_user) {
                        \Log::warning('Failed to save financially_closed_by_user date for task ID: ' . $task->id);
                    }
                }
            }

            // Commit transaction
            DB::commit();

            return redirect()->back()->with('success', __('message.tasks_closed_successfully'));
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();

            return redirect()->back()->with('error', __('message.error_closing_tasks') . ': ' . $e->getMessage());
        }
    }
}

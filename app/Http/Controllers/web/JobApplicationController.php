<?php

namespace App\Http\Controllers\web;

use App\Http\Controllers\Controller;
use App\Http\Requests\web\jobs\JobRequest;
use App\Models\Category;
use App\Models\JobApplication;
use App\Models\JobApplicationContracts;
use App\Models\Order;
use App\Models\ProviderHoliday;
use App\Models\ProviderSchedule;
use App\Models\ProviderWorkTime;
use App\Models\Service;
use App\Models\User;
use App\Models\UserLocation;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class JobApplicationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = JobApplication::with(['user', 'service'])
            ->whereHas('user', function ($query) {
                $query->where('type', 'user')
                    ->where('is_blocked', 0);
            });

        // Filter by user/client if user_id is provided
        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        $jobApplications = $query->latest()->paginate(10);

        // Get only active customers (type 'user' and not blocked)
        $users = User::where('type', 'user')
            ->where('is_blocked', 0)
            ->get();

        return view('pages.job-applications.index', compact('jobApplications', 'users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get only active customers (type 'user' and not blocked)
        $users = User::where('type', 'user')->with('locations')
            ->where('is_blocked', 0)
            ->get();

        // Get only active service providers and companies (not blocked, is_active = 1, and is_employee = true)
        $providers = User::whereIn('type', ['provider', 'company'])->with(['providerInfo', 'providerService'])
            ->where('is_blocked', 0)
            ->whereHas('providerInfo', function ($query) {
                $query->where('is_employee', true);
            })->get();

        return view('pages.job-applications.create', compact('users', 'providers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(JobRequest $request)
    {
        // dd($request);ن
        $data = $request->validated();
        $data['is_employee'] = false; // Always set to false as we don't want employees

        // Ensure provider_id is included
        if (!isset($data['provider_id'])) {
            $data['provider_id'] = null;
        }
        $provider_payment_type = $data['provider_payment_method_fixed'];
        // dd($provider_payment_type);
        if(isset($data['calculation_type']) && $data['calculation_type'] == 'fixed'){

        if ($provider_payment_type == 'hourly') {
            $data['provider_payment_method'] = $provider_payment_type;
            $data['provider_hourly_cost'] = $data['provider_hourly_cost_fixed'];
            $data['provider_fixed_cost'] = 0;
        }else{
            $data['provider_payment_method'] = $provider_payment_type;
            $data['provider_hourly_cost'] = 0;
            $data['provider_fixed_cost'] = $data['provider_fixed_cost'];
        }
    }
        if(isset($data['calculation_type']) && $data['calculation_type'] == 'hourly'){
            // dd($data);
            $data['provider_payment_method'] = 'hourly';
            $data['provider_hourly_cost'] = $data['provider_hourly_cost'];
            $data['provider_fixed_cost'] = 0;
        }
        if (isset($data['service_id'])) {
            if ($data['service_id'] === 'hours') {
                // Buscar un servicio de categoría 1 (horas)
                $service = Service::where('category_id', 1)->first();
                if ($service) {
                    $data['service_id'] = $service->id;
                }
            } elseif ($data['service_id'] === 'meters') {
                // Buscar un servicio de categoría 2 (metros)
                $service = Service::where('category_id', 2)->first();
                if ($service) {
                    $data['service_id'] = $service->id;
                }
            }
        }

        // تحويل الجدول الزمني إلى التنسيق المطلوب
        if (isset($data['schedule']) && is_array($data['schedule'])) {
            // تنظيم البيانات حسب اليوم
            $organizedSchedule = [];
            $scheduleByDay = [];

            // تجميع البيانات حسب اليوم
            // تحويل البيانات إلى التنسيق المطلوب
            $iteration = 1; // Initialize a counter variable
            foreach ($data['schedule'] as $item) {
                if (!isset($scheduleByDay[$item['day']])) {
                    $scheduleByDay[$item['day']] = [];
                }

                // إضافة الساعات إلى اليوم المناسب
                $scheduleByDay[$item['day']][] = [
                    'd_id' => $iteration++, // Increment the counter for each iteration
                    'duration' => $item['hours'],
                    'start_time' => $item['start_time'],
                ];
            }

            foreach ($scheduleByDay as $day => $hours) {
                $organizedSchedule[] = [
                    'day' => $day,
                    'hours' => $hours,
                ];
            }

            $data['schedule'] = $organizedSchedule; // json_encode($organizedSchedule);
        }

        $jobApplication = JobApplication::create($data);
        if (isset($jobApplication->calculation_type) && $jobApplication->calculation_type === 'fixed') {
            JobApplicationContracts::create([
                'job_application_id' => $jobApplication->id,
                'fixed_amount' => $jobApplication->fixed_amount ?? 0,
                'provider_calculation_type' => $jobApplication->provider_payment_method,
                'provider_fixed_cost' => $jobApplication->provider_fixed_cost,
                'provider_hourly_cost' => $jobApplication->provider_hourly_cost,
                'month' => now()->format('Y-m-01'),
            ]);
        }
if($jobApplication){
    PaymentTransaction::where('job_id', $jobApplication->id)->where('transaction_for', 'deposit')->where('confirmed', 0)->delete();
    $transaction = new PaymentTransaction();
    $transaction->job_id = $jobApplication->id;
    $transaction->user_id = $jobApplication->user?->id;
    $transaction->transaction_for = 'deposit';
    $amount = $jobApplication->deposit_amount;
    $transaction->amount = $amount;
    $transaction->confirmed = 0;
    $transaction->save();
}

        return redirect()->route('job-applications.index')->with('success', __('message.job_application_created_successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(JobApplication $jobApplication)
    {
        $jobApplication->load(['user', 'service']);

        return view('pages.job-applications.show', compact('jobApplication'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(JobApplication $jobApplication)
    {
        // Eager load service and location relationships
        $jobApplication->load(['service', 'location']);

        // Log the loaded job application for debugging
        \Log::info('Editing job application', [
            'id' => $jobApplication->id,
            'service_id' => $jobApplication->service_id,
            'service' => $jobApplication->service,
            'location_id' => $jobApplication->location_id,
            'location' => $jobApplication->location,
        ]);

        // Get only active customers (type 'user' and not blocked)
        $users = User::where('type', 'user')
            ->where(function ($query) use ($jobApplication) {
                $query->where('is_blocked', 0) // Exclude blocked users
                    ->orWhere('id', $jobApplication->user_id); // Include the current user regardless of status
            })
            ->get();

        // Get only active service providers and companies (not blocked and is_employee = true)
        $providers = User::whereIn('type', ['provider', 'company'])
            ->where(function ($query) use ($jobApplication) {
                $query->where('is_blocked', 0)
                    ->orWhere('id', $jobApplication->provider_id); // Include the current provider regardless of status
            })
            ->whereHas('providerInfo', function ($query) {
                $query->where('is_employee', true);
            })->get();

        $services = Service::whereIn('category_id', [1, 2])->get(); // 1 for hours, 2 for meters
        $categories = Category::all();

        // Get existing schedules for the current user
        $userSchedules = [];

        // Get provider schedules if user is a provider
        $providerSchedules = ProviderSchedule::where('user_id', $jobApplication->user_id)->get();

        // Get orders for the user
        $orders = Order::where('user_id', $jobApplication->user_id)
            ->orWhere('provider_id', $jobApplication->user_id)
            ->with('orderSchedules')
            ->get();

        // Get user locations for the selected customer
        $userLocations = UserLocation::where('user_id', $jobApplication->user_id)->get();

        // If we have a location_id but no location relationship loaded, try to find it
        if ($jobApplication->location_id && !$jobApplication->location) {
            $jobApplication->location = UserLocation::find($jobApplication->location_id);
            \Log::info('Found location by ID', ['location' => $jobApplication->location]);
        }

        // If we have a location_id but no location relationship loaded, try to find it
        if ($jobApplication->location_id && !$jobApplication->location) {
            $jobApplication->location = UserLocation::find($jobApplication->location_id);
            \Log::info('Found location by ID', ['location' => $jobApplication->location]);
        }

        // Format schedules for display
        $userSchedules = $this->formatUserSchedules($providerSchedules, $orders);

        return view('pages.job-applications.edit', compact('jobApplication', 'users', 'providers', 'services', 'categories', 'userSchedules', 'userLocations'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(JobRequest $request, JobApplication $jobApplication)
    {
        \Log::info('Updating job application', [
            'id' => $jobApplication->id,
            'request_data' => $request->all(),
        ]);

        $data = $request->validated();
        \Log::info('Validated data', ['data' => $data]);
        $data['is_employee'] = false; // Always set to false as we don't want employees

        // Ensure provider_id is included
        if (!isset($data['provider_id'])) {
            $data['provider_id'] = null;
        }
        if (isset($data['provider_hourly_cost_fixed'])) {
            $data['provider_hourly_cost'] = $data['provider_hourly_cost_fixed'];
            $data['provider_fixed_cost'] = 0;
        }
        // Copiar el valor de provider_hourly_cost_fixed a provider_hourly_cost si existe
        // if (isset($data['provider_payment_method_fixed']) && $data['provider_payment_method_fixed'] == 'hourly') {

        // } else {
        //     if (isset($data['provider_fixed_cost'])) {
        //         $data['provider_fixed_cost'] = $data['provider_fixed_cost'];
        //         $data['provider_hourly_cost'] = 0;
        //     }
        // }
        // Convertir los valores 'hours' y 'meters' a IDs de servicio reales
        if (isset($data['service_id'])) {
            if ($data['service_id'] === 'hours') {
                // Buscar un servicio de categoría 1 (horas)
                $service = Service::where('category_id', 1)->first();
                if ($service) {
                    $data['service_id'] = $service->id;
                }
            } elseif ($data['service_id'] === 'meters') {
                // Buscar un servicio de categoría 2 (metros)
                $service = Service::where('category_id', 2)->first();
                if ($service) {
                    $data['service_id'] = $service->id;
                }
            }
        }

        // // تحويل الجدول الزمني إلى التنسيق المطلوب
        // if (isset($data['schedule']) && is_array($data['schedule'])) {
        //     // تنظيم البيانات حسب اليوم
        //     $organizedSchedule = [];
        //     $scheduleByDay = [];

        //     // جلب آخر قيمة للمفتاح 'd_id' من الجدول الزمني الحالي
        //     $lastDId = 0;
        //     if (!empty($jobApplication->schedule)) {
        //         $existingSchedule = is_string($jobApplication->schedule) ? json_decode($jobApplication->schedule, true) : $jobApplication->schedule;
        //         foreach ($existingSchedule as $daySchedule) {
        //             if (isset($daySchedule['hours']) && is_array($daySchedule['hours'])) {
        //                 foreach ($daySchedule['hours'] as $hour) {
        //                     if (isset($hour['d_id']) && $hour['d_id'] > $lastDId) {
        //                         $lastDId = $hour['d_id'];
        //                     }
        //                 }
        //             }
        //         }
        //     }

        //     // تجميع البيانات حسب اليوم
        //     foreach ($data['schedule'] as $item) {
        //         if (!isset($scheduleByDay[$item['day']])) {
        //             $scheduleByDay[$item['day']] = [];
        //         }

        //         // إضافة الساعات إلى اليوم المناسب
        //         $scheduleByDay[$item['day']][] = [
        //             'd_id' => ++$lastDId, // Increment the counter starting from the last d_id
        //             'duration' => $item['hours'],
        //             'start_time' => $item['start_time'],
        //         ];
        //     }

        //     // تحويل البيانات إلى التنسيق المطلوب
        //     foreach ($scheduleByDay as $day => $hours) {
        //         $organizedSchedule[] = [
        //             'day' => $day,
        //             'hours' => $hours,
        //         ];
        //     }

        //     $data['schedule'] = $organizedSchedule; // json_encode($organizedSchedule);
        // }
        $data = $request->except('schedule'); // Exclude 'schedule' from the request data
        $jobApplication->update($data);

        return redirect()->route('job-applications.index')->with('success', __('message.job_application_updated_successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JobApplication $jobApplication)
    {
        $jobApplication->delete();

        return redirect()->route('job-applications.index')->with('success', __('message.job_application_deleted_successfully'));
    }

    /**
     * Toggle the active status of the job application.
     */
    public function toggleActive(JobApplication $jobApplication)
    {
        $jobApplication->is_active = !$jobApplication->is_active;
        $jobApplication->save();

        return redirect()->back()->with('success', __('message.job_application_status_updated'));
    }

    /**
     * Stop a recurring job application.
     */
    public function stopRecurring(Request $request, JobApplication $jobApplication)
    {
        $request->validate([
            'stop_date' => 'required|date',
            'stop_reason' => 'required|string|max:255',
        ]);

        // Update the job application
        $jobApplication->is_recurring = false;
        $jobApplication->stop_date = $request->stop_date;
        $jobApplication->stop_reason = $request->stop_reason;
        $jobApplication->save();

        return redirect()->back()->with('success', __('message.job_application_recurring_stopped'));
    }

    /**
     * Format user schedules for display.
     */
    private function formatUserSchedules($providerSchedules, $orders)
    {
        $formattedSchedules = [
            'sunday' => [],
            'monday' => [],
            'tuesday' => [],
            'wednesday' => [],
            'thursday' => [],
            'friday' => [],
            'saturday' => [],
        ];

        // Add provider schedules
        foreach ($providerSchedules as $schedule) {
            $day = strtolower(Carbon::parse($schedule->date)->format('l'));
            $formattedSchedules[$day][] = [
                'type' => 'provider_schedule',
                'start_time' => $schedule->start_time,
                'end_time' => $schedule->end_time,
                'description' => __('message.provider_schedule'),
            ];
        }

        // Add order schedules (excluding past dates)
        $today = Carbon::today();
        foreach ($orders as $order) {
            foreach ($order->orderSchedules as $schedule) {
                $scheduleDate = Carbon::parse($schedule->date);

                // Skip past dates
                if ($scheduleDate->lt($today)) {
                    continue;
                }

                $day = strtolower($scheduleDate->format('l'));
                $formattedSchedules[$day][] = [
                    'type' => 'order',
                    'start_time' => $schedule->start_time,
                    'end_time' => $schedule->end_time,
                    'description' => __('message.order').' #'.$order->id,
                    'order_id' => $order->id,
                    'date' => $scheduleDate->format('Y-m-d'),
                ];
            }
        }

        // Sort schedules by start time
        foreach ($formattedSchedules as $day => $schedules) {
            usort($formattedSchedules[$day], function ($a, $b) {
                return strtotime($a['start_time']) - strtotime($b['start_time']);
            });
        }

        return $formattedSchedules;
    }

    /**
     * Format events for calendar display.
     */
    private function formatCalendarEvents($providerSchedules, $orders)
    {
        $events = [];

        // Add provider schedules to calendar
        foreach ($providerSchedules as $schedule) {
            $date = Carbon::parse($schedule->date)->format('Y-m-d');
            $events[] = [
                'id' => 'provider_'.$schedule->id,
                'title' => __('message.provider_schedule'),
                'start' => $date.'T'.$schedule->start_time,
                'end' => $date.'T'.$schedule->end_time,
                'backgroundColor' => '#4285F4',
                'borderColor' => '#4285F4',
                'textColor' => '#ffffff',
                'extendedProps' => [
                    'type' => 'provider_schedule',
                    'schedule_id' => $schedule->id,
                    'description' => __('message.provider_schedule'),
                    'start_time' => $schedule->start_time,
                    'end_time' => $schedule->end_time,
                ],
            ];
        }

        // Add order schedules to calendar (excluding past dates)
        $today = Carbon::today();
        foreach ($orders as $order) {
            foreach ($order->orderSchedules as $schedule) {
                $scheduleDate = Carbon::parse($schedule->date);

                // Skip past dates
                if ($scheduleDate->lt($today)) {
                    continue;
                }

                $date = $scheduleDate->format('Y-m-d');

                // Get service name if available
                $serviceName = '';
                if ($order->service) {
                    $serviceName = $order->service->getTranslation('name', app()->getLocale());
                }

                // Format title to include time
                $title = __('message.order').' #'.$order->id;
                if ($serviceName) {
                    $title .= ' - '.$serviceName;
                }

                $events[] = [
                    'id' => 'order_'.$order->id.'_'.$schedule->id,
                    'title' => $title,
                    'start' => $date.'T'.$schedule->start_time,
                    'end' => $date.'T'.$schedule->end_time,
                    'backgroundColor' => '#DB4437',
                    'borderColor' => '#DB4437',
                    'textColor' => '#ffffff',
                    'extendedProps' => [
                        'type' => 'order',
                        'order_id' => $order->id,
                        'schedule_id' => $schedule->id,
                        'description' => $title,
                        'start_time' => $schedule->start_time,
                        'end_time' => $schedule->end_time,
                        'service_name' => $serviceName,
                        'customer_name' => $order->user ? $order->user->name : '',
                        'provider_name' => $order->provider ? $order->provider->name : '',
                    ],
                ];
            }
        }

        return $events;
    }

    /**
     * Get user calendar events via AJAX.
     */
    public function getUserCalendarEvents(Request $request)
    {
        $userId = $request->input('user_id');
        $providerId = $request->input('provider_id');

        if (!$userId) {
            return response()->json(['events' => []]);
        }

        // Get provider schedules if provider is selected
        $providerSchedules = collect();
        if ($providerId) {
            $providerSchedules = ProviderSchedule::where('user_id', $providerId)->get();
        }

        // Get orders for the user and provider
        $ordersQuery = Order::with('orderSchedules');

        // Add user orders
        $ordersQuery->where('user_id', $userId);

        // Add provider orders if provider is selected
        if ($providerId) {
            $ordersQuery->orWhere('provider_id', $providerId);
        }

        $orders = $ordersQuery->get();

        // Format events for calendar
        $calendarEvents = $this->formatCalendarEvents($providerSchedules, $orders);

        return response()->json(['events' => $calendarEvents]);
    }

    /**
     * Get user locations via AJAX.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserLocations(Request $request)
    {
        $userId = $request->input('user_id');

        if (!$userId) {
            return response()->json([
                'success' => false,
                'message' => 'User ID is required',
                'locations' => [],
            ]);
        }

        try {
            // Get locations for the selected user
            $locations = UserLocation::where('user_id', $userId)->get();

            // Log for debugging
            \Log::info('User locations retrieved', [
                'user_id' => $userId,
                'count' => $locations->count(),
                'locations' => $locations,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Locations retrieved successfully',
                'locations' => $locations,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error retrieving user locations', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving locations: '.$e->getMessage(),
                'locations' => [],
            ]);
        }
    }

    /**
     * Get provider services via AJAX.
     */
    public function getProviderServices(Request $request)
    {
        // تسجيل بيانات الطلب للمساعدة في تشخيص المشكلة
        \Log::info('Provider services request received with data', [
            'all_data' => $request->all(),
            'provider_id' => $request->input('provider_id'),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
        ]);

        $providerId = $request->input('provider_id');

        if (!$providerId) {
            return response()->json([
                'success' => false,
                'message' => 'Provider ID is required',
                'services' => [],
            ]);
        }

        try {
            // Get services for the selected provider
            $providerServices = \App\Models\ProviderService::where('provider_id', $providerId)->with('service')->get();

            \Log::info('Provider services found', [
                'provider_id' => $providerId,
                'count' => $providerServices->count(),
                'services' => $providerServices,
            ]);

            // Extract just the services with properly formatted names
            $services = $providerServices->map(function ($providerService) {
                $service = $providerService->service;
                if ($service) {
                    // Make sure the service name is properly formatted for the frontend
                    // For Spatie translatable fields, this will convert the JSON to an array
                    $service->name = $service->getTranslations('name');
                }

                return $service;
            })->filter(function ($service) {
                // تحقق فقط من وجود الخدمة
                return $service != null;
            })->unique('id')->values(); // Use unique() to remove duplicate services

            // Log for debugging
            \Log::info('Provider services extracted', [
                'provider_id' => $providerId,
                'count' => $services->count(),
                'services' => $services,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Services retrieved successfully',
                'services' => $services,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error retrieving provider services', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving services: '.$e->getMessage(),
                'services' => [],
            ]);
        }
    }

    /**
     * Get user monthly schedule via AJAX.
     */
    public function getUserMonthlySchedule(Request $request)
    {
        $userId = $request->input('user_id');
        $month = $request->input('month', date('m'));
        $year = $request->input('year', date('Y'));

        if (!$userId) {
            return response()->json(['schedules' => []]);
        }

        // Get provider schedules if user is a provider (not used in this function but kept for reference)
        // $providerSchedules = ProviderSchedule::where('user_id', $userId)->get();

        // Get orders for the provider
        $orders = Order::where('provider_id', $userId)
            ->with('orderSchedules')
            ->get();

        // Get job applications for the provider
        $jobApplications = JobApplication::where('provider_id', $userId)
            ->where('is_active', true)
            ->get();

        // Get the first and last day of the month
        $startDate = Carbon::createFromDate($year, $month, 1)->startOfMonth();
        $endDate = Carbon::createFromDate($year, $month, 1)->endOfMonth();

        // Current date for filtering past dates
        $today = Carbon::today();

        // Initialize the schedule array with all days of the month
        $monthlySchedule = [];
        $currentDate = $startDate->copy();

        // Define the order of days (Saturday to Friday)
        $orderedDays = ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'];

        // Create an array for each day of the month
        while ($currentDate->lte($endDate)) {
            // Skip past dates
            if ($currentDate->lt($today)) {
                $currentDate->addDay();
                continue;
            }

            $dayName = strtolower($currentDate->format('l'));
            $dateStr = $currentDate->format('Y-m-d');

            $monthlySchedule[$dateStr] = [
                'date' => $dateStr,
                'day_name' => $dayName,
                'day_number' => $currentDate->format('d'),
                'day_name_translated' => __('message.'.$dayName),
                'schedules' => [],
            ];

            $currentDate->addDay();
        }

        // Add order schedules to the monthly schedule
        foreach ($orders as $order) {
            foreach ($order->orderSchedules as $schedule) {
                $scheduleDate = Carbon::parse($schedule->date);
                $dateStr = $scheduleDate->format('Y-m-d');

                // Skip if not in the requested month or is a past date
                if (!$scheduleDate->isSameMonth($startDate) || $scheduleDate->lt($today)) {
                    continue;
                }

                // Skip if the date is not in our monthly schedule (should not happen, but just in case)
                if (!isset($monthlySchedule[$dateStr])) {
                    continue;
                }

                // Get service name if available
                $serviceName = '';
                if ($order->typable && $order->typable_type === 'App\\Models\\Service') {
                    $service = Service::find($order->typable_id);
                    if ($service) {
                        $serviceName = $service->name;
                    }
                }

                // Get client name
                $clientName = '';
                if ($order->user) {
                    $clientName = $order->user->name;
                }

                // Add the schedule to the monthly schedule
                $monthlySchedule[$dateStr]['schedules'][] = [
                    'type' => 'order',
                    'id' => $order->id,
                    'start_time' => $schedule->start_time,
                    'end_time' => $schedule->end_time,
                    'description' => __('message.order').' #'.$order->id.' - '.$serviceName.' ('.$clientName.')',
                    'status' => $order->status,
                ];
            }
        }

        // Add job application schedules to the monthly schedule
        foreach ($jobApplications as $jobApp) {
            if (!empty($jobApp->schedule)) {
                $appSchedule = is_string($jobApp->schedule) ? json_decode($jobApp->schedule, true) : $jobApp->schedule;

                if (is_array($appSchedule)) {
                    foreach ($appSchedule as $scheduleItem) {
                        // التحقق من أن الجدول يحتوي على يوم
                        if (isset($scheduleItem['day'])) {
                            $dayName = $scheduleItem['day'];

                            // البحث عن التواريخ المطابقة لهذا اليوم في الشهر الحالي
                            foreach ($monthlySchedule as $dateStr => $dateSchedule) {
                                if ($dateSchedule['day_name'] === $dayName) {
                                    // التحقق من وجود ساعات في الجدول
                                    if (isset($scheduleItem['hours']) && is_array($scheduleItem['hours'])) {
                                        foreach ($scheduleItem['hours'] as $hourItem) {
                                            if (isset($hourItem['start_time']) && isset($hourItem['duration'])) {
                                                $startTime = $hourItem['start_time'];
                                                $duration = (int) $hourItem['duration'];
                                                $endTime = Carbon::createFromFormat('H:i', $startTime)
                                                    ->addHours($duration)
                                                    ->format('H:i');

                                                // Get client name
                                                $clientName = '';
                                                if ($jobApp->user) {
                                                    $clientName = $jobApp->user->name;
                                                }

                                                // Get service name
                                                $serviceName = '';
                                                if ($jobApp->service) {
                                                    $serviceName = $jobApp->service->name;
                                                }

                                                // Add the schedule to the monthly schedule
                                                $monthlySchedule[$dateStr]['schedules'][] = [
                                                    'type' => 'job',
                                                    'id' => $jobApp->id,
                                                    'start_time' => $startTime,
                                                    'end_time' => $endTime,
                                                    'description' => __('message.job_application').' #'.$jobApp->id.' - '.$serviceName.' ('.$clientName.')',
                                                    'status' => $jobApp->status,
                                                ];
                                            }
                                        }
                                    } elseif (isset($scheduleItem['start_time']) && isset($scheduleItem['hours'])) {
                                        // التعامل مع الشكل القديم للجدول
                                        $startTime = $scheduleItem['start_time'];
                                        $hours = (int) $scheduleItem['hours'];
                                        $endTime = Carbon::createFromFormat('H:i', $startTime)
                                            ->addHours($hours)
                                            ->format('H:i');

                                        // Get client name
                                        $clientName = '';
                                        if ($jobApp->user) {
                                            $clientName = $jobApp->user->name;
                                        }

                                        // Get service name
                                        $serviceName = '';
                                        if ($jobApp->service) {
                                            $serviceName = $jobApp->service->name;
                                        }

                                        // Add the schedule to the monthly schedule
                                        $monthlySchedule[$dateStr]['schedules'][] = [
                                            'type' => 'job',
                                            'id' => $jobApp->id,
                                            'start_time' => $startTime,
                                            'end_time' => $endTime,
                                            'description' => __('message.job_application').' #'.$jobApp->id.' - '.$serviceName.' ('.$clientName.')',
                                            'status' => $jobApp->status,
                                        ];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Group schedules by day of week
        $groupedByDay = [];
        foreach ($orderedDays as $day) {
            $groupedByDay[$day] = [];
        }

        foreach ($monthlySchedule as $dateStr => $dateSchedule) {
            $dayName = $dateSchedule['day_name'];
            if (!empty($dateSchedule['schedules'])) {
                $groupedByDay[$dayName][] = $dateSchedule;
            }
        }

        return response()->json([
            'schedules' => $groupedByDay,
            'month_name' => $startDate->format('F Y'),
        ]);
    }

    public function checkAvailability(Request $request)
    {
        try {
            \Log::info('Check availability request received', [
                'request_data' => $request->all(),
            ]);

            $validator = Validator::make($request->all(), [
                'provider_id' => 'required|exists:users,id',
                'day' => 'required|in:saturday,sunday,monday,tuesday,wednesday,thursday,friday',
                'duration' => 'required|integer|min:1|max:12',
                'job_application_id' => 'nullable|exists:job_applications,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            $providerId = $request->provider_id;
            $day = $request->day;
            $duration = (int) $request->duration;
            $jobApplicationId = $request->job_application_id;

            \Log::info('Validated request data', [
                'provider_id' => $providerId,
                'day' => $day,
                'duration' => $duration,
                'job_application_id' => $jobApplicationId,
            ]);

            // 1. التحقق من أوقات عمل مقدم الخدمة لليوم المحدد
            $providerWorkTime = ProviderWorkTime::where('user_id', $providerId)
                ->where('day', $day)
                ->first();

            if (!$providerWorkTime) {
                \Log::info('Provider does not work on this day', [
                    'provider_id' => $providerId,
                    'day' => $day,
                ]);

                // بدلاً من إرجاع خطأ، نرجع قائمة فارغة من الفترات
                return response()->json([
                    'success' => true,
                    'message' => __('message.provider_not_working_on_this_day'),
                    'slots' => [],
                ]);
            }

            \Log::info('Provider work time found', [
                'provider_id' => $providerId,
                'day' => $day,
                'start_at' => $providerWorkTime->start_at,
                'end_at' => $providerWorkTime->end_at,
            ]);

            // 2. التحقق من الإجازات
            $hasHoliday = false;
            try {
                $hasHoliday = ProviderHoliday::where('user_id', $providerId)
                    ->where(function ($query) use ($day) {
                        $query->whereRaw('LOWER(DAYNAME(starts_at)) = ?', [$day])
                            ->orWhereRaw('LOWER(DAYNAME(ends_at)) = ?', [$day]);
                    })
                    ->exists();
            } catch (\Exception $e) {
                \Log::error('Error checking provider holidays', [
                    'provider_id' => $providerId,
                    'day' => $day,
                    'error' => $e->getMessage(),
                ]);
                // نتجاهل الخطأ ونفترض أنه لا توجد إجازات
            }

            if ($hasHoliday) {
                \Log::info('Provider is on holiday', [
                    'provider_id' => $providerId,
                    'day' => $day,
                ]);

                // بدلاً من إرجاع خطأ، نرجع قائمة فارغة من الفترات
                return response()->json([
                    'success' => true,
                    'message' => __('message.provider_on_holiday'),
                    'slots' => [],
                ]);
            }

            // 3. جلب الحجوزات والوظائف لبناء الفترات المشغولة
            $today = Carbon::today();
            $orders = [];
            $jobs = [];

            try {
                $orders = Order::where('provider_id', $providerId)
                    ->whereIn('status', ['approved', 'confirmed'])
                    ->where(function ($query) use ($day) {
                        $query->whereRaw('LOWER(DAYNAME(start_date)) = ?', [$day])
                            ->orWhereRaw('LOWER(DAYNAME(end_date)) = ?', [$day]);
                    })
                    ->where(function ($query) use ($today) {
                        $query->whereDate('start_date', '>=', $today)
                            ->orWhereDate('end_date', '>=', $today);
                    })
                    ->get(['id', 'start_date', 'end_date']);

                \Log::info('Orders found', [
                    'provider_id' => $providerId,
                    'day' => $day,
                    'count' => $orders->count(),
                ]);
            } catch (\Exception $e) {
                \Log::error('Error fetching orders', [
                    'provider_id' => $providerId,
                    'day' => $day,
                    'error' => $e->getMessage(),
                ]);
                // نتجاهل الخطأ ونفترض أنه لا توجد طلبات
            }

            try {
                $jobs = JobApplication::where('provider_id', $providerId)
                    ->whereJsonContains('schedule', [['day' => $day]])
                    ->where('is_active', 1)
                    ->get(['id', 'schedule']);

                \Log::info('Job applications found', [
                    'provider_id' => $providerId,
                    'day' => $day,
                    'count' => $jobs->count(),
                ]);
            } catch (\Exception $e) {
                \Log::error('Error fetching job applications', [
                    'provider_id' => $providerId,
                    'day' => $day,
                    'error' => $e->getMessage(),
                ]);
                // نتجاهل الخطأ ونفترض أنه لا توجد طلبات توظيف
            }

            $busySlots = [];

            // إضافة الحجوزات إلى الفترات المشغولة
            foreach ($orders as $order) {
                try {
                    $orderStart = Carbon::parse($order->start_date);
                    $orderEnd = Carbon::parse($order->end_date);

                    // تحويل الأوقات إلى الساعات والدقائق فقط للمقارنة الصحيحة
                    $formattedOrderStart = Carbon::today()->setHour($orderStart->hour)->setMinute($orderStart->minute);
                    $formattedOrderEnd = Carbon::today()->setHour($orderEnd->hour)->setMinute($orderEnd->minute);

                    $busySlots[] = [
                        'start' => $formattedOrderStart,
                        'end' => $formattedOrderEnd,
                        'type' => 'order',
                    ];
                } catch (\Exception $e) {
                    \Log::error('Error processing order', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                    ]);
                    // نتجاهل هذا الطلب ونستمر
                    continue;
                }
            }

            // إضافة الوظائف إلى الفترات المشغولة
            foreach ($jobs as $job) {
                try {
                    $scheduleData = $job->schedule;

                    // التحقق من أن البيانات صالحة
                    if (is_string($scheduleData)) {
                        $scheduleData = json_decode($scheduleData, true);
                    }

                    if (!is_array($scheduleData)) {
                        \Log::warning('Invalid schedule data', [
                            'job_id' => $job->id,
                            'schedule' => $scheduleData,
                        ]);
                        continue; // تخطي هذه الوظيفة إذا كانت البيانات غير صالحة
                    }

                    foreach ($scheduleData as $schedule) {
                        if (isset($schedule['day']) && $schedule['day'] === $day) {
                            // التحقق من وجود ساعات وأنها مصفوفة
                            if (isset($schedule['hours']) && is_array($schedule['hours'])) {
                                foreach ($schedule['hours'] as $hour) {
                                    if (isset($hour['start_time']) && isset($hour['duration'])) {
                                        try {
                                            $jobStartTime = Carbon::createFromFormat('H:i', $hour['start_time']);
                                            $jobEndTime = $jobStartTime->copy()->addHours((int) $hour['duration']);

                                            // تحويل الأوقات إلى الساعات والدقائق فقط للمقارنة الصحيحة
                                            $formattedJobStartTime = Carbon::today()->setHour($jobStartTime->hour)->setMinute($jobStartTime->minute);
                                            $formattedJobEndTime = Carbon::today()->setHour($jobEndTime->hour)->setMinute($jobEndTime->minute);

                                            $busySlots[] = [
                                                'start' => $formattedJobStartTime,
                                                'end' => $formattedJobEndTime,
                                                'type' => 'job',
                                            ];
                                        } catch (\Exception $e) {
                                            \Log::error('Error processing job hour', [
                                                'job_id' => $job->id,
                                                'start_time' => $hour['start_time'] ?? 'unknown',
                                                'duration' => $hour['duration'] ?? 'unknown',
                                                'error' => $e->getMessage(),
                                            ]);
                                            // تجاهل الساعات غير الصالحة
                                            continue;
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (\Exception $e) {
                    \Log::error('Error processing job application', [
                        'job_id' => $job->id,
                        'error' => $e->getMessage(),
                    ]);
                    // تجاهل الوظائف التي تسبب أخطاء
                    continue;
                }
            }

            // فرز الفترات المشغولة حسب وقت البداية (تصاعدي)
            if (!empty($busySlots)) {
                usort($busySlots, function ($a, $b) {
                    return $a['start']->lt($b['start']) ? -1 : 1;
                });
            }

            // دمج الفترات المتداخلة حسب النوع (نحتفظ بمعلومات نوع التداخل)
            $mergedBusySlots = [];

            // دمج الفترات المتداخلة من نوع "order"
            $orderSlots = array_filter($busySlots, function ($slot) {
                return $slot['type'] === 'order';
            });

            $mergedOrderSlots = $this->mergeOverlappingSlots($orderSlots, 'order');

            // دمج الفترات المتداخلة من نوع "job"
            $jobSlots = array_filter($busySlots, function ($slot) {
                return $slot['type'] === 'job';
            });

            $mergedJobSlots = $this->mergeOverlappingSlots($jobSlots, 'job');

            // جمع الفترات المدمجة حسب النوع
            $mergedBusySlots = array_merge($mergedOrderSlots, $mergedJobSlots);

            // إعادة فرز الفترات المدمجة حسب وقت البداية
            if (!empty($mergedBusySlots)) {
                usort($mergedBusySlots, function ($a, $b) {
                    return $a['start']->lt($b['start']) ? -1 : 1;
                });
            }

            // استخراج الأوقات المحجوزة في نفس الطلب
            $currentJobSlots = [];
            if ($jobApplicationId) {
                $jobApplication = JobApplication::find($jobApplicationId);
                if ($jobApplication) {
                    $scheduleData = $jobApplication->schedule;

                    if (is_string($scheduleData) && !empty($scheduleData)) {
                        $decoded = @json_decode($scheduleData, true);
                        $scheduleData = (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) ? $decoded : [];
                    }

                    if (is_array($scheduleData)) {
                        foreach ($scheduleData as $item) {
                            if (isset($item['day']) && $item['day'] === $day && isset($item['hours']) && is_array($item['hours'])) {
                                foreach ($item['hours'] as $hour) {
                                    if (isset($hour['start_time']) && isset($hour['duration'])) {
                                        $startTime = $hour['start_time'];
                                        $durationHours = (int) $hour['duration'];

                                        // حساب وقت الانتهاء
                                        $startTimeParts = explode(':', $startTime);
                                        $startHour = (int) $startTimeParts[0];
                                        $startMinute = (int) $startTimeParts[1];

                                        $endHour = $startHour + $durationHours;
                                        $endMinute = $startMinute;

                                        // تنسيق وقت الانتهاء
                                        $endTime = sprintf('%02d:%02d', $endHour, $endMinute);

                                        $currentJobSlots[] = [
                                            'start_time' => $startTime,
                                            'end_time' => $endTime,
                                            'is_current_job' => true,
                                        ];
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // استخراج الأوقات المحجوزة في نفس الطلب
            $currentJobSlots = [];
            if ($jobApplicationId) {
                $jobApplication = JobApplication::find($jobApplicationId);
                if ($jobApplication) {
                    $scheduleData = $jobApplication->schedule;

                    if (is_string($scheduleData) && !empty($scheduleData)) {
                        $decoded = @json_decode($scheduleData, true);
                        $scheduleData = (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) ? $decoded : [];
                    }

                    if (is_array($scheduleData)) {
                        foreach ($scheduleData as $item) {
                            if (isset($item['day']) && $item['day'] === $day && isset($item['hours']) && is_array($item['hours'])) {
                                foreach ($item['hours'] as $hour) {
                                    if (isset($hour['start_time']) && isset($hour['duration'])) {
                                        $startTime = $hour['start_time'];
                                        $durationHours = (int) $hour['duration'];

                                        // حساب وقت الانتهاء
                                        $startTimeParts = explode(':', $startTime);
                                        $startHour = (int) $startTimeParts[0];
                                        $startMinute = (int) $startTimeParts[1];

                                        $endHour = $startHour + $durationHours;
                                        $endMinute = $startMinute;

                                        // تنسيق وقت الانتهاء
                                        $endTime = sprintf('%02d:%02d', $endHour, $endMinute);

                                        $currentJobSlots[] = [
                                            'start_time' => $startTime,
                                            'end_time' => $endTime,
                                            'is_current_job' => true,
                                        ];
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 4. توليد الفترات الزمنية المتاحة
            $availableSlots = [];

            try {
                $startOfWorkDay = Carbon::today()->setHour(Carbon::parse($providerWorkTime->start_at)->hour)->setMinute(Carbon::parse($providerWorkTime->start_at)->minute);
                $endOfWorkDay = Carbon::today()->setHour(Carbon::parse($providerWorkTime->end_at)->hour)->setMinute(Carbon::parse($providerWorkTime->end_at)->minute);

                $currentTime = $startOfWorkDay->copy();

                // تخزين الساعات المستخدمة لمنع التكرار
                $usedHours = [];

                while ($currentTime->copy()->addHours($duration)->lte($endOfWorkDay)) {
                    $slotStart = $currentTime->copy();
                    $slotEnd = $currentTime->copy()->addHours($duration);
                    $currentHour = $slotStart->format('H');

                    $hasOrderConflict = false;
                    $hasJobConflict = false;
                    $isDuplicateHour = in_array($currentHour, $usedHours);
                    $isCurrentJobSlot = false;

                    // التحقق مما إذا كان هذا الوقت محجوزًا في نفس الطلب
                    $currentSlotStartTime = $slotStart->format('H:i');
                    $currentSlotEndTime = $slotEnd->format('H:i');

                    foreach ($currentJobSlots as $currentJobSlot) {
                        if ($currentJobSlot['start_time'] === $currentSlotStartTime && $currentJobSlot['end_time'] === $currentSlotEndTime) {
                            $isCurrentJobSlot = true;
                            break;
                        }
                    }

                    // التحقق من التعارض مع الحجوزات المدمجة
                    foreach ($mergedBusySlots as $busySlot) {
                        if ($busySlot['type'] === 'order' && $this->isOverlap($slotStart, $slotEnd, $busySlot['start'], $busySlot['end'])) {
                            $hasOrderConflict = true;
                            break;
                        }
                    }

                    // إذا لم يكن هناك تعارض مع الحجوزات، التحقق من الوظائف المدمجة
                    if (!$hasOrderConflict) {
                        foreach ($mergedBusySlots as $busySlot) {
                            if ($busySlot['type'] === 'job' && $this->isOverlap($slotStart, $slotEnd, $busySlot['start'], $busySlot['end'])) {
                                $hasJobConflict = true;
                                break;
                            }
                        }
                    }

                    // الوقت متاح إذا لم يكن هناك تعارض مع الحجوزات أو الوظائف الأخرى وليس مكررًا
                    $isAvailable = !$hasOrderConflict && !$hasJobConflict && !$isDuplicateHour;

                    // إضافة الفترة إذا كانت متاحة أو محجوزة في نفس الطلب
                    if ($isAvailable || $isCurrentJobSlot) {
                        $availableSlots[] = [
                            'start_time' => $currentSlotStartTime,
                            'end_time' => $currentSlotEndTime,
                            'is_available' => $isAvailable,
                            'is_current_job' => $isCurrentJobSlot,
                        ];

                        // تسجيل الساعة كمستخدمة لمنع تكرارها
                        $usedHours[] = $currentHour;
                    }

                    $currentTime->addHour();
                }
            } catch (\Exception $e) {
                \Log::error('Error generating time slots', [
                    'provider_id' => $providerId,
                    'day' => $day,
                    'error' => $e->getMessage(),
                ]);

                // في حالة حدوث خطأ، نرجع قائمة فارغة من الفترات
                return response()->json([
                    'success' => true,
                    'message' => 'Error generating time slots: '.$e->getMessage(),
                    'slots' => [],
                ]);
            }

            \Log::info('Available slots generated', [
                'provider_id' => $providerId,
                'day' => $day,
                'count' => count($availableSlots),
            ]);

            // دمج الأوقات المتاحة مع الأوقات المحجوزة في نفس الطلب
            $availableSlotsFiltered = array_filter($availableSlots, function ($slot) {
                return $slot['is_available'] === true;
            });

            // دمج الأوقات المتاحة مع الأوقات المحجوزة في نفس الطلب
            $allSlots = array_merge($availableSlotsFiltered, $currentJobSlots);

            return response()->json([
                'success' => true,
                'message' => __('message.available_slots_retrieved'),
                'slots' => $allSlots,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in checkAvailability', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // في حالة حدوث خطأ، نرجع قائمة فارغة من الفترات بدلاً من رمي خطأ 500
            return response()->json([
                'success' => true,
                'message' => 'Error checking availability: '.$e->getMessage(),
                'slots' => [],
            ]);
        }
    }

    /**
     * دمج الفترات المتداخلة من نفس النوع.
     *
     * @param array  $slots
     * @param string $type
     *
     * @return array
     */
    private function mergeOverlappingSlots($slots, $type)
    {
        if (empty($slots)) {
            return [];
        }

        try {
            // فرز الفترات بالترتيب الزمني
            usort($slots, function ($a, $b) {
                return $a['start']->lt($b['start']) ? -1 : 1;
            });

            $merged = [];
            $current = reset($slots); // أول فترة

            foreach ($slots as $slot) {
                // إذا كانت الفترة الحالية متداخلة مع الفترة المجمعة
                if ($current['end']->gte($slot['start'])) {
                    // توسيع الفترة المجمعة إذا كانت الفترة الحالية تنتهي بعدها
                    if ($slot['end']->gt($current['end'])) {
                        $current['end'] = $slot['end'];
                    }
                } else {
                    // إضافة الفترة المجمعة وبدء واحدة جديدة
                    $merged[] = $current;
                    $current = $slot;
                }
            }

            // إضافة الفترة الأخيرة المجمعة
            $merged[] = $current;

            // إضافة نوع الفترة لكل فترة مدمجة
            foreach ($merged as &$slot) {
                $slot['type'] = $type;
            }

            return $merged;
        } catch (\Exception $e) {
            \Log::error('Error in mergeOverlappingSlots', [
                'error' => $e->getMessage(),
                'slots_count' => count($slots),
                'type' => $type,
            ]);

            // في حالة حدوث خطأ، نرجع المصفوفة الأصلية مع إضافة النوع
            foreach ($slots as &$slot) {
                $slot['type'] = $type;
            }

            return $slots;
        }
    }

    /**
     * التحقق من وجود تداخل بين فترتين زمنيتين.
     *
     * @param Carbon $slotStart
     * @param Carbon $slotEnd
     * @param Carbon $busyStart
     * @param Carbon $busyEnd
     *
     * @return bool
     */
    private function isOverlap($slotStart, $slotEnd, $busyStart, $busyEnd)
    {
        try {
            return $slotStart->lt($busyEnd) && $slotEnd->gt($busyStart);
        } catch (\Exception $e) {
            \Log::error('Error in isOverlap', [
                'error' => $e->getMessage(),
                'slotStart' => $slotStart,
                'slotEnd' => $slotEnd,
                'busyStart' => $busyStart,
                'busyEnd' => $busyEnd,
            ]);

            // في حالة حدوث خطأ، نفترض أن هناك تداخل لتجنب الحجز المتعارض
            return true;
        }
    }

    public function getAvailableSlots(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider_id' => 'required|exists:users,id',
            'day' => 'required|in:saturday,sunday,monday,tuesday,wednesday,thursday,friday',
            'duration' => 'required|integer|min:1|max:12',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $providerId = $request->provider_id;
        $day = $request->day;
        $duration = (int) $request->duration;

        // 1. جلب جدول عمل الموظف
        $schedule = ProviderWorkTime::where('user_id', $providerId)
            ->where('day', $day)
            ->first();

        if (!$schedule) {
            return response()->json([
                'success' => true,
                'slots' => [],
            ]);
        }

        $startTime = Carbon::parse($schedule->start_at);
        $endTime = Carbon::parse($schedule->end_at);

        // 2. جلب جميع الحجوزات الفعالة
        $today = Carbon::today();
        $orders = Order::where('provider_id', $providerId)
            ->whereIn('status', ['approved', 'confirmed'])
            ->where(function ($query) use ($day) {
                $query->whereRaw('LOWER(DAYNAME(start_date)) = ?', [$day])
                      ->orWhereRaw('LOWER(DAYNAME(end_date)) = ?', [$day]);
            })
            ->where(function ($query) use ($today) {
                $query->whereDate('start_date', '>=', $today)
                      ->orWhereDate('end_date', '>=', $today);
            })
            ->get(['start_date', 'end_date']);

        // 3. جلب جميع الوظائف الدائمة الفعالة
        $jobs = JobApplication::where('provider_id', $providerId)
            ->whereJsonContains('schedule', [['day' => $day]])
            ->where('is_active', 1)
            ->get('schedule');

        // 4. تحضير جميع الفترات المشغولة
        $busyPeriods = [];

        // إضافة أوقات الوظائف الدائمة
        foreach ($jobs as $job) {
            foreach ($job->schedule as $jobSchedule) {
                if ($jobSchedule['day'] === $day) {
                    foreach ($jobSchedule['hours'] as $hour) {
                        $jobStartTime = Carbon::createFromFormat('H:i', $hour['start_time']);
                        $jobEndTime = $jobStartTime->copy()->addHours((int) $hour['duration']);

                        // تحويل الأوقات إلى الساعات والدقائق فقط لمقارنتها مع أوقات العمل
                        $formattedJobStartTime = Carbon::today()->setHour($jobStartTime->hour)->setMinute($jobStartTime->minute);
                        $formattedJobEndTime = Carbon::today()->setHour($jobEndTime->hour)->setMinute($jobEndTime->minute);

                        $busyPeriods[] = [
                            'start' => $formattedJobStartTime,
                            'end' => $formattedJobEndTime,
                            'type' => 'job',
                        ];
                    }
                }
            }
        }

        // إضافة أوقات الحجوزات
        foreach ($orders as $order) {
            $orderStart = Carbon::parse($order->start_date);
            $orderEnd = Carbon::parse($order->end_date);

            // تحويل الأوقات إلى الساعات والدقائق فقط لمقارنتها مع أوقات العمل
            $formattedOrderStart = Carbon::today()->setHour($orderStart->hour)->setMinute($orderStart->minute);
            $formattedOrderEnd = Carbon::today()->setHour($orderEnd->hour)->setMinute($orderEnd->minute);

            $busyPeriods[] = [
                'start' => $formattedOrderStart,
                'end' => $formattedOrderEnd,
                'type' => 'order',
            ];
        }

        // 5. دمج الفترات المتداخلة - إصلاح ترتيب الفترات
        usort($busyPeriods, function ($a, $b) {
            return $a['start']->lt($b['start']) ? -1 : 1; // ترتيب تصاعدي بالوقت
        });

        $mergedBusyPeriods = [];
        if (!empty($busyPeriods)) {
            $current = $busyPeriods[0];

            for ($i = 1; $i < count($busyPeriods); ++$i) {
                $period = $busyPeriods[$i];
                // إذا كان هناك تداخل، يتم دمج الفترات
                if ($period['start']->lte($current['end'])) {
                    // توسيع الفترة الحالية إذا كانت الفترة الجديدة تنتهي بعدها
                    if ($period['end']->gt($current['end'])) {
                        $current['end'] = $period['end'];
                    }
                } else {
                    // حفظ الفترة الحالية وبدء فترة جديدة
                    $mergedBusyPeriods[] = $current;
                    $current = $period;
                }
            }
            $mergedBusyPeriods[] = $current; // إضافة الفترة الأخيرة
        }

        // 6. حساب الفترات المتاحة
        $availableSlots = [];
        $currentSlotStart = Carbon::today()->setHour($startTime->hour)->setMinute($startTime->minute);
        $endOfWorkDay = Carbon::today()->setHour($endTime->hour)->setMinute($endTime->minute);

        // تخزين الساعات المستخدمة لمنع التكرار
        $usedHours = [];

        while ($currentSlotStart->copy()->addHours($duration)->lte($endOfWorkDay)) {
            $currentSlotEnd = $currentSlotStart->copy()->addHours($duration);
            $currentHour = $currentSlotStart->format('H');
            $isAvailable = true;
            $isDuplicateHour = in_array($currentHour, $usedHours);

            // التحقق من التداخل مع أي فترة مشغولة
            foreach ($mergedBusyPeriods as $busyPeriod) {
                // إذا كان هناك أي تداخل بين الفترة الحالية والفترة المشغولة
                if ($currentSlotStart->lt($busyPeriod['end']) && $currentSlotEnd->gt($busyPeriod['start'])) {
                    $isAvailable = false;
                    // الانتقال إلى نهاية الفترة المشغولة
                    $currentSlotStart = $busyPeriod['end']->copy();
                    break;
                }
            }

            if ($isAvailable && !$isDuplicateHour) {
                $availableSlots[] = [
                    'start_time' => $currentSlotStart->format('H:i'),
                    'end_time' => $currentSlotEnd->format('H:i'),
                ];
                // تسجيل الساعة كمستخدمة لمنع تكرارها
                $usedHours[] = $currentHour;
                $currentSlotStart = $currentSlotEnd;
            } elseif ($isAvailable) {
                // إذا كانت الساعة متكررة، ننتقل للساعة التالية
                $currentSlotStart = $currentSlotStart->copy()->addHour();
            }
        }

        // 7. تحضير الفترات المشغولة للعرض
        $formattedBusySlots = array_map(function ($period) {
            return [
                'start_time' => $period['start']->format('H:i'),
                'end_time' => $period['end']->format('H:i'),
            ];
        }, $mergedBusyPeriods);

        return response()->json([
            'success' => true,
            'WorkTime' => $schedule,
            'orders' => $orders,
            'jobs' => $jobs,
            'activeTime' => $availableSlots,
            'busyTime' => $formattedBusySlots,
        ]);
    }

    /**
     * Get available time slots for the provider based on their schedule.
     * Excludes times that are already booked in job applications or orders.
     *
     * @param int      $providerId
     * @param string   $day
     * @param string   $date
     * @param int      $duration
     * @param int|null $excludeJobApplicationId
     *
     * @return array
     */
    public function getAvailableTimeSlots($providerId, $day, $date, $duration = 1, $excludeJobApplicationId = null)
    {
        // Get provider schedules for the given day
        $providerSchedules = ProviderSchedule::where('provider_id', $providerId)
            ->where('day', $day)
            ->where('is_available', true)
            ->get();

        // Get existing orders for the provider on the given date that are approved or confirmed
        $existingOrders = Order::where('provider_id', $providerId)
            ->whereDate('date', $date)
            ->whereIn('status', ['approved', 'confirmed', 'completed'])
            ->get();

        // Get existing job applications for the provider on the given date
        $existingJobApplications = JobApplication::where('provider_id', $providerId)
            ->where('status', '!=', 'rejected')
            ->where('status', '!=', 'cancelled')
            ->when($excludeJobApplicationId, fn ($query) => $query->where('id', '!=', $excludeJobApplicationId))
            ->where(function ($query) use ($date) {
                $query->whereJsonContains('schedule', ['date' => $date])
                    ->orWhereJsonContains('schedule->date', $date);
            })
            ->get();

        // Calculate available time slots
        $availableSlots = [];
        $bookedTimeSlots = [];

        // Collect all booked time slots from orders
        foreach ($existingOrders as $order) {
            $orderStartTime = strtotime($order->start_time);
            $orderEndTime = strtotime($order->end_time);
            $bookedTimeSlots[] = [
                'start' => $orderStartTime,
                'end' => $orderEndTime,
            ];
        }

        // Collect all booked time slots from job applications
        foreach ($existingJobApplications as $jobApp) {
            $scheduleData = $jobApp->schedule;

            if (is_string($scheduleData) && !empty($scheduleData)) {
                $decoded = @json_decode($scheduleData, true);
                $scheduleData = (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) ? $decoded : [];
            }

            if (is_array($scheduleData)) {
                foreach ($scheduleData as $item) {
                    if (isset($item['date']) && $item['date'] == $date
                        && isset($item['start_time']) && isset($item['end_time'])) {
                        $appStartTime = strtotime($item['start_time']);
                        $appEndTime = strtotime($item['end_time']);
                        $bookedTimeSlots[] = [
                            'start' => $appStartTime,
                            'end' => $appEndTime,
                        ];
                    }
                }
            }
        }

        // Generate available time slots
        foreach ($providerSchedules as $schedule) {
            $startTime = strtotime($schedule->start_time);
            $endTime = strtotime($schedule->end_time);

            // تخزين الساعات المستخدمة لمنع التكرار
            $usedHours = [];

            // Create slots with the specified duration
            for ($time = $startTime; $time < $endTime; $time += $duration * 3600) {
                $slotStart = $time;
                $slotEnd = $time + $duration * 3600;
                $currentHour = date('H', $slotStart);
                $isDuplicateHour = in_array($currentHour, $usedHours);

                // Check if this slot conflicts with any booked slots
                $isAvailable = true;
                foreach ($bookedTimeSlots as $bookedSlot) {
                    if ($slotStart < $bookedSlot['end'] && $slotEnd > $bookedSlot['start']) {
                        $isAvailable = false;
                        break;
                    }
                }

                if ($isAvailable && !$isDuplicateHour) {
                    $availableSlots[] = [
                        'start_time' => date('H:i', $slotStart),
                        'end_time' => date('H:i', $slotEnd),
                        'display' => date('H:i', $slotStart).' - '.date('H:i', $slotEnd),
                        'value' => date('H:i', $slotStart),
                    ];

                    // تسجيل الساعة كمستخدمة لمنع تكرارها
                    $usedHours[] = $currentHour;
                }
            }
        }

        return $availableSlots;
    }

    /**
     * Get services by type (hours or meters).
     *
     * @param string $type
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getServicesByType($type = 'hours')
    {
        // Only allow 'hours' or 'meters' types
        if (!in_array($type, ['hours', 'meters'])) {
            $type = 'hours';
        }

        // Get services by type
        return Service::where('type', $type)->get();
    }

    /**
     * Get active service providers.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveProviders()
    {
        return User::whereIn('type', ['provider', 'company'])
            ->where('is_blocked', 0)
            ->whereHas('providerInfo', function ($query) {
                $query->where('is_employee', true);
            })
            ->get();
    }

    /**
     * Get active customers.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveCustomers()
    {
        return User::where('type', 'user')
            ->where('is_blocked', 0)
            ->get();
    }

    /**
     * Check if there is a duplicate job application for the same provider, date and time.
     *
     * @param int      $providerId
     * @param string   $date
     * @param string   $startTime
     * @param string   $endTime
     * @param int|null $excludeJobApplicationId
     *
     * @return bool
     */
    public function hasDuplicateJobApplication($providerId, $date, $startTime, $endTime, $excludeJobApplicationId = null)
    {
        // Get existing job applications for the provider on the given date
        $existingJobApplications = JobApplication::where('provider_id', $providerId)
            ->where('status', '!=', 'rejected')
            ->where('status', '!=', 'cancelled')
            ->when($excludeJobApplicationId, fn ($query) => $query->where('id', '!=', $excludeJobApplicationId))
            ->where(function ($query) use ($date) {
                $query->whereJsonContains('schedule', ['date' => $date])
                    ->orWhereJsonContains('schedule->date', $date);
            })
            ->get();

        $requestedStartTime = strtotime($startTime);
        $requestedEndTime = strtotime($endTime);

        foreach ($existingJobApplications as $jobApp) {
            $scheduleItems = [];

            $scheduleData = $jobApp->schedule;

            if (is_array($scheduleData)) {
                $scheduleItems = $scheduleData;
            } elseif (is_string($scheduleData) && !empty($scheduleData)) {
                try {
                    $decoded = @json_decode($scheduleData, true);
                    $scheduleItems = (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) ? $decoded : [];
                } catch (\Exception $e) {
                    // Log error or handle exception
                    continue;
                }
            }

            foreach ($scheduleItems as $item) {
                if (isset($item['date']) && $item['date'] == $date
                    && isset($item['start_time']) && isset($item['end_time'])) {
                    $appStartTime = strtotime($item['start_time']);
                    $appEndTime = strtotime($item['end_time']);

                    // Check if time slots overlap
                    if ($requestedStartTime < $appEndTime && $requestedEndTime > $appStartTime) {
                        return true; // Found a duplicate job application
                    }
                }
            }
        }

        return false; // No duplicate job application found
    }

    /**
     * Format schedule data for storage.
     *
     * @param array $scheduleData
     *
     * @return string
     */
    public function formatScheduleForStorage($scheduleData)
    {
        $formattedSchedule = [];

        foreach ($scheduleData as $schedule) {
            if (isset($schedule['date']) && isset($schedule['start_time']) && isset($schedule['hours'])) {
                // Calculate end time based on start time and hours
                $startTime = $schedule['start_time'];
                $hours = (int) $schedule['hours'];
                $endTime = date('H:i', strtotime("+{$hours} hours", strtotime($startTime)));

                // Get day name from date
                $dayName = strtolower(date('l', strtotime($schedule['date'])));

                $formattedSchedule[] = [
                    'date' => $schedule['date'],
                    'day' => $dayName,
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'hours' => $hours,
                ];
            }
        }

        return json_encode($formattedSchedule);
    }

    /**
     * Calculate the total cost for a job application.
     *
     * @param string     $calculationType
     * @param float|null $fixedAmount
     * @param float|null $hourlyRate
     * @param int        $totalHours
     * @param bool       $includeTransportation
     * @param float|null $transportationCost
     *
     * @return float
     */
    public function calculateTotalCost($calculationType, $fixedAmount = null, $hourlyRate = null, $totalHours = 0, $includeTransportation = false, $transportationCost = 0)
    {
        $totalCost = 0;

        if ($calculationType === 'fixed' && $fixedAmount !== null) {
            $totalCost = (float) $fixedAmount;
        } elseif ($calculationType === 'hourly' && $hourlyRate !== null && $totalHours > 0) {
            $totalCost = (float) $hourlyRate * $totalHours;
        }

        if ($includeTransportation && $transportationCost > 0) {
            $totalCost += (float) $transportationCost;
        }

        return $totalCost;
    }

    /**
     * Calculate total hours from schedule.
     *
     * @param array|string $schedule
     *
     * @return int
     */
    public function calculateTotalHours($schedule)
    {
        $totalHours = 0;

        if (is_string($schedule) && !empty($schedule)) {
            $decoded = @json_decode($schedule, true);
            $schedule = (json_last_error() === JSON_ERROR_NONE) ? $decoded : [];
        }

        if (is_array($schedule)) {
            foreach ($schedule as $item) {
                if (isset($item['hours'])) {
                    $totalHours += (int) $item['hours'];
                }
            }
        }

        return $totalHours;
    }

    /**
     * Check if a provider is available at a specific time.
     *
     * @param int      $providerId
     * @param string   $date
     * @param string   $startTime
     * @param string   $endTime
     * @param int|null $excludeJobApplicationId
     *
     * @return bool
     */
    public function isProviderAvailable($providerId, $date, $startTime, $endTime, $excludeJobApplicationId = null)
    {
        // Check if the provider has a schedule for this day
        $dayOfWeek = strtolower(date('l', strtotime($date)));
        $providerSchedule = ProviderSchedule::where('provider_id', $providerId)
            ->where('day', $dayOfWeek)
            ->where('is_available', true)
            ->first();

        if (!$providerSchedule) {
            return false; // Provider doesn't work on this day
        }

        // Check if the requested time is within the provider's working hours
        $requestedStartTime = strtotime($startTime);
        $requestedEndTime = strtotime($endTime);
        $providerStartTime = strtotime($providerSchedule->start_time);
        $providerEndTime = strtotime($providerSchedule->end_time);

        if ($requestedStartTime < $providerStartTime || $requestedEndTime > $providerEndTime) {
            return false; // Requested time is outside provider's working hours
        }

        // Check for conflicts with existing orders that are approved or confirmed
        $conflictingOrders = Order::where('provider_id', $providerId)
            ->whereDate('date', $date)
            ->whereIn('status', ['approved', 'confirmed', 'completed'])
            ->where(function ($query) use ($startTime, $endTime) {
                $query->where(function ($q) use ($startTime, $endTime) {
                    $q->where('start_time', '<', $endTime)
                      ->where('end_time', '>', $startTime);
                });
            })
            ->exists();

        if ($conflictingOrders) {
            return false; // Conflicts with existing orders
        }

        // Check for conflicts with existing job applications
        if ($this->hasDuplicateJobApplication($providerId, $date, $startTime, $endTime, $excludeJobApplicationId)) {
            return false; // Conflicts with existing job application
        }

        return true; // Provider is available at the requested time
    }

    /**
     * Create an order from a job application.
     *
     * @param array $scheduleItem
     *
     * @return Order|null
     */
    public function createOrder(JobApplication $jobApplication, $scheduleItem)
    {
        if (!isset($scheduleItem['date']) || !isset($scheduleItem['start_time']) || !isset($scheduleItem['end_time'])) {
            return null;
        }

        // Create a new order
        $order = new Order();
        $order->user_id = $jobApplication->user_id;
        $order->provider_id = $jobApplication->provider_id;
        $order->service_id = $jobApplication->service_id;
        $order->job_application_id = $jobApplication->id;
        $order->location_id = $jobApplication->location_id;
        $order->date = $scheduleItem['date'];
        $order->start_time = $scheduleItem['start_time'];
        $order->end_time = $scheduleItem['end_time'];
        $order->hours = $scheduleItem['hours'] ?? 0;

        // Set cost based on calculation type
        if ($jobApplication->calculation_type === 'fixed') {
            $order->amount = $jobApplication->fixed_amount;

            // Set provider cost based on payment method
            $order->provider_cost = $jobApplication->provider_payment_method === 'fixed'
                ? $jobApplication->provider_fixed_cost
                : $jobApplication->provider_hourly_cost * $order->hours;
        } else {
            // Hourly calculation
            $order->amount = $jobApplication->system_hourly_cost * $order->hours;
            $order->provider_cost = $jobApplication->provider_hourly_cost * $order->hours;
        }

        // Add transportation costs if included
        if ($jobApplication->include_transportation) {
            $order->amount += $jobApplication->system_transportation_cost;
            $order->provider_cost += $jobApplication->provider_transportation_cost;
        }

        $order->status = 'pending';
        $order->save();

        return $order;
    }

    /**
     * Create orders for all schedule items in a job application.
     *
     * @return array
     */
    public function createOrdersFromSchedule(JobApplication $jobApplication)
    {
        $orders = [];
        $scheduleItems = [];

        $scheduleData = $jobApplication->schedule;

        if (is_string($scheduleData) && !empty($scheduleData)) {
            try {
                $decoded = @json_decode($scheduleData, true);
                $scheduleItems = (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) ? $decoded : [];
            } catch (\Exception $e) {
                // Log error or handle exception
                $scheduleItems = [];
            }
        } elseif (is_array($scheduleData)) {
            $scheduleItems = $scheduleData;
        }

        foreach ($scheduleItems as $scheduleItem) {
            $order = $this->createOrder($jobApplication, $scheduleItem);
            if ($order) {
                $orders[] = $order;
            }
        }

        return $orders;
    }

    /**
     * Create a copy of a job application for the next month.
     *
     * @return JobApplication|null
     */
    public function createNextMonthCopy(JobApplication $jobApplication)
    {
        // Only create a copy if this is a recurring job application and it's active
        if (!$jobApplication->is_recurring || !$jobApplication->is_active) {
            return null;
        }

        // If there's an end date or stop date and it's in the past, don't create a copy
        if (($jobApplication->end_date && $jobApplication->end_date->isPast())
            || ($jobApplication->stop_date && $jobApplication->stop_date->isPast())) {
            return null;
        }

        // Create a new job application with the same data
        $newJobApplication = $jobApplication->replicate([
            'created_at',
            'updated_at',
            'deleted_at',
        ]);

        // Update the schedule for the next month if it's date-based
        $scheduleData = $jobApplication->schedule;

        if (is_string($scheduleData) && !empty($scheduleData)) {
            $decoded = @json_decode($scheduleData, true);
            $scheduleData = (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) ? $decoded : [];
        }

        if (is_array($scheduleData)) {
            $newSchedule = [];
            foreach ($scheduleData as $scheduleItem) {
                if (isset($scheduleItem['date'])) {
                    // Parse the date and add one month
                    $date = Carbon::parse($scheduleItem['date'])->addMonth();
                    $scheduleItem['date'] = $date->format('Y-m-d');
                }
                $newSchedule[] = $scheduleItem;
            }
            $newJobApplication->schedule = $newSchedule;
        }

        $newJobApplication->save();

        return $newJobApplication;
    }

    /**
     * Get booked time slots for a provider on a specific day.
     * This method returns time slots that are already booked in other job applications and orders
     * so they can be displayed as unavailable in the edit page.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBookedSlots(Request $request)
    {
        try {
            \Log::info('Get booked slots request received', [
                'request_data' => $request->all(),
            ]);

            $validator = Validator::make($request->all(), [
                'provider_id' => 'required|exists:users,id',
                'day' => 'required|in:saturday,sunday,monday,tuesday,wednesday,thursday,friday',
                'duration' => 'required|integer|min:1|max:12',
                'current_job_application_id' => 'nullable|exists:job_applications,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            $providerId = $request->provider_id;
            $day = $request->day;
            $duration = (int) $request->duration;
            $currentJobApplicationId = $request->current_job_application_id;

            \Log::info('Validated request data', [
                'provider_id' => $providerId,
                'day' => $day,
                'duration' => $duration,
                'current_job_application_id' => $currentJobApplicationId,
            ]);

            // جلب الأوقات المحجوزة من الطلبات الأخرى والوظائف
            $bookedSlots = [];
            $today = Carbon::today();

            // 1. جلب الأوقات المحجوزة من الطلبات
            try {
                $orders = Order::where('provider_id', $providerId)
                    ->whereIn('status', ['approved', 'confirmed'])
                    ->where(function ($query) use ($day) {
                        $query->whereRaw('LOWER(DAYNAME(start_date)) = ?', [$day])
                            ->orWhereRaw('LOWER(DAYNAME(end_date)) = ?', [$day]);
                    })
                    ->where(function ($query) use ($today) {
                        $query->whereDate('start_date', '>=', $today)
                            ->orWhereDate('end_date', '>=', $today);
                    })
                    ->get(['id', 'start_date', 'end_date']);

                \Log::info('Orders found for booked slots', [
                    'provider_id' => $providerId,
                    'day' => $day,
                    'count' => $orders->count(),
                ]);

                // إضافة الحجوزات إلى الفترات المشغولة
                foreach ($orders as $order) {
                    try {
                        $orderStart = Carbon::parse($order->start_date);
                        $orderEnd = Carbon::parse($order->end_date);

                        // تنسيق وقت البداية والنهاية
                        $startTime = $orderStart->format('H:i');
                        $endTime = $orderEnd->format('H:i');

                        $bookedSlots[] = [
                            'start_time' => $startTime,
                            'end_time' => $endTime,
                            'duration' => $orderEnd->diffInHours($orderStart),
                            'type' => 'order',
                        ];
                    } catch (\Exception $e) {
                        \Log::error('Error processing order for booked slots', [
                            'order_id' => $order->id,
                            'error' => $e->getMessage(),
                        ]);
                        continue;
                    }
                }
            } catch (\Exception $e) {
                \Log::error('Error fetching orders for booked slots', [
                    'provider_id' => $providerId,
                    'day' => $day,
                    'error' => $e->getMessage(),
                ]);
            }

            // 2. جلب الأوقات المحجوزة من طلبات التوظيف الأخرى
            try {
                $jobs = JobApplication::where('provider_id', $providerId)
                    ->whereJsonContains('schedule', [['day' => $day]])
                    ->where('is_active', 1)
                    ->when($currentJobApplicationId, function ($query) use ($currentJobApplicationId) {
                        return $query->where('id', '!=', $currentJobApplicationId);
                    })
                    ->get(['id', 'schedule']);

                \Log::info('Job applications found for booked slots', [
                    'provider_id' => $providerId,
                    'day' => $day,
                    'count' => $jobs->count(),
                ]);

                // إضافة الوظائف إلى الفترات المشغولة
                foreach ($jobs as $job) {
                    try {
                        $scheduleData = $job->schedule;

                        // التحقق من أن البيانات صالحة
                        if (is_string($scheduleData)) {
                            $scheduleData = json_decode($scheduleData, true);
                        }

                        if (!is_array($scheduleData)) {
                            \Log::warning('Invalid schedule data for booked slots', [
                                'job_id' => $job->id,
                                'schedule' => $scheduleData,
                            ]);
                            continue;
                        }

                        foreach ($scheduleData as $schedule) {
                            if (isset($schedule['day']) && $schedule['day'] === $day) {
                                // التحقق من وجود ساعات وأنها مصفوفة
                                if (isset($schedule['hours']) && is_array($schedule['hours'])) {
                                    foreach ($schedule['hours'] as $hour) {
                                        if (isset($hour['start_time']) && isset($hour['duration'])) {
                                            $startTime = $hour['start_time'];
                                            $durationHours = (int) $hour['duration'];

                                            // حساب وقت الانتهاء
                                            $startTimeParts = explode(':', $startTime);
                                            $startHour = (int) $startTimeParts[0];
                                            $startMinute = (int) $startTimeParts[1];

                                            $endHour = $startHour + $durationHours;
                                            $endMinute = $startMinute;

                                            // تنسيق وقت الانتهاء
                                            $endTime = sprintf('%02d:%02d', $endHour, $endMinute);

                                            $bookedSlots[] = [
                                                'start_time' => $startTime,
                                                'end_time' => $endTime,
                                                'duration' => $durationHours,
                                                'type' => 'job',
                                            ];
                                        }
                                    }
                                }
                            }
                        }
                    } catch (\Exception $e) {
                        \Log::error('Error processing job application for booked slots', [
                            'job_id' => $job->id,
                            'error' => $e->getMessage(),
                        ]);
                        continue;
                    }
                }
            } catch (\Exception $e) {
                \Log::error('Error fetching job applications for booked slots', [
                    'provider_id' => $providerId,
                    'day' => $day,
                    'error' => $e->getMessage(),
                ]);
            }

            \Log::info('Booked slots retrieved', [
                'provider_id' => $providerId,
                'day' => $day,
                'count' => count($bookedSlots),
                'slots' => $bookedSlots,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Booked slots retrieved successfully',
                'booked_slots' => $bookedSlots,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getBookedSlots', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error getting booked slots: '.$e->getMessage(),
                'booked_slots' => [],
            ]);
        }
    }

    public function change_contract($id)
    {
        $jobApplication = JobApplication::find($id);
        $contract = JobApplicationContracts::where('job_application_id', $id)->latest()->first();

        // تحقق من وجود طلب العمل
        if (!$jobApplication) {
            abort(404);
        }

        // Get all used months for this job application
        $usedMonths = JobApplicationContracts::where('job_application_id', $id)
            ->get()
            ->map(function ($contract) {
                return date('Y-m', strtotime($contract->month));
            })
            ->toArray();

        if (request()->ajax()) {
            return view('pages.job-applications.contract', compact('jobApplication', 'contract', 'usedMonths'))->render();
        }

        return view('pages.job-applications.contract', compact('jobApplication', 'contract', 'usedMonths'));
    }

    /**
     * Store a new contract for a job application.
     *
     * @return \Illuminate\Http\Response
     */
    public function storeContract(Request $request, JobApplication $jobApplication)
    {
        // Format the month value to be compatible with the date field in the database
        // The input is in YYYY-MM format, we need to add a day to make it a valid date
        $monthValue = $request->month ? $request->month.'-01' : date('Y-m-01');

        // Check if the selected month is in the past
        $currentMonth = date('Y-m');
        if ($request->month < $currentMonth) {
            return redirect()->back()->withErrors(['month' => __('message.cannot_select_past_month')])->withInput();
        }

        // Check if a contract already exists for this month
        try {
            $existingContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                ->whereRaw("DATE_FORMAT(month, '%Y-%m') = ?", [date('Y-m', strtotime($monthValue))])
                ->first();
        } catch (\Exception $e) {
            // Log the error but continue with null
            \Log::error('Error checking existing contract: '.$e->getMessage());
            $existingContract = null;
        }

        if ($existingContract) {
            return redirect()->back()->withErrors(['month' => __('message.contract_already_exists_for_month')])->withInput();
        }

        $request->validate([
            'month' => 'required|date_format:Y-m',
            'fixed_amount' => 'required|numeric|min:0',
            'provider_calculation_type' => 'required|in:fixed,hourly',
            'provider_fixed_cost' => 'required_if:provider_calculation_type,fixed|nullable|numeric|min:0',
            'provider_hourly_cost' => 'required_if:provider_calculation_type,hourly|nullable|numeric|min:0',
        ]);

        // Set the appropriate cost based on calculation type
        if ($request->provider_calculation_type === 'fixed') {
            $provider_fixed_cost = $request->provider_fixed_cost;
            $provider_hourly_cost = 0;
        } else {
            $provider_fixed_cost = 0;
            $provider_hourly_cost = $request->provider_hourly_cost;
        }

        // We already have $monthValue from the validation step

        // Create a new contract
        $contract = JobApplicationContracts::create([
            'job_application_id' => $jobApplication->id,
            'fixed_amount' => $request->fixed_amount,
            'provider_calculation_type' => $request->provider_calculation_type,
            'provider_fixed_cost' => $provider_fixed_cost,
            'provider_hourly_cost' => $provider_hourly_cost,
            'month' => $monthValue,
        ]);

        return redirect()->back()->with('success', __('message.contract_saved_successfully'));
    }
}

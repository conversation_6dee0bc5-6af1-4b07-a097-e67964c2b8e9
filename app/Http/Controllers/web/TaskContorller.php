<?php

namespace App\Http\Controllers\web;

use App\Http\Controllers\Controller;
use App\Models\JobApplication;
use App\Models\JobApplicationContracts;
use App\Models\Order;
use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Http\Request;

class TaskContorller extends Controller
{
    /**
     * Save a new task with status 'cancelled'.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveTask(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'provider_id' => 'required|exists:users,id',
                'user_id' => 'required|exists:users,id',
                'service_id' => 'required|exists:services,id',
                'execution_date' => 'required|date',
                'job_application_id' => 'required|exists:job_applications,id',
                'notes' => 'nullable|string',
                'start_time' => 'nullable|string',
                'end_time' => 'nullable|string',
                'task_id' => 'nullable|exists:tasks,id', // معرف المهمة المحددة للتحديث
            ]);

            // Get the job application to copy its data
            $jobApplication = JobApplication::findOrFail($request->job_application_id);

            // Create a new task with status 'cancelled'
            $task = new Task();

            // Data from the report
            $task->provider_id = $request->provider_id;
            $task->user_id = $request->user_id;
            $task->service_id = $request->service_id;
            $task->location_id = $request->location_id;
            $task->execution_date = $request->execution_date;
            $task->job_application_id = $request->job_application_id;
            $task->d_id = $request->d_id;

            // Check if task already exists
            if ($request->has('task_id') && !empty($request->task_id)) {
                // إذا تم توفير معرف المهمة المحددة، ابحث عنها مباشرة
                $existingTask = Task::find($request->task_id);
            } else {
                // البحث بالطريقة العادية
                $existingTask = Task::where('job_application_id', $request->job_application_id)
                    ->where('d_id', $request->d_id)
                    ->where('execution_date', $request->execution_date)
                    ->first();
            }

            if ($existingTask) {
                // Task already exists, update it instead of creating a new one
                if ($request->has('start_time') && !empty($request->start_time)
                    && $request->has('end_time') && !empty($request->end_time)) {
                    // Both start and end times provided - task is completed
                    $existingTask->start_time = $request->start_time;
                    $existingTask->end_time = $request->end_time;

                    // Calculate duration in minutes
                    $startTime = Carbon::parse($request->start_time);
                    $endTime = Carbon::parse($request->end_time);
                    $existingTask->duration_minutes = (int) $startTime->diffInMinutes($endTime);
                    // Set status to ended (completed)
                    $existingTask->status = 'ended';

                    // Log the status update
                    \Log::info('Task status updated to ended', [
                        'task_id' => $existingTask->id,
                        'status' => $existingTask->status
                    ]);

                    // Save the existing task
                    $existingTask->save();

                    return response()->json([
                        'success' => true,
                        'message' => 'تم تحديث الأوقات بنجاح',
                        'task' => $existingTask,
                    ]);
                } elseif ($request->has('start_time') && !empty($request->start_time)) {
                    // Only start time provided - task is started but not completed
                    $existingTask->start_time = $request->start_time;
                    $existingTask->end_time = null;
                    $existingTask->duration_minutes = null;

                    // Set status to started
                    $existingTask->status = 'started';

                    // Log the status update
                    \Log::info('Task status updated to started', [
                        'task_id' => $existingTask->id,
                        'status' => $existingTask->status
                    ]);

                    // Save the existing task
                    $existingTask->save();

                    return response()->json([
                        'success' => true,
                        'message' => 'تم تحديث وقت البداية بنجاح',
                        'task' => $existingTask,
                    ]);
                } elseif (!$request->has('start_time') && !$request->has('end_time')) {
                    // No times provided, set status to cancelled
                    $existingTask->status = 'cancelled';

                    // Save cancellation notes if provided
                    if ($request->has('notes') && !empty($request->notes)) {
                        $existingTask->notes = 'سبب الإلغاء: '.$request->notes;
                    }

                    // Save the existing task
                    $existingTask->save();

                    return response()->json([
                        'success' => true,
                        'message' => 'تم الإلغاء بنجاح',
                        'task' => $existingTask,
                    ]);
                }
            }

            // For new tasks
            if ($request->has('start_time') && !empty($request->start_time)
                && $request->has('end_time') && !empty($request->end_time)) {
                // Both start and end times provided - task is completed

                $task->start_time = $request->start_time;
                $task->end_time = $request->end_time;

                // Calculate duration in minutes
                // Parse times as 24-hour format
                $startTime = Carbon::createFromFormat('H:i', $request->start_time);
                $endTime = Carbon::createFromFormat('H:i', $request->end_time);

                // Handle overnight shifts (when end time is next day)
                if ($endTime->lt($startTime)) {
                    $endTime->addDay();
                }

                $task->duration_minutes = (int) $startTime->diffInMinutes($endTime);

                // Set status to ended (completed)
                $task->status = 'ended';

                // Log the status update
                \Log::info('New task status set to ended', [
                    'job_application_id' => $task->job_application_id,
                    'status' => $task->status
                ]);
            } elseif ($request->has('start_time') && !empty($request->start_time)) {
                // Only start time provided - task is started but not completed

                $task->start_time = $request->start_time;
                $task->end_time = null;
                $task->duration_minutes = null;

                // Set status to started
                $task->status = 'started';

                // Log the status update
                \Log::info('New task status set to started', [
                    'job_application_id' => $task->job_application_id,
                    'status' => $task->status
                ]);
            } else {
                // No times provided, set status to cancelled
                $task->status = 'cancelled';

                // Log the status update
                \Log::info('New task status set to cancelled', [
                    'job_application_id' => $task->job_application_id,
                    'status' => $task->status
                ]);
            }

            // Save cancellation notes if provided
            if ($request->has('notes') && !empty($request->notes)) {
                $task->notes = 'سبب الإلغاء: '.$request->notes;
            }
            // Copy common data from job application
            $task->calculation_type = $jobApplication->calculation_type;
            $task->provider_payment_method = $jobApplication->provider_payment_method;
            $task->include_transportation = $jobApplication->include_transportation;
            $task->system_transportation_cost = $jobApplication->system_transportation_cost;
            $task->provider_transportation_cost = $jobApplication->provider_transportation_cost;
            $task->service_id = $jobApplication->service_id;
            $task->location_id = $jobApplication->location_id;

            // Explicitly set financial close fields to false for new tasks
            $task->is_financially_closed_by_user = false;
            $task->is_financially_closed_by_provider = false;
            $task->financially_closed_by_user = false;
            $task->financially_closed_by_provider = false;

            if ($jobApplication->calculation_type == 'hourly') {
                // Copy hourly-specific data from job application (job_applications table)
                $task->fixed_amount = $jobApplication->fixed_amount;
                $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;
                $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                $task->system_hourly_cost = $jobApplication->system_hourly_cost;

                // Save the task
                $task->save();

                return response()->json([
                    'success' => true,
                    'message' => 'تم الإضافة بنجاح',
                    'task' => $task,
                ]);
            } else {
                // Fixed calculation type logic - get data from job_application_contracts table
                // Get the current month and year
                $currentDate = Carbon::now();
                $currentMonth = $currentDate->format('m'); // Just the month number (01-12)
                $currentYear = $currentDate->format('Y');  // Just the year (e.g., 2023)
                $currentYearMonth = $currentDate->format('Y-m'); // Year and month (e.g., 2023-05)

                // Get the latest contract for this job application from job_application_contracts table
                $latestContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                    ->orderBy('month', 'desc')
                    ->first();

                if ($latestContract) {
                    // Get the month and year from the latest contract
                    $contractDate = Carbon::parse($latestContract->month);
                    $contractMonth = $contractDate->format('m');
                    $contractYear = $contractDate->format('Y');
                    $contractYearMonth = $contractDate->format('Y-m');

                    // Compare current month with contract month
                    if ($currentMonth == $contractMonth && $currentYear == $contractYear) {
                        // Current month equals latest contract month - use this contract
                        $task->fixed_amount = $latestContract->fixed_amount;
                        $task->provider_fixed_cost = $latestContract->provider_fixed_cost;
                        $task->provider_hourly_cost = $latestContract->provider_hourly_cost;
                    } elseif ($contractYearMonth > $currentYearMonth) {
                        // Contract month is in the future - find the latest contract that doesn't exceed current month
                        $appropriateContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                            ->where('month', '<=', $currentDate->format('Y-m-d'))
                            ->orderBy('month', 'desc')
                            ->first();

                        if ($appropriateContract) {
                            // Use the appropriate contract that doesn't exceed current month
                            $task->fixed_amount = $appropriateContract->fixed_amount;
                            $task->provider_fixed_cost = $appropriateContract->provider_fixed_cost;
                            $task->provider_hourly_cost = $appropriateContract->provider_hourly_cost;
                        } else {
                            // If no appropriate contract found, use the job application data
                            $task->fixed_amount = $jobApplication->fixed_amount;
                            $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                            $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;
                        }
                    } else {
                        // Current month is newer than contract month - use latest contract that doesn't exceed current month
                        $appropriateContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                            ->where('month', '<=', $currentDate->format('Y-m-d'))
                            ->orderBy('month', 'desc')
                            ->first();

                        if ($appropriateContract) {
                            // Use the appropriate contract
                            $task->fixed_amount = $appropriateContract->fixed_amount;
                            $task->provider_fixed_cost = $appropriateContract->provider_fixed_cost;
                            $task->provider_hourly_cost = $appropriateContract->provider_hourly_cost;
                        } else {
                            // If no appropriate contract found, use the job application data
                            $task->fixed_amount = $jobApplication->fixed_amount;
                            $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                            $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;
                        }
                    }
                } else {
                    // If no contract exists, use the job application data
                    $task->fixed_amount = $jobApplication->fixed_amount;
                    $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                    $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;
                }

                // Save the task
                $task->save();

                return response()->json([
                    'success' => true,
                    'message' => 'تم الإضافة بنجاح',
                    'task' => $task,
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel an existing task.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelTask($id)
    {
        try {
            $task = Task::findOrFail($id);
            $task->status = 'cancelled';
            $task->save();

            return response()->json([
                'success' => true,
                'message' => 'تم الإلغاء بنجاح',
                'task' => $task,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update task start and end times.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateTaskTime(Request $request, $id)
    {
        try {
            // Validate the request
            $request->validate([
                'start_time' => 'required|string',
                'end_time' => 'nullable|string', // End time is now optional
            ]);

            // Find the task
            $task = Task::findOrFail($id);

            // Get the job application to check calculation type
            $jobApplication = JobApplication::findOrFail($task->job_application_id);

            // Update the task times
            $task->start_time = $request->start_time;

            // Check if end_time is provided
            if ($request->has('end_time') && !empty($request->end_time)) {
                $task->end_time = $request->end_time;

                // Calculate duration in minutes
                // Parse times as 24-hour format
                $startTime = Carbon::createFromFormat('H:i', $request->start_time);
                $endTime = Carbon::createFromFormat('H:i', $request->end_time);

                // Handle overnight shifts (when end time is next day)
                if ($endTime->lt($startTime)) {
                    $endTime->addDay();
                }

                $task->duration_minutes = (int) $startTime->diffInMinutes($endTime);

                // Update status to completed
                $task->status = 'ended';

                // Log the status update
                \Log::info('Task status updated to ended', [
                    'task_id' => $task->id,
                    'status' => $task->status
                ]);
            } else {
                // If end_time is not provided, set it to null
                $task->end_time = null;
                $task->duration_minutes = null;

                // Update status to started
                $task->status = 'started';

                // Log the status update
                \Log::info('Task status updated to started', [
                    'task_id' => $task->id,
                    'status' => $task->status
                ]);
            }

            // If this is a fixed calculation type, update the contract data from job_application_contracts table
            if ($jobApplication->calculation_type == 'fixed') {
                // Get the current month and year
                $currentDate = Carbon::now();
                $currentMonth = $currentDate->format('m'); // Just the month number (01-12)
                $currentYear = $currentDate->format('Y');  // Just the year (e.g., 2023)
                $currentYearMonth = $currentDate->format('Y-m'); // Year and month (e.g., 2023-05)

                // Get the latest contract for this job application from job_application_contracts table
                $latestContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                    ->orderBy('month', 'desc')
                    ->first();

                if ($latestContract) {
                    // Get the month and year from the latest contract
                    $contractDate = Carbon::parse($latestContract->month);
                    $contractMonth = $contractDate->format('m');
                    $contractYear = $contractDate->format('Y');
                    $contractYearMonth = $contractDate->format('Y-m');

                    // Compare current month with contract month
                    if ($currentMonth == $contractMonth && $currentYear == $contractYear) {
                        // Current month equals latest contract month - use this contract
                        $task->fixed_amount = $latestContract->fixed_amount;
                        // Only update provider_payment_method if it's not already set
                        if (!$task->provider_payment_method) {
                            $task->provider_payment_method = $latestContract->provider_payment_method;
                        }
                        if($task->provider_payment_method == 'fixed'){
                            $task->provider_fixed_cost = $latestContract->provider_fixed_cost;
                        }else{
                        $task->provider_hourly_cost = $latestContract->provider_hourly_cost;

                        }

                    } elseif ($contractYearMonth > $currentYearMonth) {
                        // Contract month is in the future - find the latest contract that doesn't exceed current month
                        $appropriateContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                            ->where('month', '<=', $currentDate->format('Y-m-d'))
                            ->orderBy('month', 'desc')
                            ->first();

                        if ($appropriateContract) {
                            // Use the appropriate contract that doesn't exceed current month
                            $task->fixed_amount = $appropriateContract->fixed_amount;
                            // Only update provider_payment_method if it's not already set
                            if (!$task->provider_payment_method) {
                                $task->provider_payment_method = $appropriateContract->provider_payment_method;
                            }
                            if($task->provider_payment_method == 'fixed'){
                                $task->provider_fixed_cost = $appropriateContract->provider_fixed_cost;
                            }else{
                            $task->provider_hourly_cost = $appropriateContract->provider_hourly_cost;

                            }

                        } else {
                            // If no appropriate contract found, use the job application data
                            $task->fixed_amount = $jobApplication->fixed_amount;
                            // Only update provider_payment_method if it's not already set
                            if (!$task->provider_payment_method) {
                                $task->provider_payment_method = $jobApplication->provider_payment_method;
                            }
                            if($task->provider_payment_method == 'fixed'){
                                $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                            }else{
                            $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;

                            }

                        }
                    } else {
                        // Current month is newer than contract month - use latest contract that doesn't exceed current month
                        $appropriateContract = JobApplicationContracts::where('job_application_id', $jobApplication->id)
                            ->where('month', '<=', $currentDate->format('Y-m-d'))
                            ->orderBy('month', 'desc')
                            ->first();

                        if ($appropriateContract) {
                            // Use the appropriate contract
                            $task->fixed_amount = $appropriateContract->fixed_amount;
                            // Only update provider_payment_method if it's not already set
                            if (!$task->provider_payment_method) {
                                $task->provider_payment_method = $appropriateContract->provider_payment_method;
                            }
                            if($task->provider_payment_method == 'fixed'){
                                $task->provider_fixed_cost = $appropriateContract->provider_fixed_cost;
                            }else{
                            $task->provider_hourly_cost = $appropriateContract->provider_hourly_cost;

                            }

                        } else {
                            // If no appropriate contract found, use the job application data
                            $task->fixed_amount = $jobApplication->fixed_amount;
                            // Only update provider_payment_method if it's not already set
                            if (!$task->provider_payment_method) {
                                $task->provider_payment_method = $jobApplication->provider_payment_method;
                            }
                            if($task->provider_payment_method == 'fixed'){
                                $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                            }else{
                            $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;

                            }

                        }
                    }
                } else {
                    // If no contract exists, use the job application data
                    $task->fixed_amount = $jobApplication->fixed_amount;
                    // Only update provider_payment_method if it's not already set
                    if (!$task->provider_payment_method) {
                        $task->provider_payment_method = $jobApplication->provider_payment_method;
                    }
                    if($task->provider_payment_method == 'fixed'){
                        $task->provider_fixed_cost = $jobApplication->provider_fixed_cost;
                    }else{
                    $task->provider_hourly_cost = $jobApplication->provider_hourly_cost;

                    }

                }
            }

            // Save the task
            $task->save();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الأوقات بنجاح',
                'task' => $task,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getMonthlyCalendar(Request $request)
    {
        $user = auth()->user();
        // $providerId = $user->id;
        // $user = $request->provider;provider_id
        // $providerId = $user;
        $providerId = $request->provider_id;
        $month = $request->input('month'); // format: YYYY-MM
        $day = $request->input('day'); // Optional: specific day filter

        if (!$month) {
            return response()->json(['success' => false, 'message' => 'Month is required (YYYY-MM)'], 422);
        }

        // If day is specified, filter for that specific day only
        if ($day) {
            $dayFormatted = str_pad($day, 2, '0', STR_PAD_LEFT);
            $startDate = Carbon::parse($month.'-'.$dayFormatted)->startOfDay();
            $endDate = Carbon::parse($month.'-'.$dayFormatted)->endOfDay();
        } else {
            // Otherwise, get the entire month
            $startDate = Carbon::parse($month.'-01')->startOfMonth();
            $endDate = (clone $startDate)->endOfMonth();
        }

        // 1. جلب كل job_applications المتكررة لهذا المزود
        $jobApps = JobApplication::where('provider_id', $providerId)
            ->where('is_active', true)
            // ->where('is_recurring', true)
            ->get();

        // 2. جلب كل المهام المنفذة لهذا المزود في هذا الشهر
        $tasks = Task::where('provider_id', $providerId)
            ->whereBetween('execution_date', [$startDate->toDateString(), $endDate->toDateString()])
            ->get();

        // 3. جلب كل الحجوزات (orders) لهذا المزود في هذا الشهر
        $orders = Order::where('provider_id', $providerId)
            ->whereBetween('start_date', [$startDate->toDateString(), $endDate->toDateString()])
            ->get();

        $calendar = [];
        // 4. توليد كل المهام المتكررة (job_applications) لكل يوم وفترة زمنية
        foreach ($jobApps as $jobApp) {
            $createdAt = $jobApp->created_at->copy()->startOfDay();
            $schedule = $jobApp->schedule ?? [];
            foreach ($schedule as $item) {
                $dayName = strtolower($item['day']);
                foreach ($item['hours'] as $hour) {
                    // لكل يوم في الشهر يطابق اليوم المطلوب
                    $dId = $hour['d_id'] ?? null;
                    $startTime = $hour['start_time'];
                    $endTime = Carbon::parse($startTime)->addHours((int) $hour['duration'])->format('H:i');
                    $period = \Carbon\CarbonPeriod::create($startDate, $endDate);
                    foreach ($period as $date) {
                        if ($date->lt($createdAt)) {
                            continue;
                        } // لا نولد قبل تاريخ الإنشاء
                        if ($date->format('l') === ucfirst($dayName)) {
                            // تحقق هل هناك task منفذ
                            $task = $tasks->first(function ($t) use ($jobApp, $dId, $date) {
                                return $t->job_application_id == $jobApp->id && $t->d_id == $dId && $t->execution_date == $date->toDateString();
                            });
                            $status = $task ? 'تم التنفيذ' : 'لم تُنفذ';
                            $calendar[] = [
                                'type' => 'job_application',
                                'job_application_id' => $jobApp->id,
                                'd_id' => $dId,
                                'date' => $date->toDateString(),
                                'day' => $dayName,
                                'start_time' => $startTime,
                                'end_time' => $endTime,
                                'status' => $status,
                                'task' => $task ? [
                                    'id' => $task->id,
                                    'start_time' => $task->start_time,
                                    'end_time' => $task->end_time,
                                ] : null,
                            ];
                        }
                    }
                }
            }
        }
        // 5. أضف الحجوزات من orders
        foreach ($orders as $order) {
            $calendar[] = [
                'type' => 'order',
                'order_id' => $order->id,
                'date' => $order->start_date,
                'day' => strtolower(Carbon::parse($order->start_date)->format('l')),
                'start_time' => $order->start_time ?? $order->start_date,
                'end_time' => $order->end_time ?? $order->end_date,
                'status' => 'حجز (order)',
            ];
        }
        // ترتيب النتائج حسب التاريخ والوقت
        usort($calendar, function ($a, $b) {
            $cmp = strcmp($a['date'], $b['date']);
            if ($cmp === 0) {
                return strcmp($a['start_time'], $b['start_time']);
            }

            return $cmp;
        });

        return response()->json([
            'success' => true,
            'month' => $month,
            'day' => $day,
            'data' => $calendar,
        ]);
    }

    /**
     * Check hours usage for fixed calculation type tasks
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkHours(Request $request)
    {
        try {
            $jobApplicationId = $request->job_application_id;
            $currentTaskId = $request->current_task_id;
            $newTaskHours = $request->new_task_hours;
            $executionDate = $request->execution_date;

            // Get the job application
            $jobApplication = JobApplication::find($jobApplicationId);
            if (!$jobApplication) {
                return response()->json([
                    'success' => false,
                    'message' => 'Job application not found'
                ]);
            }

            // Get total allowed hours
            $totalAllowedHours = $jobApplication->total_hours ?? 0;

            // Get the month and year from execution date
            $executionMonth = date('Y-m', strtotime($executionDate));

            // Calculate used hours in the current month (excluding current task if updating)
            $usedHours = Task::where('job_application_id', $jobApplicationId)
                ->whereRaw("DATE_FORMAT(execution_date, '%Y-%m') = ?", [$executionMonth])
                ->where('status', '!=', 'cancelled')
                ->when($currentTaskId, function($query) use ($currentTaskId) {
                    return $query->where('id', '!=', $currentTaskId);
                })
                ->sum('duration_minutes') / 60; // Convert minutes to hours

            // Check if transportation has been paid for this job application in this month
            $transportationPaid = $this->checkTransportationPaid($jobApplicationId, $executionMonth);

            return response()->json([
                'success' => true,
                'total_allowed_hours' => $totalAllowedHours,
                'used_hours' => $usedHours,
                'execution_month' => $executionMonth,
                'transportation_paid' => $transportationPaid
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if transportation has been paid for a job application in a specific month
     *
     * @param int $jobApplicationId
     * @param string $executionMonth (Y-m format)
     * @return array
     */
    private function checkTransportationPaid($jobApplicationId, $executionMonth)
    {
        // Check if there are any financially closed tasks for this job application in this month
        $closedTask = Task::where('job_application_id', $jobApplicationId)
            ->whereRaw("DATE_FORMAT(execution_date, '%Y-%m') = ?", [$executionMonth])
            ->where(function($query) {
                $query->where('is_financially_closed_by_user', 1)
                      ->orWhere('is_financially_closed_by_provider', 1);
            })
            ->first();

        if ($closedTask) {
            $paidBy = [];
            if ($closedTask->is_financially_closed_by_user) {
                $paidBy[] = 'customer';
            }
            if ($closedTask->is_financially_closed_by_provider) {
                $paidBy[] = 'provider';
            }

            return [
                'paid' => true,
                'paid_by' => $paidBy,
                'customer_transportation' => $closedTask->system_transportation_cost ?? 0,
                'provider_transportation' => $closedTask->provider_transportation_cost ?? 0
            ];
        }

        return [
            'paid' => false,
            'paid_by' => [],
            'customer_transportation' => 0,
            'provider_transportation' => 0
        ];
    }
}

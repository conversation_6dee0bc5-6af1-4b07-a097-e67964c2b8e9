<?php

namespace App\Http\Controllers\web;

use App\Models\Task;
use App\Models\Order;
use App\Models\PaymentTransaction;
use App\Http\Controllers\Controller;
use App\Events\Orders\OrderConfirmed;
use App\Events\Orders\OrderTotalPricePaid;

class PaymentController extends Controller
{
    public function index($id)
    {
        $transaction = PaymentTransaction::where('confirmed', 0)->where('id', $id)->first();
        if (!$transaction) {
            return redirect('Failed-Payment/'.$id);
        }

        return view('payments.payment', compact(['transaction']));
    }

    public function successfulPayment($id)
    {
        $transaction = PaymentTransaction::where('confirmed', 0)->where('id', $id)->first();
        if (!$transaction) {
            return redirect('Failed-Payment/'.$id);
        }
        $transaction->confirmed = 1;
        $transaction->save();

        if ($transaction->order_id != null) {
            // new for multi order payment
            if ($transaction->order?->group != null) {
                $multiTransaction = PaymentTransaction::whereHas('order', function ($query) use ($transaction) {
                    $query->where('orders.group', $transaction->order->group);
                })->orderBy('id', 'asc')->get();
                foreach ($multiTransaction as $index => $singleTransaction) {
                    $singleTransaction->confirmed = 1;
                    $singleTransaction->save();
                    if ($singleTransaction->transaction_for == 'deposit') {
                        $order = Order::where('status', 'approved')->where('id', $singleTransaction->order_id)->first();

                        $order->status = 'confirmed';
                        $order->payment_left = $order->total_payment - $singleTransaction->amount;
                        $order->save();
                    // event(new OrderConfirmed($order));
                    } elseif ($singleTransaction->transaction_for == 'total_amount') {
                        $order = Order::where('id', $singleTransaction->order_id)->first();
                        // event(new OrderTotalPricePaid($order));
                    }
                }
            } else {
                if ($transaction->transaction_for == 'deposit') {
                    $order = Order::where('status', 'approved')->where('id', $transaction->order_id)->first();
                    if (!$order) {
                        return redirect('Failed-Payment/'.$id);
                    }
                    $order->status = 'confirmed';
                    $order->payment_left = $order->total_payment - $transaction->amount;
                    $order->save();
                // event(new OrderConfirmed($order));
                } elseif ($transaction->transaction_for == 'total_amount') {
                    $order = Order::where('id', $transaction->order_id)->first();
                    // event(new OrderTotalPricePaid($order));
                }
            }
        }
        if ($transaction->task_id != null) {
            // Task payment processing
            if ($transaction->transaction_for == 'deposit') {
                $task = Task::where('status', 'ended')->where('is_financially_closed_by_user', 0)->where('id', $transaction->task_id)->first();
                // if (!$task) {
                //     return redirect('Failed-Payment/' . $id);
                // }
                $task->is_financially_closed_by_user = 1;
                // $task->payment_left = $task->total_payment - $transaction->amount;
                $task->save();
                //event(new TaskConfirmed($task));
            } else if ($transaction->transaction_for == 'total_amount') {
                $task = Task::where('id', $transaction->task_id)->first();
                // event(new TaskTotalPricePaid($task));
            }
        }
        return view('pages.payments.success-payment');
    }

    public function failedPayment($id)
    {
        $transaction = PaymentTransaction::where('confirmed', 0)->where('id', $id)->first();
        if ($transaction !== null) {
            $transaction->delete();
        }

        return view('pages.payments.failed-payment');
    }
}

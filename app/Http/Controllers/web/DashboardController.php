<?php

namespace App\Http\Controllers\web;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\Order;
use App\Models\ProviderInvoice;
use App\Models\Service;
use App\Models\Task;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $admins = Admin::count();
        $users = User::count();
        $clients = User::whereDoesntHave('providerInfo')->count();
        $providers = User::where('type', 'provider')->whereHas('providerInfo')->count();
        $companies = User::where('type', 'company')->whereHas('providerInfo')->count();
        $pending_providers = User::whereHas('providerInfo', function ($query) {
            $query->where('is_approved', 0);
        })->count();
        $services = Service::count();
        $orders = Order::count();
        $completed_orders = Order::where('status', 'completed')->count();
        $confirmed_orders = Order::where('status', 'confirmed')->count();
        $user_invoices = Order::where('status', 'completed')->where('invoice_request', 1)->whereNull('invoice')->count();
        $invoices = ProviderInvoice::where('status', 'pending')->with(['provider'])->count();
        $reports = [];
        $reports[] = ['name' => __('message.Admins'), 'value' => $admins, 'bg' => 'bg-primary', 'icon' => '<i class="fas fa-user-tie text-white h3"></i>'];
        $reports[] = ['name' => __('message.Users'), 'value' => $users, 'bg' => 'bg-dark',  'icon' => '<i class="fas fa-user text-white h3"></i>'];
        $reports[] = ['name' => __('message.Clients'), 'value' => $clients, 'bg' => 'bg-primary',  'icon' => '<i class="fas fa-user text-white h3"></i>'];
        $reports[] = ['name' => __('message.Providers'), 'value' => $providers, 'bg' => 'bg-dark',  'icon' => '<i class="fas fa-user text-white h3"></i>'];
        $reports[] = ['name' => __('message.Companies'), 'value' => $companies, 'bg' => 'bg-primary',  'icon' => '<i class="fas fa-user text-white h3"></i>'];
        $reports[] = ['name' => __('message.Pending Providers'), 'value' => $pending_providers, 'bg' => 'bg-dark',  'icon' => '<i class="fas fa-check-circle text-white h3"></i>'];
        $reports[] = ['name' => __('message.Services'), 'value' => $services, 'bg' => 'bg-primary',  'icon' => '<i class="fas fa-list text-white h3"></i>'];
        $reports[] = ['name' => __('message.Orders'), 'value' => $orders, 'bg' => 'bg-dark',  'icon' => '<i class="fas fa-list text-white h3"></i>'];
        $reports[] = ['name' => __('message.Completed Orders'), 'value' => $completed_orders, 'bg' => 'bg-primary',  'icon' => '<i class="fas fa-list text-white h3"></i>'];
        $reports[] = ['name' => __('message.Confirmed Orders'), 'value' => $confirmed_orders, 'bg' => 'bg-dark',  'icon' => '<i class="fas fa-list text-white h3"></i>'];
        if ($user_invoices > 0) {
            $reports[] = ['name' => 'User Invoices', 'value' => $user_invoices, 'bg' => 'bg-danger',  'icon' => '<i class="fas fa-file-invoice text-white h3"></i>'];
        }
        if ($invoices > 0) {
            $reports[] = ['name' => 'Provider Invoices', 'value' => $invoices, 'bg' => 'bg-danger',  'icon' => '<i class="fas fa-file-invoice text-white h3"></i>'];
        }

        // Get employee providers for the filter dropdown
        $employeeProviders = User::employeeProviders()
            ->where('is_verified', 1)
            ->where('is_blocked', 0)
            ->with('providerInfo') // Load provider info for each user
            ->get();

        // Get the filter parameters with defaults
        $providerId = $request->input('provider_id', $employeeProviders->first()->id ?? null);
        $day = $request->input('day', date('j')); // Default to current day

        // Format month parameter as YYYY-MM
        $month = $request->input('month', date('m')); // Default to current month
        $year = $request->input('year', date('Y')); // Default to current year

        if (!$month) {
            $month = date('m');
        }

        // Create the month parameter in YYYY-MM format
        $monthParam = $year.'-'.str_pad($month, 2, '0', STR_PAD_LEFT);

        // Create a new request with the formatted month parameter
        $calendarRequest = new Request($request->all());
        $calendarRequest->merge(['month' => $monthParam]);

        // Generate date options for the dropdowns
        $days = range(1, 31);
        $months = [];
        for ($i = 1; $i <= 12; ++$i) {
            $months[$i] = date('F', mktime(0, 0, 0, $i, 1));
        }
        $currentYear = date('Y');
        $years = range($currentYear, 2040);

        // Get tasks with calendar view
        $tasks = $this->getEmployeeTasksCalendar($calendarRequest);

        // Handle the case where the function returns a JSON response (error case)
        if ($tasks instanceof \Illuminate\Http\JsonResponse) {
            // Default to current month if there was an error
            $calendarRequest->merge(['month' => date('Y-m')]);
            $tasks = $this->getEmployeeTasksCalendar($calendarRequest);
        }

        return view('pages.dashboard', compact([
            'reports',
            'employeeProviders',
            'tasks',
            'providerId',
            'day',
            'month',
            'year',
            'days',
            'months',
            'years',
        ]));
    }

    /**
     * Get tasks for employee service providers with calendar view
     * This function is based on the logic from getMonthlyCalendar in TaskContorller.
     */
    private function getEmployeeTasksCalendar(Request $request)
    {
        $user = auth()->user();
        $providerId = $request->provider_id;
        $month = $request->input('month'); // format: YYYY-MM
        $day = $request->input('day'); // Optional: specific day filter

        if (!$month) {
            return response()->json(['success' => false, 'message' => 'Month is required (YYYY-MM)'], 422);
        }

        // If day is specified, filter for that specific day only
        if ($day) {
            $dayFormatted = str_pad($day, 2, '0', STR_PAD_LEFT);
            $startDate = Carbon::parse($month.'-'.$dayFormatted)->startOfDay();
            $endDate = Carbon::parse($month.'-'.$dayFormatted)->endOfDay();
        } else {
            // Otherwise, get the entire month
            $startDate = Carbon::parse($month.'-01')->startOfMonth();
            $endDate = (clone $startDate)->endOfMonth();
        }

        // 1. جلب كل job_applications المتكررة لهذا المزود
        $jobApps = \App\Models\JobApplication::where('provider_id', $providerId)
            ->where('is_active', true)
            ->get();

        // 2. جلب كل المهام المنفذة لهذا المزود
        // نوسع نطاق البحث ليشمل المهام المحدثة من API بتواريخ مختلفة
        // لكن نحدد فترة معقولة لتحسين الأداء (آخر 3 أشهر)
        $threeMonthsAgo = Carbon::now()->subMonths(3)->startOfMonth();
        $tasks = Task::where('provider_id', $providerId)
            ->where('execution_date', '>=', $threeMonthsAgo->toDateString())
            ->get();

        $calendar = [];
        // 3. توليد كل المهام المتكررة (job_applications) لكل يوم وفترة زمنية
        foreach ($jobApps as $jobApp) {
            $createdAt = $jobApp->created_at->copy()->startOfDay();
            $schedule = $jobApp->schedule ?? [];
            foreach ($schedule as $item) {
                $dayName = strtolower($item['day']);
                foreach ($item['hours'] as $hour) {
                    $dId = $hour['d_id'] ?? null;
                    $startTime = $hour['start_time'];
                    $endTime = Carbon::parse($startTime)->addHours((int) $hour['duration'])->format('H:i');
                    $period = \Carbon\CarbonPeriod::create($startDate, $endDate);
                    foreach ($period as $date) {
                        if ($date->lt($createdAt)) {
                            continue;
                        }
                        if ($date->format('l') === ucfirst($dayName)) {
                            // البحث عن المهمة بناءً على job_application_id و d_id والتاريخ المحدد
                            // هذا يضمن أن التحديثات تؤثر فقط على المهمة المحددة بالتاريخ
                            $matchingTasks = $tasks->filter(function ($t) use ($jobApp, $dId, $date) {
                                return $t->job_application_id == $jobApp->id && 
                                      $t->d_id == $dId && 
                                      $t->execution_date == $date->toDateString();
                            });
                            $task = $matchingTasks->sortByDesc('updated_at')->first();

                            // تحديد الحالة بناءً على وجود التاسك وحالته
                            if ($task) {
                                if ($task->status === 'cancelled') {
                                    $status = 'تم الالغاء';
                                } elseif ($task->status === 'ended' || ($task->start_time && $task->end_time)) {
                                    $status = 'تم التنفيذ';
                                } elseif ($task->status === 'started' || ($task->start_time && !$task->end_time)) {
                                    $status = 'غير مكتمل';
                                } else {
                                    $status = 'لم تُنفذ';
                                }
                            } else {
                                $status = 'لم تُنفذ';
                            }

                            // تنسيق الوقت وعرض "لم يتم" إذا لم يكن هناك وقت في التاسك
                            $formattedStartTime = ($task && $task->start_time) ? $this->formatTimeToArabic($task->start_time) : 'لم يتم';
                            $formattedEndTime = ($task && $task->end_time) ? $this->formatTimeToArabic($task->end_time) : 'لم يتم';

                            $calendar[] = [
                                'type' => 'job_application',
                                'job_application_id' => $jobApp->id,
                                'd_id' => $dId,
                                'date' => $date->toDateString(),
                                'day' => $dayName,
                                'provider' => $jobApp->provider,
                                'user' => $jobApp->user,
                                'service' => $jobApp->service,
                                'location' => $jobApp->location,
                                'start_time' => $formattedStartTime,
                                'end_time' => $formattedEndTime,
                                'display_status' => $status,
                                'task_id' => $task ? $task->id : null,
                                'task' => $task ? [
                                    'id' => $task->id,
                                    'start_time' => $task->start_time,
                                    'end_time' => $task->end_time,
                                    'status' => $task->status,
                                ] : null,
                            ];
                        }
                    }
                }
            }
        }
        // لا تضف الحجوزات من orders (استثناء orders)
        // ترتيب النتائج حسب التاريخ والوقت
        usort($calendar, function ($a, $b) {
            $cmp = strcmp($a['date'], $b['date']);
            if ($cmp === 0) {
                return strcmp($a['start_time'], $b['start_time']);
            }

            return $cmp;
        });

        // Paginate the results manually
        $perPage = 10;
        $currentPage = $request->input('page', 1);
        $items = array_slice($calendar, ($currentPage - 1) * $perPage, $perPage);

        // Create paginator with all query parameters preserved
        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            count($calendar),
            $perPage,
            $currentPage,
            [
                'path' => $request->url(),
                'query' => $request->all(),
            ]
        );

        return $paginator;
    }

    /**
     * Get tasks for employee service providers with pagination.
     */
    private function getEmployeeTasksWithPagination(Request $request)
    {
        // Get the filter parameters
        $providerId = $request->input('provider_id');
        $timeFilter = $request->input('time_filter', 'day'); // Default to day

        // Get the date range based on the time filter
        $now = Carbon::now();
        $startDate = null;
        $endDate = null;

        switch ($timeFilter) {
            case 'day':
                $startDate = $now->copy()->startOfDay();
                $endDate = $now->copy()->endOfDay();
                break;
            case 'month':
                $startDate = $now->copy()->startOfMonth();
                $endDate = $now->copy()->endOfMonth();
                break;
            case 'year':
                $startDate = $now->copy()->startOfYear();
                $endDate = $now->copy()->endOfYear();
                break;
        }

        // Base query for tasks from employee providers
        $query = Task::query()
            ->whereHas('provider', function ($query) {
                $query->whereIn('type', ['provider', 'company'])
                    ->where('is_verified', 1)
                    ->where('is_blocked', 0)
                    ->whereHas('providerInfo', function ($query) {
                        $query->where('is_employee', true);
                    });
            })
            ->whereHas('jobApplication', function ($query) {
                $query->where('is_active', true);
            })
            ->with([
                'provider.providerInfo',
                'user',
                'jobApplication.service',
                'jobApplication.location',
            ]);

        // Filter by provider if specified
        if ($providerId) {
            $query->where('provider_id', $providerId);
        }

        // Filter by date range
        $query->whereBetween('execution_date', [$startDate->toDateString(), $endDate->toDateString()]);

        // Get paginated results (10 per page)
        $tasks = $query->paginate(10)->withQueryString();

        // Process tasks to determine status
        foreach ($tasks as $task) {
            // Determine status based on requirements
            if ($task->status === 'ended' || ($task->start_time && $task->end_time)) {
                $task->display_status = __('message.completed');
            } elseif ($task->status === 'started' || ($task->start_time && !$task->end_time)) {
                $task->display_status = __('message.incomplete');
            } elseif ($task->status === 'cancelled') {
                $task->display_status = __('message.cancelled');
            } else {
                $task->display_status = __('message.not_executed');
            }
        }

        return $tasks;
    }

    /**
     * Format time to Arabic AM/PM format
     *
     * @param string $time
     * @return string
     */
    private function formatTimeToArabic($time)
    {
        if (!$time) {
            return 'لم يتم';
        }

        try {
            // Parse the time
            $carbonTime = Carbon::parse($time);
            $hour = $carbonTime->hour;
            $minute = $carbonTime->minute;

            // Format hour for 12-hour format
            $displayHour = $hour;
            if ($hour == 0) {
                $displayHour = 12;
                $period = 'صباحاً';
            } elseif ($hour < 12) {
                $period = 'صباحاً';
            } elseif ($hour == 12) {
                $period = 'مساءً';
            } else {
                $displayHour = $hour - 12;
                $period = 'مساءً';
            }

            // Format the time string
            $timeString = sprintf('%d:%02d %s', $displayHour, $minute, $period);

            return $timeString;
        } catch (\Exception $e) {
            return 'لم يتم';
        }
    }
}

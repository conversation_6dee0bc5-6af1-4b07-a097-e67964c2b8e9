<?php

namespace App\Http\Resources\api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\App;

class CustomerFinancialCloseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Set application locale based on lang header
        $locale = $request->header('lang') ?? 'en';
        App::setLocale($locale);

        return [
            'customer_id' => $this->customer_id,
            'customer_name' => $this->customer_name,
            'month' => $this->month,
            'year' => $this->year,
            'total_tasks' => $this->total_tasks,
            'total_hours' => round($this->total_hours, 2),
            'total_amount' => round($this->total_amount, 2),
            'total_transportation' => round($this->total_transportation, 2),
            'grand_total' => round($this->grand_total, 2),
            'fixed_amount_tasks' => $this->fixed_amount_tasks,
            'hourly_tasks' => $this->hourly_tasks,
            'closed_tasks' => $this->closed_tasks,
            'closure_date' => $this->closure_date,
            'payer_name' => $this->payer_name,
            'payment_notes' => $this->payment_notes,
            'message' => __('message.customer_financial_close_success'),
        ];
    }
}

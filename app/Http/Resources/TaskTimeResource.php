<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TaskTimeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $currentLanguage = $request->header('lang') ?? 'en';

        // Determine status based on start_time and end_time
        if (!$this->start_time && !$this->end_time) {
            $displayStatus = 'لم يتم التنفيذ'; // Not executed
            $status = 'pending';
        } elseif ($this->start_time && !$this->end_time) {
            $displayStatus = 'غير مكتمل'; // Incomplete
            $status = 'incomplete';
        } elseif ($this->start_time && $this->end_time) {
            $displayStatus = 'مكتملة'; // Complete
            $status = 'completed';
        } else {
            $displayStatus = 'لم يتم التنفيذ'; // Default: Not executed
            $status = 'pending';
        }

        // If status is explicitly set to 'cancelled', override the above
        if ($this->status === 'cancelled') {
            $displayStatus = 'تم الالغاء'; // Cancelled
            $status = 'cancelled';
        }

        // Simplified response for mobile
        return [
            'id' => $this->id,
            'job_application_id' => $this->job_application_id,
            'd_id' => $this->d_id,
            'execution_date' => $this->execution_date,
            'time' => $this->start_time ? date('g:i A', strtotime($this->start_time)) : null,
            'end_time' => $this->end_time ? date('g:i A', strtotime($this->end_time)) : null,
            'status_code' => $status,
            'status' => $displayStatus,
            'service_id' => $this->service_id,
            'location_id' => $this->location_id,
            'calculation_type' => $this->calculation_type,
            'fixed_amount' => $this->fixed_amount,
            'provider_payment_method' => $this->provider_payment_method,
            'provider_hourly_cost' => $this->provider_hourly_cost,
            'provider_fixed_cost' => $this->provider_fixed_cost,
            'system_hourly_cost' => $this->system_hourly_cost,
            'include_transportation' => $this->include_transportation,
            'system_transportation_cost' => $this->system_transportation_cost,
            'provider_transportation_cost' => $this->provider_transportation_cost,
            'user_id' => $this->user_id,
            'provider_id' => $this->provider_id,
        ];
    }
}

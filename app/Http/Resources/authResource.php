<?php

namespace App\Http\Resources;

use App\Models\City;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class authResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $currentLanguage = $request->header('lang') ?? 'en';
        $response['id'] = $this->id;
        $response['name'] = $this->name;
        $response['email'] = $this->email;
        $response['phone_code'] = $this->phone_code;
        $response['phone'] = $this->phone;
        $response['type'] = $this->type;
        $response['address'] = $this->address;
        $response['district'] = $this->city_id != null ? DistrictResource::make(City::find($this->city_id)->district) : null;
        $response['city'] = $this->city_id != null ? CityResource::make($this->city) : null;
        $response['verified'] = $this->is_verified;
        $response['bloked'] = $this->is_bloked;
        $response['otp'] = $this->otp;
        $response['image'] = Storage::disk('public')->url($this->image);
        $response['gender'] = $this->gender;
        $response['birth_date'] = $this->birth_date;
        $response['device_token'] = $this->device_token;
        $response['access_token'] = $this->access_token;
        $response['provider'] = $this->providerInfo ? ProviderResource::make($this->providerInfo) : null;

        return $response;
    }
}

<?php

namespace App\Http\Resources;

use App\Trait\NotificationTrait;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    use NotificationTrait;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // return [
        //     'id' => $this->id,
        //     'title' => $this->notification_title($this->data),
        //     'user_type' => $this->data['user_type'],
        //     'data' => $this->notification_details($this->data),
        //     'sent_at' => $this->created_at->format('Y-m-d').' at '.$this->created_at->format('h:i A'),
        //     'is_read' => ($this->read_at == null) ? false : true,
        // ];
        return [
            'id' => $this->id,
            'type' => $this->type,
            'data' => $this->data,
            'read_at' => $this->read_at,
            'sent_at' => $this->created_at->format('Y-m-d').' at '.$this->created_at->format('h:i A'),
            'is_read' => ($this->read_at == null) ? false : true,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

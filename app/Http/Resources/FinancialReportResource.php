<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FinancialReportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $currentLanguage = $request->header('lang') ?? 'en';
        $user = auth()->user();

        // Determine if current user is customer or provider
        $isCustomer = $user->type === 'user';
        $isProvider = in_array($user->type, ['provider', 'company']);

        // Get the appropriate person name based on user type
        $personName = '';
        if ($isCustomer) {
            // If user is customer, show provider name
            $personName = $this->provider ? $this->provider->name : '';
        } else {
            // If user is provider, show customer name
            $personName = $this->user ? $this->user->name : '';
        }

        // Get service name with translation
        $serviceName = '';
        if ($this->jobApplication && $this->jobApplication->service) {
            $serviceName = $this->jobApplication->service->getTranslation('name', $currentLanguage);
        }

        // Get location name (no translation needed for UserLocation)
        $locationName = '';
        if ($this->jobApplication && $this->jobApplication->location) {
            $locationName = $this->jobApplication->location->name;
        }

        // Format times to AM/PM format
        $startTime = $this->start_time ? date('g:i A', strtotime($this->start_time)) : null;
        $endTime = $this->end_time ? date('g:i A', strtotime($this->end_time)) : null;

        // Determine payment date based on financial closure status
        $paymentDate = null;
        $isFinanciallyClosed = $isCustomer ?
            $this->is_financially_closed_by_user :
            $this->is_financially_closed_by_provider;

        if ($isFinanciallyClosed) {
            // If financially closed, use updated_at as payment date
            $paymentDate = $this->updated_at ? $this->updated_at->format('Y-m-d') : null;
        }

        // Get calculation type based on user type
        $calculationType = '';
        $customerCalculationType = $this->calculation_type ?? 'fixed';
        $providerCalculationType = $this->provider_payment_method ?? 'fixed';

        if ($isCustomer) {
            $calculationType = $customerCalculationType;
        } else {
            $calculationType = $providerCalculationType;
        }

        // Prepare task type information
        $taskTypeInfo = [
            'customer_type' => $customerCalculationType,
            'provider_type' => $providerCalculationType,
        ];

        // Calculate amount based on calculation type and user type
        $amount = 0;
        $rate = 0;
        $hours = 0;
        $transportationCost = 0;
        $showTransportation = false;
        $schedules = [];

        // Check if this task has grouped schedules (for fixed amount tasks)
        if (isset($this->schedules) && is_array($this->schedules)) {
            // This is a grouped fixed amount task
            $totalMinutes = 0;
            foreach ($this->schedules as $schedule) {
                $totalMinutes += $schedule['duration_minutes'] ?? 0;

                // Format schedule times
                $schedules[] = [
                    'execution_date' => $schedule['execution_date'],
                    'start_time' => $schedule['start_time'] ? date('g:i A', strtotime($schedule['start_time'])) : null,
                    'end_time' => $schedule['end_time'] ? date('g:i A', strtotime($schedule['end_time'])) : null,
                    'duration_minutes' => $schedule['duration_minutes'],
                    'status' => $schedule['status'],
                    'notes' => $schedule['notes'],
                    'customer_type_label' => $customerCalculationType === 'hourly' ? __('message.hourly') : __('message.fixed_amount'),
                    'provider_type_label' => $providerCalculationType === 'hourly' ? __('message.hourly') : __('message.fixed_amount'),
                ];
            }
            $hours = round($totalMinutes / 60, 2);
        } else {
            // Individual task
            if ($this->duration_minutes > 0) {
                $hours = round($this->duration_minutes / 60, 2);
            }
        }

        // Handle specific case: Customer fixed + Provider hourly ONLY
        $hoursInfo = null;
        if ($customerCalculationType === 'fixed' && $providerCalculationType === 'hourly') {
            $hoursInfo = $this->calculateHoursInfo($request);
        }

        if ($isCustomer) {
            // Customer calculations
            if ($this->calculation_type === 'hourly') {
                // Hourly: show rate and calculated amount for each task
                $rate = $this->system_hourly_cost ?? 0;
                $amount = $hours * $rate;
                // Transportation shown but note: calculated once per job_application_id in summary
                $transportationCost = $this->system_transportation_cost ?? 0;
                $showTransportation = true;
            } else {
                // Fixed amount: show fixed amount and transportation (but calculated once per job_application_id per month)
                $amount = $this->fixed_amount ?? 0;
                $transportationCost = $this->system_transportation_cost ?? 0;
                $showTransportation = true;
            }
        } else {
            // Provider calculations
            if ($this->provider_payment_method === 'hourly') {
                // Hourly: show rate and calculated amount for each task
                $rate = $this->provider_hourly_cost ?? 0;
                $amount = $hours * $rate;
                // Transportation shown but note: calculated once per job_application_id in summary
                $transportationCost = $this->provider_transportation_cost ?? 0;
                $showTransportation = true;
            } else {
                // Fixed amount: show fixed amount and transportation (but calculated once per job_application_id per month)
                $amount = $this->provider_fixed_cost ?? 0;
                $transportationCost = $this->provider_transportation_cost ?? 0;
                $showTransportation = true;
            }
        }

        $response = [
            'id' => $this->id,
            'job_application_id' => $this->job_application_id,
            'person_name' => $personName,
            'service_name' => $serviceName,
            'location_name' => $locationName,
            'calculation_type' => $calculationType,
            'provider_payment_method' => $this->provider_payment_method,
            'rate' => $rate,
            'amount' => $amount,
            'transportation_cost' => $transportationCost,
            'total_amount' => $amount + $transportationCost,
            'is_financially_closed' => $isFinanciallyClosed,
            'payment_date' => $paymentDate,
            'payment_status' => $isFinanciallyClosed ? __('message.paid') : __('message.unpaid'),
            'schedules' => $schedules, // For grouped fixed amount tasks
            'is_grouped' => !empty($schedules), // Indicates if this is a grouped task
            'task_type_info' => $taskTypeInfo, // Customer and provider calculation types
            'hours_info' => $hoursInfo, // Hours information for mixed case
            // Additional info for calculation rules
            'calculation_note' => $calculationType === 'fixed' ?
                __('message.fixed_amount_calculation_note') :
                __('message.hourly_calculation_note'),
        ];

        // Add individual task details only for non-grouped tasks
        if (empty($schedules)) {
            $response['execution_date'] = $this->execution_date;
            $response['start_time'] = $startTime;
            $response['end_time'] = $endTime;
            $response['hours'] = $hours;
            $response['status'] = $this->status;
            $response['notes'] = $this->notes;
        }

        return $response;
    }

    /**
     * Calculate hours information ONLY for the specific case:
     * - Customer calculation type: Fixed Amount
     * - Provider calculation type: Hourly
     * This helps track provider's hourly work against total allocated hours for the fixed amount job.
     */
    private function calculateHoursInfo($request)
    {
        if (!$this->jobApplication) {
            return null;
        }

        $month = $this->report_month ?? $request->input('month');
        $year = $this->report_year ?? $request->input('year');

        // Get total allowed hours from job application
        $totalAllowedHours = $this->jobApplication->total_hours ?? 0;

        // Calculate worked hours in the current month for this job application
        $totalMinutes = \App\Models\Task::where('job_application_id', $this->job_application_id)
            ->whereMonth('execution_date', $month)
            ->whereYear('execution_date', $year)
            ->where('status', '!=', 'cancelled')
            ->sum('duration_minutes');

        $workedHours = round($totalMinutes / 60, 2); // Convert minutes to hours with precision
        $remainingHours = $totalAllowedHours - $workedHours;
        $percentage = $totalAllowedHours > 0 ? round(($workedHours / $totalAllowedHours) * 100, 2) : 0;

        // Determine status
        $status = 'normal';
        $exceededHours = 0;

        if ($totalAllowedHours > 0) {
            if ($workedHours > $totalAllowedHours) {
                $status = 'exceeded';
                $exceededHours = round($workedHours - $totalAllowedHours, 2);
            } elseif ($workedHours >= $totalAllowedHours) {
                $status = 'completed';
            }
        }

        $remainingHours = $totalAllowedHours - $workedHours;

        return [
            'total_allowed_hours' => round($totalAllowedHours, 2),
            'worked_hours_this_month' => $workedHours,
            'remaining_hours' => round($remainingHours, 2),
            'status' => $status, // normal, completed, exceeded
            'exceeded_hours' => $exceededHours, // مقدار التجاوز
        ];
    }
}

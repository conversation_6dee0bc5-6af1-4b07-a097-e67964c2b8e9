<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CouponResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    /**
     * @var int
     */
    private $usageCount;

    /**
     * Create a new resource instance.
     *
     * @param  mixed  $resource
     * @param  int  $usageCount
     * @return void
     */
    public function __construct($resource, $usageCount = 0)
    {
        parent::__construct($resource);
        $this->usageCount = $usageCount;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // The locale is already set in the controller, no need to set it again here

        return [
            'id' => $this->id,
            'code' => $this->code,
            'discount_value' => $this->discount_value,
            'discount_type' => $this->discount_type,
            'usage_count' => $this->usageCount,
            'usage_limit' => $this->usage_limit,
            'start_date' => $this->start_date->format('Y-m-d'),
            'end_date' => $this->end_date->format('Y-m-d'),
        ];
    }
}

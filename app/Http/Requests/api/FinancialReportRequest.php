<?php

namespace App\Http\Requests\api;

use Illuminate\Foundation\Http\FormRequest;

class FinancialReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'month' => 'required|integer|min:1|max:12',
            'year' => 'required|integer|min:2000|max:2040',
            'showpayment' => 'nullable|boolean',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'month.required' => __('message.month is required'),
            'month.integer' => __('message.month must be a valid number'),
            'month.min' => __('message.month must be between 1 and 12'),
            'month.max' => __('message.month must be between 1 and 12'),
            'year.required' => __('message.year is required'),
            'year.integer' => __('message.year must be a valid number'),
            'year.min' => __('message.year must be at least 2000'),
            'year.max' => __('message.year must be at most 2040'),
        ];
    }
}

<?php

namespace App\Http\Requests\api;

use Illuminate\Foundation\Http\FormRequest;

class CustomerFinancialCloseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'month' => 'required|integer|min:1|max:12',
            'year' => 'required|integer|min:2000|max:2040',
            'payer_name' => 'nullable|string|max:255',
            'payment_notes' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'month.required' => __('message.month_required'),
            'month.integer' => __('message.month_must_be_integer'),
            'month.min' => __('message.month_min_value'),
            'month.max' => __('message.month_max_value'),
            'year.required' => __('message.year_required'),
            'year.integer' => __('message.year_must_be_integer'),
            'year.min' => __('message.year_min_value'),
            'year.max' => __('message.year_max_value'),
            'payer_name.string' => __('message.payer_name_must_be_string'),
            'payer_name.max' => __('message.payer_name_max_length'),
            'payment_notes.string' => __('message.payment_notes_must_be_string'),
            'payment_notes.max' => __('message.payment_notes_max_length'),
        ];
    }
}

<?php

namespace App\Http\Requests\api\Task;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class UpdateTaskTimeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'job_application_id' => 'required|exists:job_applications,id',
            'time' => 'required|date_format:g:i A',
            'end_time' => 'nullable|date_format:g:i A',
            'd_id' => 'required|integer',
            'execution_date' => 'required|date_format:Y-m-d|after_or_equal:today',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate d_id matches the job application schedule
            $jobApplicationId = $this->input('job_application_id');
            $dId = $this->input('d_id');
            $executionDate = $this->input('execution_date');

            if ($jobApplicationId && $dId && $executionDate) {
                $jobApplication = \App\Models\JobApplication::find($jobApplicationId);

                if ($jobApplication) {
                    // Check if d_id exists in job application schedule
                    $schedule = $jobApplication->schedule ?? [];
                    $validDIds = [];

                    foreach ($schedule as $daySchedule) {
                        if (isset($daySchedule['hours']) && is_array($daySchedule['hours'])) {
                            foreach ($daySchedule['hours'] as $hour) {
                                if (isset($hour['d_id'])) {
                                    $validDIds[] = $hour['d_id'];
                                }
                            }
                        }
                    }

                    if (!in_array($dId, $validDIds)) {
                        $lang = $this->header('lang', 'en');
                        $validator->errors()->add('d_id', __('message.invalid_d_id_for_job_application', [], $lang));
                    }

                    // Check if execution_date matches the day of week for d_id
                    $executionDayOfWeek = strtolower(\Carbon\Carbon::parse($executionDate)->format('l'));
                    $validDay = false;

                    foreach ($schedule as $daySchedule) {
                        if (isset($daySchedule['day']) && isset($daySchedule['hours'])) {
                            $scheduleDayOfWeek = strtolower($daySchedule['day']);
                            if ($scheduleDayOfWeek === $executionDayOfWeek) {
                                foreach ($daySchedule['hours'] as $hour) {
                                    if (isset($hour['d_id']) && $hour['d_id'] == $dId) {
                                        $validDay = true;
                                        break 2;
                                    }
                                }
                            }
                        }
                    }

                    if (!$validDay) {
                        $lang = $this->header('lang', 'en');
                        $validator->errors()->add('execution_date', __('message.execution_date_day_mismatch', [], $lang));
                    }
                }
            }

            // No additional validation needed since 'time' is required
        });
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        $lang = $this->header('lang', 'en');

        return [
            'job_application_id.required' => __('message.job_application_id_required', [], $lang),
            'job_application_id.exists' => __('message.job_application_id_exists', [], $lang),
            'time.required' => __('message.time_required', [], $lang),
            'time.date_format' => __('message.invalid_time_format', [], $lang),
            'end_time.date_format' => __('message.invalid_time_format', [], $lang),
            'd_id.required' => __('message.d_id_required', [], $lang),
            'd_id.integer' => __('message.d_id_integer', [], $lang),
            'execution_date.required' => __('message.execution_date_required', [], $lang),
            'execution_date.date_format' => __('message.execution_date_format', [], $lang),
            'execution_date.after_or_equal' => __('message.execution_date_past_error', [], $lang),
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function failedValidation(Validator $validator)
    {
        $response = new JsonResponse([
            'success' => false,
            'message' => $validator->errors()->first(),
            'errors' => $validator->errors(),
        ], 422);

        throw new ValidationException($validator, $response);
    }
}

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProviderFinancialReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // Only authenticated users with proper permissions can access this
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'provider_id' => 'nullable|exists:users,id',
            'month' => 'nullable|integer|min:1|max:12',
            'year' => 'nullable|integer|min:2000|max:2040',
            'calculation_type' => 'nullable|in:all,hourly,fixed',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'provider_id.exists' => __('message.provider not found'),
            'month.integer' => __('message.month must be a valid number'),
            'month.min' => __('message.month must be between 1 and 12'),
            'month.max' => __('message.month must be between 1 and 12'),
            'year.integer' => __('message.year must be a valid number'),
            'year.min' => __('message.year must be at least 2000'),
            'year.max' => __('message.year must be at most 2040'),
        ];
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class SyncLanguageFiles extends Command
{
    protected $signature = 'language:sync {--lang=all}';
    protected $description = 'Sync language keys between PHP and JSON language files';

    protected $supportedLanguages = ['ar', 'en', 'he'];

    public function handle()
    {
        $lang = $this->option('lang');
        
        if ($lang === 'all') {
            foreach ($this->supportedLanguages as $language) {
                $this->syncLanguage($language);
            }
        } else {
            $this->syncLanguage($lang);
        }
        
        $this->info('Language synchronization completed.');
    }
    
    protected function syncLanguage($lang)
    {
        $this->info("Syncing language: {$lang}");
        
        // Path to PHP language file
        $phpFilePath = resource_path("lang/{$lang}/response.php");
        
        // Path to JSON language file
        $jsonFilePath = storage_path("app/public/languages/{$lang}.json");
        
        // Check if PHP file exists
        if (!File::exists($phpFilePath)) {
            $this->warn("PHP language file not found: {$phpFilePath}");
            return;
        }
        
        // Create JSON file if it doesn't exist
        if (!File::exists($jsonFilePath)) {
            File::put($jsonFilePath, '{}');
            $this->info("Created new JSON language file: {$jsonFilePath}");
        }
        
        // Load PHP language file
        $phpData = require $phpFilePath;
        
        // Load JSON language file
        $jsonData = json_decode(File::get($jsonFilePath), true) ?: [];
        
        // Merge data from PHP to JSON (without overwriting existing values)
        $updated = false;
        foreach ($phpData as $key => $value) {
            if (!isset($jsonData[$key])) {
                $jsonData[$key] = $value;
                $updated = true;
            }
        }
        
        // Save updated JSON file if changes were made
        if ($updated) {
            File::put($jsonFilePath, json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            $this->info("Updated JSON language file: {$jsonFilePath}");
        } else {
            $this->info("No new keys to add to JSON file");
        }
        
        // Now check for keys in JSON that are not in PHP
        $missingInPhp = [];
        foreach ($jsonData as $key => $value) {
            if (!isset($phpData[$key])) {
                $missingInPhp[$key] = $value;
            }
        }
        
        if (!empty($missingInPhp)) {
            $this->warn("Found " . count($missingInPhp) . " keys in JSON file that are not in PHP file");
            $this->warn("Consider adding these keys to the PHP file: " . implode(', ', array_keys($missingInPhp)));
        }
    }
}
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MobileJobApplication;
use App\Models\UserLocation;

class UpdateMobileJobApplicationsLocationId extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-mobile-job-applications-location-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update location_id in mobile_job_applications based on existing location data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $applications = MobileJobApplication::whereNull('location_id')->get();
        $this->info("Found {$applications->count()} applications without location_id");

        $updated = 0;

        foreach ($applications as $application) {
            // Find a matching location for this user
            $location = UserLocation::where('user_id', $application->user_id)
                ->where('latitude', $application->latitude)
                ->where('longitude', $application->longitude)
                ->first();

            if (!$location) {
                // If no exact match, create a new location
                $location = new UserLocation();
                $location->user_id = $application->user_id;
                $location->name = $application->location_name;
                $location->latitude = $application->latitude;
                $location->longitude = $application->longitude;
                $location->save();
                
                $this->info("Created new location ID {$location->id} for application ID {$application->id}");
            } else {
                $this->info("Found existing location ID {$location->id} for application ID {$application->id}");
            }

            // Update the application with the location_id
            $application->location_id = $location->id;
            $application->save();
            
            $updated++;
        }

        $this->info("Updated {$updated} applications with location_id");
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\JobApplication;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CopyRecurringJobApplications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'job-applications:copy-recurring';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Copy recurring job applications to the next month';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to copy recurring job applications...');
        
        // Get all active recurring job applications
        $recurringApplications = JobApplication::where('is_recurring', true)
            ->where('is_active', true)
            ->where(function($query) {
                $query->whereNull('end_date')
                      ->orWhere('end_date', '>=', Carbon::now());
            })
            ->get();
            
        $this->info("Found {$recurringApplications->count()} recurring job applications");
        
        $copied = 0;
        
        foreach ($recurringApplications as $application) {
            try {
                $newApplication = $application->createNextMonthCopy();
                
                if ($newApplication) {
                    $copied++;
                    $this->info("Copied job application #{$application->id} to #{$newApplication->id}");
                }
            } catch (\Exception $e) {
                $this->error("Error copying job application #{$application->id}: {$e->getMessage()}");
                Log::error("Error copying job application #{$application->id}: {$e->getMessage()}");
                Log::error($e->getTraceAsString());
            }
        }
        
        $this->info("Successfully copied {$copied} job applications");
        
        return 0;
    }
}

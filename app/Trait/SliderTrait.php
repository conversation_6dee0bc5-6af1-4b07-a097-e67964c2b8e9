<?php

namespace App\Trait;

use App\Models\User;

trait SliderTrait
{
    public function clickable_types()
    {
        $clickable_types = array(
            '' => 'None', 'external_link' => 'External Link',
            'provider' => 'Provider', 'company' => 'Company'
        );
        return $clickable_types;
    }

    public function clickable_values($type, $hidden_clickval)
    {
        if ($type == 'external_link') {
            return view('pages.sliders.partials.external_link', compact(['hidden_clickval']));
        } else if ($type == 'provider') {
            $providers = User::where('type', 'provider')->with(['providerInfo'])->get();
            return view('pages.sliders.partials.provider', compact(['hidden_clickval', 'providers']));
        } else if ($type == 'company') {
            $companies = User::where('type', 'company')->with(['providerInfo'])->get();
            return view('pages.sliders.partials.company', compact(['hidden_clickval', 'companies']));
        } else {
            return NULL;
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentTransaction extends Model
{
    use HasFactory;
    protected $fillable = ['invoice_file', 'order_id', 'job_id', 'user_id', 'transaction_for', 'amount', 'confirmed', 'invoice_logid'];

    /**
     * Get the order that owns the PaymentTransaction.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id')->withTrashed();
    }

    // public function job(): BelongsTo
    // {
    //     return $this->belongsTo(JobApplication::class, 'job_id')->withTrashed();
    // }
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class, 'task_id');
    }
}

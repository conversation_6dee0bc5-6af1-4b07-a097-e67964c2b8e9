<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Task extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'provider_id',
        'job_application_id',
        'd_id',
        'execution_date',
        'start_time',
        'end_time',
        'duration_minutes',
        'calculation_type',
        'fixed_amount',
        'provider_payment_method',
        'provider_hourly_cost',
        'provider_fixed_cost',
        'system_hourly_cost',
        'include_transportation',
        'system_transportation_cost',
        'provider_transportation_cost',
        'status',
        'mobile_payment_enabled',
        'notes',
        'is_financially_closed_by_user',
        'is_financially_closed_by_provider',
        'financially_closed_by_user',
        'financially_closed_by_provider',
    ];

    public function jobApplication()
    {
        return $this->belongsTo(JobApplication::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function provider()
    {
        return $this->belongsTo(User::class, 'provider_id');
    }

    /**
     * Get the provider info associated with this task
     */
    public function providerInfo()
    {
        return $this->hasOneThrough(
            ProviderInfo::class,
            User::class,
            'id', // Foreign key on users table
            'user_id', // Foreign key on provider_infos table
            'provider_id', // Local key on tasks table
            'id' // Local key on users table
        );
    }
    public function transactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class, 'task_id');
    }
}

<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Jetstream\HasProfilePhoto;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone_code',
        'phone',
        'password',
        'address',
        'type',
        'city_id',
        'district_id',
        'gender',
        'birth_date',
        'is_verified',
        'is_blocked',
        'otp',
        'otp_expires_at',
        'remember_token',
        'access_token',
        'device_token',
        'image',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the URL to the user's profile photo.
     *
     * @return \Illuminate\Database\Eloquent\Casts\Attribute
     */
    public function profilePhotoUrl(): Attribute
    {
        return Attribute::get(function (): string {
            $profileService = app(\App\Services\ProfileService::class);
            return $profileService->getProfilePhotoUrl($this);
        });
    }

    /**
     * Update the user's profile photo.
     *
     * @param  \Illuminate\Http\UploadedFile  $photo
     * @param  string  $storagePath
     * @return void
     */
    public function updateProfilePhoto($photo, $storagePath = 'profile-photos')
    {
        $profileService = app(\App\Services\ProfileService::class);
        $profileService->updateProfilePhoto($this, $photo, $storagePath);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Route notifications for the SMS channel.
     *
     * @return string
     */
    public function routeNotificationForSms()
    {
        return $this->phone;
    }

    public function generateOtp()
    {
        $generator = '0123456789';
        $result = '';

        for ($i = 0; $i < 4; ++$i) {
            $result .= substr($generator, rand() % strlen($generator), 1);
        }

        if (User::where('otp', $result)->count() > 0) {
            $result = $this->generateOtp();
        }

        $this->otp = $result;
        $this->otp_expires_at = now()->addMinutes(10);
        $this->is_verified = 0;
        $this->save();

        return $result;
    }

    /**
     * Verify the OTP for the user.
     *
     * @param string $otp
     *
     * @return bool
     */
    public function verifyOtp($otp)
    {
        if ($this->otp !== $otp) {
            return false;
        }

        if ($this->otp_expires_at < now()) {
            return false;
        }

        $this->is_verified = 1;
        $this->save();

        return true;
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'city_id');
    }

    public function district(): BelongsTo
    {
        return $this->belongsTo(District::class, 'district_id');
    }

    public function providerInfo()
    {
        return $this->hasOne(ProviderInfo::class, 'user_id');
    }

    public function scopeEmployeeProviders($query)
    {
        return $query->whereIn('type', ['provider', 'company'])
            ->whereHas('providerInfo', function($query) {
                $query->where('is_employee', true);
            });
    }

    /**
     * Get all of the user orders for the User.
     */
    public function userOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'user_id');
    }

    /**
     * The services that belong to the User.
     */
    public function providerServices(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'provider_services', 'provider_id', 'service_id')
            ->where('services.is_active', 1);
    }

    /**
     * The services that belong to the User.
     */
    public function providerService(): HasMany
    {
        return $this->hasMany(ProviderService::class, 'provider_id');
    }

    /**
     * Get all of the provider orders for the User.
     */
    public function providerOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'user_id');
    }

    /**
     * Get all of the provider orders schedule for the User.
     */
    public function providerSchedule(): HasMany
    {
        return $this->hasMany(OrderSchedule::class, 'provider_id');
    }

    /**
     * Get all of the holidays for the User.
     */
    public function providerHolidays(): HasMany
    {
        return $this->hasMany(ProviderHoliday::class, 'user_id')
            ->whereDate('ends_at', '>=', date('Y-m-d'))->orderBy('ends_at');
    }

    /**
     * Get all of the provider work zones schedule for the User.
     */
    // public function providerWorkZones(): BelongsToMany
    // {
    //     return $this->belongsToMany(city::class, 'provider_cities', 'user_id', 'city_id');
    // }
    public function providerWorkZones(): BelongsToMany
    {
        return $this->belongsToMany(City::class, 'provider_cities', 'user_id', 'city_id');
    }

    /**
     * Get all of the working_times for the User.
     */
    public function providerWorkTimes(): HasMany
    {
        return $this->hasMany(ProviderWorkTime::class, 'user_id');
    }

    /**
     * Get all provider skills for the User.
     */
    public function providerSkills(): HasMany
    {
        return $this->hasMany(ProviderSkill::class, 'user_id');
    }

    /**
     * The skills that belong to the User.
     */
    public function skills(): BelongsToMany
    {
        return $this->belongsToMany(Skill::class, 'provider_skills', 'user_id', 'skill_id');
    }

    /**
     * Get all of the discounts for the User.
     */
    public function discounts(): HasMany
    {
        return $this->hasMany(UserDiscount::class);
    }

    /**
     * The services that belong to the User.
     */
    public function favorites(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'favorites_providers', 'user_id', 'provider_id');
    }

    /**
     * Get all of the albums for the User.
     */
    public function albums(): HasMany
    {
        return $this->hasMany(Album::class, 'provider_id');
    }

    /**
     * Get all of the reviews for the Provider.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class, 'provider_id')->latest();
    }

    /**
     * Get all of the locations for the User.
     */
    public function locations(): HasMany
    {
        return $this->hasMany(UserLocation::class);
    }

}

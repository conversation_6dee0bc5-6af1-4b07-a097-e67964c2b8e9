<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JobApplicationContracts extends Model
{
    protected $fillable = [
        'job_application_id',
        'fixed_amount',
        'provider_calculation_type',
        'provider_fixed_cost',
        'provider_hourly_cost',
        'start_date',
        'month',
    ];

    public function jobApplication()
    {
        return $this->belongsTo(JobApplication::class);
    }
}

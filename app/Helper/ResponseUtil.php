<?php

namespace App\Helper;

class ResponseUtil
{
    /**
     * @param  string  $message
     * @param  mixed  $data
     * @return array
     */
    public static function makeResponse($message, $data)
    {
        return [
            'success' =>  true,
            'code'   => 1,
            'data'    => $data,
            'message' => $message,
        ];
    }

    /**
     * @param  string  $message
     * @param  array  $data
     * @return array
     */
    public static function makeError($message, array $data = [])
    {
        $res = [
            'success' =>  false,
            'code'   => -1,
            'message' => $message,
        ];

        if (! empty($data)) {
            $res['data'] = $data;
        }

        return $res;
    }
}

<?php

namespace App\Helpers;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\URL;

class LocaleHelper
{
    /**
     * Localiza una URL con el prefijo de idioma actual
     *
     * @param string $url
     * @return string
     */
    public static function localizeUrl($url)
    {
        $locale = App::getLocale();

        // Verificamos si la URL es una ruta completa o solo un path
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            $parsedUrl = parse_url($url);
            $path = $parsedUrl['path'] ?? '';

            // Si la URL ya tiene un prefijo de idioma, lo eliminamos
            $path = preg_replace('#^/(ar|en|he)/#', '/', $path);

            // Reconstruimos la URL con el prefijo de idioma actual
            $scheme = $parsedUrl['scheme'] ?? 'http';
            $host = $parsedUrl['host'] ?? '';
            $port = isset($parsedUrl['port']) ? ':' . $parsedUrl['port'] : '';
            $query = isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '';
            $fragment = isset($parsedUrl['fragment']) ? '#' . $parsedUrl['fragment'] : '';

            return "{$scheme}://{$host}{$port}/{$locale}{$path}{$query}{$fragment}";
        } else {
            // Si es solo un path, simplemente añadimos el prefijo de idioma
            // Si la URL ya tiene un prefijo de idioma, lo eliminamos
            $url = preg_replace('#^/(ar|en|he)/#', '/', $url);

            // Añadimos el prefijo de idioma actual
            return "/{$locale}{$url}";
        }
    }
}
